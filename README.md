# MyUse Frontend

A modern React Native mobile application built with Expo.

## Prerequisites

- Node.js (v16 or higher)
- Yarn
- Expo CLI (`npm install -g expo-cli`)
- iOS Simulator (for Mac) or Android Studio (for Android development)

## Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd myuse-frontend
```

2. Install dependencies:
```bash
yarn install
```

3. Set up environment variables:
```bash
cp .env.example .env
```
Edit the `.env` file with your configuration.

4. Start the development server:
```bash
yarn start
```

Then press:
- `i` to run on iOS simulator
- `a` to run on Android device/emulator
- `w` to run on web browser

## Available Scripts

- `yarn start` - Start Expo development server
- `yarn android` - Run on Android
- `yarn ios` - Run on iOS
- `yarn web` - Run on web browser
- `yarn test` - Run tests
- `yarn lint` - Run linting
- `yarn eject` - Eject from Expo managed workflow

## Contributing

1. Create a new branch
2. Make your changes
3. Submit a pull request

## License

This project is proprietary and confidential.
