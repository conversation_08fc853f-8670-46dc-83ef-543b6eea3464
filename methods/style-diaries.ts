import { useMutation } from "@tanstack/react-query"
import Meteor from "@meteorrn/core"


type StyleDiary = {
  styleDiaryId: string;
  items: any[];
  imageURL: string;
}

export const createStyleDiary = () => {
  return useMutation({
    mutationFn: async (data: StyleDiary) => {
      return new Promise((resolve, reject) => {
        Meteor.call('styleDiaries.update', {
          styleDiaryId: data.styleDiaryId,
          items: data.items,
          imageURL: data.imageURL,
        }, (err: any, res: any) => {
          if (err) {
            console.log('err', err);
            reject(err);
          }
          console.log('res', res);
          resolve(res);
        });
      });
    },
  });
};