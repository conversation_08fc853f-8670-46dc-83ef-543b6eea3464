import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Meteor from '@meteorrn/core';

type ItemResponse = {
  success: boolean;
  data: any;
};

// Get item by ID
export const useItemById = (id: string) => {
  return useQuery({
    queryKey: ['item', id],
    queryFn: () =>
      new Promise<ItemResponse>((resolve, reject) => {
        console.log(`Fetching item with ID: ${id}`);
        Meteor.call(
          'items-fetchById',
          { itemId: id },
          (err: any, res: ItemResponse) => {
            if (err) {
              console.error('Error fetching item:', err);
              reject(err);
              return;
            }
            console.log('Item data fetched successfully');
            resolve(res);
          }
        );
      }),
    select: (data) => {
      // Process the item data
      let itemData;
      if (data.data?.item && Array.isArray(data.data.item) && data.data.item.length > 0) {
        itemData = data.data.item[0];
        console.log('Item data processed successfully');
      } else if (data.data?.item && typeof data.data.item === 'object') {
        itemData = data.data.item;
        console.log('Item data processed successfully');
      } else {
        itemData = { _id: id, name: 'Item Details' };
        console.log('Using fallback item data');
      }

      // Format image URL if present
      if (itemData.imageUrl) {
        if (itemData.imageUrl.startsWith('http')) {
          // URL is already properly formatted
        } else if (itemData.imageUrl.startsWith('//')) {
          // Fix protocol-relative URL
          itemData.imageUrl = `https:${itemData.imageUrl}`;
        } else {
          // Add https:// prefix if needed
          itemData.imageUrl = `https://${itemData.imageUrl.replace(/^\/\//, '')}`;
        }
      }

      return itemData;
    },
    // Add caching configuration
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: true, // Refetch when component mounts
  });
};

// Update item wear count
export const useWearItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (itemId: string) =>
      new Promise<ItemResponse>((resolve, reject) => {
        Meteor.call(
          'items-wearIt',
          { itemId },
          (err: any, res: ItemResponse) => {
            if (err) {
              console.error('Error updating wear count:', err);
              reject(err);
              return;
            }
            console.log('Wear count updated successfully');
            resolve(res);
          }
        );
      }),
    onSuccess: (_, itemId) => {
      // Invalidate the item query to refresh the data
      queryClient.invalidateQueries({ queryKey: ['item', itemId] });
    }
  });
};
