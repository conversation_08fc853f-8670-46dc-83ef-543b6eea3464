import { useQueryClient } from '@tanstack/react-query';
import Meteor from '@meteorrn/core';
import { useMutation } from '@tanstack/react-query';

/**
 * Hook to invalidate caches when items are modified in packing lists
 * This ensures that the Closet view stays in sync with Packing List changes
 */
export const useSyncPackingListWithCloset = () => {
  const queryClient = useQueryClient();

  // Invalidate the clothes cache to force a refresh
  const invalidateClosetCache = () => {
    console.log('Invalidating clothes cache to sync with packing list changes');
    queryClient.invalidateQueries({ queryKey: ['clothes'] });
  };

  // Force refresh all relevant caches
  const forceRefreshAllCaches = () => {
    console.log('Force refreshing all caches');
    // Invalidate clothes cache
    queryClient.invalidateQueries({ queryKey: ['clothes'] });
    // Invalidate packing list cache
    queryClient.invalidateQueries({ queryKey: ['packingList'] });
    // Invalidate specific item caches
    queryClient.invalidateQueries({ queryKey: ['item'] });
  };

  // Update item to remove it from packing lists
  const removeItemFromPackingListMutation = useMutation({
    mutationFn: (itemId: string) => {
      if (!itemId) {
        return Promise.reject(new Error('Invalid itemId'));
      }

      return new Promise((resolve, reject) => {
        try {
          // First, fetch the item to get its current data
          Meteor.call('items-fetchById', { itemId }, (fetchErr: any, fetchRes: any) => {
            if (fetchErr) {
              reject(fetchErr);
              return;
            }

            // Safely access the item data with proper null checks
            if (!fetchRes || !fetchRes.data) {
              reject(new Error('Invalid response from items-fetchById'));
              return;
            }

            // Get the item data with proper null checks
            const item = fetchRes.data?.item?.[0];
            if (!item) {
              reject(new Error('Item not found'));
              return;
            }

            // Create a safe update object with fallbacks for missing properties
            const updateData = {
              itemId,
              // Include any fields that need to be preserved with fallbacks
              name: item.name || 'Unknown Item',
              itemCategoryId: item.itemCategoryId || item.category?._id || 'unknown',
              color: item.color || '',
              brand: item.brand || '',
              // Clear any packing list associations
              packingLists: [] // This assumes the backend has a packingLists field
            };

            // Update the item to remove packing list associations
            Meteor.call('items-update', updateData, (updateErr: any, updateRes: any) => {
              if (updateErr) {
                reject(updateErr);
                return;
              }

              resolve(updateRes);
            });
          });
        } catch (error) {
          reject(error);
        }
      });
    },
    onSuccess: () => {
      // Force refresh all caches to ensure UI is updated
      forceRefreshAllCaches();
    },
    onError: (error) => {
      console.error('Error in removeItemFromPackingListMutation:', error);
      // Still refresh caches to ensure UI is consistent
      forceRefreshAllCaches();
    }
  });

  // Hard refresh method - use this as a last resort
  const hardRefresh = () => {
    console.log('Performing hard refresh of all data');
    // Clear all queries in the cache
    queryClient.clear();
    // Force refetch of critical data
    queryClient.refetchQueries({ queryKey: ['clothes'] });
    queryClient.refetchQueries({ queryKey: ['packingList'] });
  };

  return {
    invalidateClosetCache,
    removeItemFromPackingListMutation,
    forceRefreshAllCaches,
    hardRefresh
  };
};
