import { TripDetails } from '@/types/trip';
import Meteor from '@meteorrn/core';
import { useMutation, useQuery } from '@tanstack/react-query';

type TripResponse = {
  success: boolean;
  data: any;
};


export const createNewTrip = () =>
  useMutation({
    mutationFn: (trip: TripDetails) => {
      return new Promise<TripResponse>((resolve, reject) => {
        console.log('🚀 DEBUG: createNewTrip - Starting Meteor call');
        console.log('🚀 DEBUG: createNewTrip - Trip data:', trip);
        console.log('🚀 DEBUG: createNewTrip - User ID:', Meteor.userId());
        console.log('🚀 DEBUG: createNewTrip - Connection status:', Meteor.status().connected);
        console.log('🚀 DEBUG: createNewTrip - Timestamp:', new Date().toISOString());
        
        Meteor.call(
          'events-add',
          { ...trip },
          (err: any, res: TripResponse) => {
            console.log('📡 DEBUG: createNewTrip - Meteor call completed');
            console.log('📡 DEBUG: createNewTrip - Timestamp:', new Date().toISOString());
            
            if (err) {
              console.log('❌ DEBUG: createNewTrip - Error occurred:', err);
              console.log('❌ DEBUG: createNewTrip - Error details:', {
                error: err.error,
                reason: err.reason,
                details: err.details,
                message: err.message
              });
              reject(err);
              return;
            }
            
            console.log('✅ DEBUG: createNewTrip - Success response:', res);
            console.log('✅ DEBUG: createNewTrip - Response success:', res?.success);
            console.log('✅ DEBUG: createNewTrip - Response data:', res?.data);
            
            resolve(res);
            return;
          },
        );
      });
    },
    onMutate: (trip) => {
      console.log('🔄 DEBUG: createNewTrip - Mutation started');
      console.log('🔄 DEBUG: createNewTrip - Trip being created:', trip);
    },
    onSuccess: (data) => {
      console.log('🎉 DEBUG: createNewTrip - Mutation succeeded');
      console.log('🎉 DEBUG: createNewTrip - Success data:', data);
    },
    onError: (error) => {
      console.log('💥 DEBUG: createNewTrip - Mutation failed');
      console.log('💥 DEBUG: createNewTrip - Error:', error);
    },
  });

export const deleteTrip = () =>
  useMutation({
    mutationFn: (tripId: string) => {
      return new Promise<{ tripId: string }>((resolve, reject) => {
        Meteor.call(
          'events-delete',
          { eventId: tripId },
          (err: any, res: { tripId: string }) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const getTrips = () => {
  return useQuery({
    queryKey: ['trips'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call('events-fetchAll', {}, (err: any, res: TripResponse) => {
          if (err) {
            reject(err);
          }
          console.log(res, 'res');
          resolve(res);
        });
      }),
  });
};

export const getTripById = (id: string) => {
  return useQuery({
    queryKey: ['trip', id],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-fetchById',
          { eventId: id },
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
            }
            resolve(res);
          },
        );
      }),
  });
};

export const updatePackingList = () => {
  return useMutation({
    mutationFn: ({
      eventId,
      packingList,
    }: {
      eventId: string;
      packingList: any;
    }) =>
      new Promise<TripResponse>((resolve, reject) => {
        // Check if user is authenticated before making the call
        const userId = Meteor.userId();
        
        if (!userId) {
          const authError = new Error('Authentication required: User must be logged in to update packing list');
          console.error('updatePackingList failed: No authenticated user found');
          reject(authError);
          return;
        }

        // Validate required parameters
        if (!eventId || !eventId.trim()) {
          const validationError = new Error('Event ID is required');
          console.error('updatePackingList failed: Invalid eventId');
          reject(validationError);
          return;
        }

        if (!packingList) {
          const validationError = new Error('Packing list data is required');
          console.error('updatePackingList failed: Invalid packingList data');
          reject(validationError);
          return;
        }

        console.log(`Updating packing list for event ${eventId} by user ${userId}`);
        
        Meteor.call(
          'events-updatePackingList',
          { eventId, packingList },
          (err: any, res: TripResponse) => {
            if (err) {
              console.error('updatePackingList Meteor call failed:', err);
              
              // Check if error is due to authentication issues
              if (err.error === 'unauthorized' || err.error === 'not-authorized' || err.message?.includes('unauthorized')) {
                const authError = new Error('Authentication failed: User session may have expired');
                reject(authError);
                return;
              }
              
              // Check if error is due to user not found
              if (err.error === 'user-not-found' || err.message?.includes('user not found')) {
                const userError = new Error('User not found: Please sign in again');
                reject(userError);
                return;
              }
              
              reject(err);
              return;
            }
            
            console.log('updatePackingList successful:', res);
            resolve(res);
          },
        );
      }),
    onError: (error: Error) => {
      console.error('updatePackingList mutation error:', error);
      
      // Additional error handling can be added here
      if (error.message.includes('Authentication')) {
        console.error('Authentication error detected - user may need to sign in again');
      }
    },
    onSuccess: (data) => {
      console.log('updatePackingList mutation completed successfully');
    },
  });
};

export const getPackingList = (eventId: string) => {
  return useQuery({
    queryKey: ['packingList', eventId],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        if (!eventId) {
          resolve({ data: { packingList: [] } } as TripResponse); // Return empty packing list
          return;
        }

        Meteor.call(
          'events-fetchPackingListByEventId',
          { eventId },
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
              return;
            }

            // Ensure we have a valid response with proper structure
            if (!res) {
              resolve({ data: { packingList: [] } } as TripResponse);
              return;
            }

            if (!res.data) {
              res = { ...res, data: { packingList: [] } } as TripResponse;
            }

            if (!res.data.packingList) {
              res.data = { ...res.data, packingList: [] };
            }

            if (!Array.isArray(res.data.packingList)) {
              res.data.packingList = [];
            }

            resolve(res);
          },
        );
      }),
    // Add staleTime to reduce unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const getUpcomingTrips = () => {
  return useQuery({
    queryKey: ['upcomingTrips'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-fetchUpComingEvents',
          {},
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
            }
            console.log(res, 'res');
            resolve(res);
          },
        );
      }),
  });
};

export const getPastTrips = () => {
  return useQuery({
    queryKey: ['pastTrips'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call(
          'events-fetchPastEvents',
          {},
          (err: any, res: TripResponse) => {
            if (err) {
              reject(err);
            }
            console.log(res, 'res');
            resolve(res);
          },
        );
      }),
  });
};

export const getTemplate = () => {
  return useQuery({
    queryKey: ['template'],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call('packingListTemplates-fetchAll', {}, (err: any, res: TripResponse) => {
          if (err) {
            console.log(err, 'errsss');
            reject(err);
          }

          console.log(res, 'ressss');

          resolve(res.data);
        });
      }),
  });
};

//get styles diary
export const getStylesDiary = (eventId: string) => {
  return useQuery({
    queryKey: ['stylesDiary', eventId],
    queryFn: () =>
      new Promise<TripResponse>((resolve, reject) => {
        Meteor.call('styleDiaries.fetchByEventId', {
          eventId: eventId,
        }, (err: any, res: TripResponse) => {
          if (err) {
            reject(err);
          }
          resolve(res);
        });
      }),
  });
};
