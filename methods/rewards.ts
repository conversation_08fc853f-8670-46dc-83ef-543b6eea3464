import Meteor, { withTracker } from '@meteorrn/core';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

type Reward = {
  _id: string;
  createdAt: Date;
  description: string;
  name: string;
  points: number;
};

type EarnedReward = {
  _id: string;
  rewardCode: string;
  metadata?: any;
  seen: boolean;
  createdAt: Date;
  userId: string;
  points?: number;
};

type RewardsResponse = {
  success: boolean;
  data: {
    rewards: Reward[];
  };
};

type EarnedRewardsResponse = {
  success: boolean;
  data: {
    earnedRewards: EarnedReward[];
  };
};

type RewardsCompleteResponse = {
  success: boolean;
  data: any;
};

export const getRewards = () =>
  useQuery({
    queryKey: ['rewards'],
    queryFn: () => {
      return new Promise<RewardsResponse>((resolve, reject) => {
        Meteor.call(
          'rewards-fetchAll',
          {},
          (err: any, res: RewardsResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }

            resolve(res);
            return;
          },
        );
      });
    },
  });

// Simple hook to get unseen earned rewards
// Note: The actual subscription is handled by EarnedRewardsWatcher component
export const useUnseenEarnedRewards = () => {
  const [earnedRewards, setEarnedRewards] = useState<EarnedReward[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<any>(null);

  // This hook is currently a placeholder
  // The real subscription logic is in EarnedRewardsWatcher
  // In the future, this could be enhanced to work with withTracker

  return {
    data: earnedRewards,
    isLoading,
    error,
  };
};

// Keep the old function for backward compatibility but mark as deprecated
export const getUnseenEarnedRewards = () => {
  console.warn('getUnseenEarnedRewards is deprecated. Use useUnseenEarnedRewards hook instead.');

  return useQuery({
    queryKey: ['earnedRewards', 'unseen'],
    queryFn: () => {
      return new Promise<EarnedRewardsResponse>((resolve, reject) => {
        // This is a fallback - the proper way is to use the subscription
        console.log('Using fallback method call for earnedRewards-getUnseen');
        resolve({
          success: true,
          data: { earnedRewards: [] }
        });
      });
    },
    // Disable automatic refetching since we should use subscription
    refetchInterval: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const rewardsComplete = () =>
  useMutation({
    mutationFn: (reward: any) => {
      return new Promise<RewardsCompleteResponse>((resolve, reject) => {
        Meteor.call(
          'rewards-complete',
          { ...reward },
          (err: any, res: RewardsCompleteResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            resolve(res);
            return;
          },
        );
      });
    },
  });

// Mark earned reward as seen
export const markEarnedRewardAsSeen = () =>
  useMutation({
    mutationFn: (params: { _id: string }) => {
      return new Promise<any>((resolve, reject) => {
        Meteor.call(
          'earnedRewards-seen',
          params,
          (err: any, res: any) => {
            if (err) {
              console.log('Error marking earned reward as seen:', err);
              reject(err);
              return;
            }
            console.log('Earned reward marked as seen:', res);
            resolve(res);
            return;
          },
        );
      });
    },
  });
