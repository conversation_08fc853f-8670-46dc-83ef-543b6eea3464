import { useMutation, useQuery } from "@tanstack/react-query"
import Meteor from "@meteorrn/core"

type StyleQuizResponse = {
  success: boolean;
  data: any;
};

type StyleQuiz = {
  questionId?: string;
  selected?: boolean;
}

export const answerStyleQuiz = () => {
  return useMutation({
    mutationFn: async (data: StyleQuiz) => {
      return new Promise<StyleQuizResponse>((resolve, reject) => {
        Meteor.call('styleQuiz-answer',
          {
            questionId: data.questionId || '',
            selected: data.selected || false,
          },
          (err: any, res: any) => {
            if (err) {
              console.log('err', err);
              reject(err);
            }
            console.log('res', res);
            resolve(res);
          });
      });
    },
  });
};

export const fetchStyleQuiz = () => {
  return useQuery({
    queryKey: ['styleQuiz'],
    queryFn: async () => {
      return new Promise<StyleQuizResponse>((resolve, reject) => {
        Meteor.call('styleQuiz-fetch',
          {},
          (err: any, res: any) => {
            if (err) {
              console.log('err', err);
              reject(err);
            }
            resolve(res);
          });
      });
    },
  });
};