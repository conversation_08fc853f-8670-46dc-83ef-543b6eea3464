import Meteor from '@meteorrn/core';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Outfit API Methods
 *
 * Complete CRUD implementation:
 * - createOutfit() - Add new outfits
 * - updateOutfit() - Update existing outfits
 * - deleteOutfit() - Delete existing outfits
 * - getOutfits() - Fetch all outfits with filtering
 */

// Type definitions based on backend API schema
type OutfitLocation = {
  name: string;
  longitude: number;
  latitude: number;
};

type OutfitResponse = {
  success: boolean;
  message: string;
  data: {
    outfitId?: string;
    outfits?: any[];
    _id?: string;
  };
  error?: any;
};

type CreateOutfitParams = {
  name: string;
  location: OutfitLocation;
  eventDate?: Date;
  itemIds: string[];
  outfitCategoryId?: string;
  plannedDate?: Date;
};

type UpdateOutfitParams = {
  outfitId: string;
  name?: string;
  location?: OutfitLocation;
  eventDate?: Date;
  itemIds?: string[];
  outfitCategoryId?: string;
  plannedDate?: Date;
};

type FetchOutfitsParams = {
  categoryName?: 'Work Wear' | 'Casual Wear' | 'Evening Wear' | 'Formal Wear' | 'Resort / Beach Wear' | 'Winter Wear';
  skip?: number;
};

/**
 * Create a new outfit
 * Uses the outfits-add backend method
 */
export const createOutfit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: CreateOutfitParams) => {
      return new Promise<OutfitResponse>((resolve, reject) => {
        console.log('Creating outfit with params:', params);
        
        Meteor.call(
          'outfits-add',
          params,
          (err: any, res: OutfitResponse) => {
            if (err) {
              console.error('Error creating outfit:', err);
              reject(err);
              return;
            }
            
            console.log('Outfit created successfully:', res);
            resolve(res);
          }
        );
      });
    },
    onSuccess: (data) => {
      console.log('Outfit creation successful, invalidating queries');
      // Invalidate outfits queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['outfits'] });
      
      // Also invalidate any specific outfit queries
      if (data.data.outfitId) {
        queryClient.invalidateQueries({ queryKey: ['outfit', data.data.outfitId] });
      }
    },
    onError: (error) => {
      console.error('Outfit creation failed:', error);
    }
  });
};

/**
 * Update an existing outfit
 * Uses the outfits-update backend method
 */
export const updateOutfit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UpdateOutfitParams) => {
      return new Promise<OutfitResponse>((resolve, reject) => {
        console.log('Updating outfit with params:', params);

        Meteor.call(
          'outfits-update',
          params,
          (err: any, res: OutfitResponse) => {
            if (err) {
              console.error('Error updating outfit:', err);
              reject(err);
              return;
            }

            console.log('Outfit updated successfully:', res);
            resolve(res);
          }
        );
      });
    },
    onSuccess: (data, variables) => {
      console.log('Outfit update successful, invalidating queries');
      // Invalidate outfits queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['outfits'] });

      // Invalidate the specific outfit query
      queryClient.invalidateQueries({ queryKey: ['outfit', variables.outfitId] });
    },
    onError: (error) => {
      console.error('Outfit update failed:', error);
    }
  });
};

/**
 * Delete an outfit
 * Uses the outfits-delete backend method
 */
export const deleteOutfit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (outfitId: string) => {
      return new Promise<OutfitResponse>((resolve, reject) => {
        console.log('Deleting outfit with ID:', outfitId);
        
        Meteor.call(
          'outfits-delete',
          { outfitId },
          (err: any, res: OutfitResponse) => {
            if (err) {
              console.error('Error deleting outfit:', err);
              reject(err);
              return;
            }
            
            console.log('Outfit deleted successfully:', res);
            resolve(res);
          }
        );
      });
    },
    onSuccess: (data, outfitId) => {
      console.log('Outfit deletion successful, invalidating queries');
      // Invalidate outfits queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['outfits'] });
      
      // Remove the specific outfit from cache
      queryClient.removeQueries({ queryKey: ['outfit', outfitId] });
    },
    onError: (error) => {
      console.error('Outfit deletion failed:', error);
    }
  });
};

/**
 * Fetch all outfits with optional filtering
 * Uses the outfits-fetchAll backend method
 */
export const getOutfits = (params: FetchOutfitsParams = {}) => {
  return useQuery({
    queryKey: ['outfits', params.categoryName || 'all', params.skip || 0],
    queryFn: () => {
      return new Promise<OutfitResponse>((resolve, reject) => {
        console.log('Fetching outfits with params:', params);
        
        Meteor.call(
          'outfits-fetchAll',
          params,
          (err: any, res: OutfitResponse) => {
            if (err) {
              console.error('Error fetching outfits:', err);
              reject(err);
              return;
            }
            
            console.log('Outfits fetched successfully:', res);
            resolve(res);
          }
        );
      });
    },
    select: (data: OutfitResponse) => {
      // Process the response to match the expected format
      if (data.success && data.data.outfits) {
        // Format each outfit for the UI
        const formattedOutfits = data.data.outfits.map(outfit => formatOutfitForUI(outfit));

        return {
          items: formattedOutfits,
          success: data.success,
          message: data.message
        };
      }

      // Return empty array if no outfits or error
      return {
        items: [],
        success: false,
        message: data.message || 'No outfits found'
      };
    },
    // Cache configuration
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });
};

/**
 * Helper function to convert location string to coordinates
 * This is a placeholder - in a real app you'd use a geocoding service
 */
export const convertLocationToCoordinates = async (locationString: string): Promise<OutfitLocation> => {
  // For now, return default coordinates for London
  // In a real implementation, you'd use a geocoding service like Google Maps API
  const defaultLocation: OutfitLocation = {
    name: locationString,
    longitude: -0.1276, // London longitude
    latitude: 51.5074   // London latitude
  };
  
  console.log(`Converting location "${locationString}" to coordinates:`, defaultLocation);
  
  // TODO: Implement actual geocoding service integration
  // Example with Google Maps Geocoding API:
  // const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(locationString)}&key=${API_KEY}`);
  // const data = await response.json();
  // if (data.results && data.results.length > 0) {
  //   const location = data.results[0].geometry.location;
  //   return {
  //     name: locationString,
  //     longitude: location.lng,
  //     latitude: location.lat
  //   };
  // }
  
  return defaultLocation;
};

/**
 * Helper function to format outfit data for the frontend
 * Converts backend outfit data to the format expected by the UI
 */
export const formatOutfitForUI = (backendOutfit: any) => {
  const formattedOutfit = {
    _id: backendOutfit._id,
    name: backendOutfit.name,
    isOutfit: true,
    category: backendOutfit.category?.name || 'Outfit',
    location: backendOutfit.location?.name || 'Unknown Location',
    eventDate: backendOutfit.eventDate ? new Date(backendOutfit.eventDate) : undefined,
    plannedDate: backendOutfit.plannedDate ? new Date(backendOutfit.plannedDate) : undefined,
    selectedItems: backendOutfit.itemIds || [],
    items: backendOutfit.items || [], // Populated items from the lookup
    outfitCategoryId: backendOutfit.outfitCategoryId,
    createdAt: backendOutfit.createdAt ? new Date(backendOutfit.createdAt) : undefined,
    updatedAt: backendOutfit.updatedAt ? new Date(backendOutfit.updatedAt) : undefined,
    // For UI compatibility, we'll use a placeholder image
    imageUrl: '@/assets/images/closet/closet-outfit-main3.png',
    isLocalAsset: true
  };

  console.log('Formatted outfit for UI:', {
    id: formattedOutfit._id,
    name: formattedOutfit.name,
    imageUrl: formattedOutfit.imageUrl,
    isLocalAsset: formattedOutfit.isLocalAsset
  });

  return formattedOutfit;
};
