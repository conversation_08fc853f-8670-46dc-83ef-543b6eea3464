import React, { createContext, useState, useEffect } from 'react';
import Meteor from '@meteorrn/core';
import { View, Text } from 'react-native';
import appConfig from '../app.json';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { useSession } from '@/config/ctx';



const queryClient = new QueryClient();

// create a no connections component

const API_URL = __DEV__ ? appConfig.expo.localUrl : appConfig.expo.productionUrl;

export const NoConnections = () => {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      {/* add a lottie animation */}
    </View>
  );
};

// Create a context
export const DDPConnectionContext = createContext({});

export const DDPConnectionProvider = ({ children }: { children: React.ReactNode }) => {
  const [isConnected, setIsConnected] = useState(false);
  const { signIn, signOut } = useSession();

  useEffect(() => {
    console.log('Initializing Meteor connection to:', API_URL);
    // Connect to your Meteor server
    Meteor.connect(API_URL, { AsyncStorage });

    // Check the connection status
    const checkConnection = setInterval(async () => {
      console.log('Checking Meteor connection:', Meteor.status());

      if (Meteor.status().connected) {
        console.log('Meteor connected successfully');

        //check if the token is valid
        const token = await AsyncStorage.getItem('reactnativemeteor_usertoken');
        console.log('Stored token:', token);

        const meteorUserId = Meteor.userId();
        console.log('Current Meteor user ID:', meteorUserId);

        if (!meteorUserId && !token) {
          console.log('No user ID or token found, signing out');
          signOut();
        }

        if (meteorUserId && token) {
          console.log('Valid user session found, signing in');

          // Check user gender when session is valid
          Meteor.call('users-getUser', (err: any, res: any) => {
            if (err) {
              console.log('Error fetching user data:', err);
            } else {
              console.log('User data on login:', res);

              // Check specifically for gender information
              if (res?.data?.profile?.gender) {
                console.log('🧑‍🤝‍🧑 LOGGED IN USER GENDER:', res.data.profile.gender);
                console.log(`Logged in user is ${res.data.profile.gender === 'Male' ? 'MALE ♂️' : res.data.profile.gender === 'Female' ? 'FEMALE ♀️' : 'NON-BINARY/OTHER'}`);
              } else {
                console.log('⚠️ No gender information found for logged in user');
              }
            }
          });

          signIn(token || '');
        }

        setIsConnected(true);
        clearInterval(checkConnection);
      }
    }, 1000);

    return () => clearInterval(checkConnection);
  }, []);

  // Render children when connected
  return (

    <DDPConnectionContext.Provider value={isConnected}>
      <QueryClientProvider client={queryClient}>
        {isConnected ? children : <NoConnections />}
      </QueryClientProvider>
    </DDPConnectionContext.Provider>

  );
};