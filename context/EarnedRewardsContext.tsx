import { withEarnedRewards } from '@/lib/collections/earnedRewards';
import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

type Reward = {
  _id: string;
  code: string;
  congratsTitle: string;
  congratsDescription: string;
  points: number;
};

type EarnedReward = {
  _id: string;
  rewardCode: string;
  metadata?: any;
  seen: boolean;
  createdAt: Date;
  rewardInfo: Reward;
};

type EarnedRewardsModalContextType = {
  showRewardModal: (rewards: EarnedReward[]) => void;
  hideRewardModal: () => void;
  isModalVisible: boolean;
  currentRewards: EarnedReward[];
};

const EarnedRewardsModalContext = createContext<EarnedRewardsModalContextType | undefined>(undefined);

type EarnedRewardsModalProviderProps = {
  children: ReactNode;
};

export const EarnedRewardsModalProvider: React.FC<EarnedRewardsModalProviderProps> = withEarnedRewards(({ children }: EarnedRewardsModalProviderProps) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentRewards, setCurrentRewards] = useState<EarnedReward[]>([]);

  const showRewardModal = useCallback((rewards: EarnedReward[]) => {
    console.log('🎉 Showing reward modal for:', rewards);
    setCurrentRewards(rewards);
    setIsModalVisible(true);
  }, []);

  const hideRewardModal = useCallback(() => {
    console.log('Hiding reward modal');
    setIsModalVisible(false);
    setCurrentRewards([]);
  }, []);

  const value: EarnedRewardsModalContextType = {
    showRewardModal,
    hideRewardModal,
    isModalVisible,
    currentRewards,
  };

  return (
    <EarnedRewardsModalContext.Provider value={value}>
      {children}
    </EarnedRewardsModalContext.Provider>
  );
});

export const useEarnedRewardsModal = (): EarnedRewardsModalContextType => {
  const context = useContext(EarnedRewardsModalContext);
  if (context === undefined) {
    throw new Error('useEarnedRewardsModal must be used within an EarnedRewardsModalProvider');
  }
  return context;
}; 