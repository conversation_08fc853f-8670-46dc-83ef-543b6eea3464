import React, { createContext, useContext } from 'react';

type ProfileContextType = {
  onDeletePress?: () => void;
};

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export const ProfileProvider = ({
  children,
  onDeletePress,
}: ProfileContextType & { children: React.ReactNode }) => {
  return (
    <ProfileContext.Provider
      value={{
        onDeletePress,
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (!context) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};
