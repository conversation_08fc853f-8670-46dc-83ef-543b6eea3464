#!/bin/bash

# Load NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

# Check if .nvmrc exists
if [ -f ".nvmrc" ]; then
  echo "Using Node version specified in .nvmrc"
  nvm use
else
  # If no .nvmrc file exists, use a default version
  # You can change this to your preferred version
  echo "No .nvmrc file found. Using Node v18"
  nvm use 18
fi

# Print current node version
echo "Now using Node $(node -v)"
