// Comprehensive clothing category data structure
// Now using backend API instead of hardcoded data
import { WOMENS_CATEGORIES, MENS_CATEGORIES, fetchCategoriesFromBackend } from './gender-categories';
import Meteor from '@meteorrn/core';

export interface CategoryItem {
  id: string;
  name: string;
  children?: CategoryItem[];
  parent?: string; // Optional parent ID for hierarchical filtering
}

export interface CategoryData {
  WOMENS: CategoryItem[];
  MENS: CategoryItem[];
}

// Function to get the current user's gender
export function getCurrentUserGender(): string | null {
  try {
    // Check if user is logged in
    if (!Meteor.userId()) {
      console.log('No user is logged in');
      return null;
    }

    try {
      // @ts-ignore - Meteor.user() might exist in some implementations
      const user = Meteor.user();
      if (user && user.profile && user.profile.gender) {
        console.log('User gender from Meteor.user():', user.profile.gender);
        return user.profile.gender;
      }
    } catch (e) {
      console.log('Meteor.user() not available:', e);
    }

    console.log('No gender information found in user object');
    return null;
  } catch (error) {
    console.error('Error getting user gender:', error);
    return null;
  }
}

// Dynamic clothing categories based on user gender
// This is now just a placeholder - components should use fetchCategoriesFromBackend instead
export const clothingCategories: CategoryData = {
  WOMENS: WOMENS_CATEGORIES,
  MENS: MENS_CATEGORIES
};

// Async function to get categories from backend based on user gender
export async function getGenderSpecificCategoriesFromBackend(): Promise<CategoryItem[]> {
  const gender = getCurrentUserGender();
  if (!gender) {
    console.warn('No gender available, using default categories');
    return [];
  }

  return await fetchCategoriesFromBackend(gender);
}

// Helper function to flatten the category structure for simpler filtering
export function flattenCategories(categories: CategoryData): CategoryItem[] {
  const flattened: CategoryItem[] = [];

  // Add an "All" category
  flattened.push({ id: 'all', name: 'All' });

  // Process women's categories
  categories.WOMENS.forEach(category => {
    flattened.push({ id: category.id, name: category.name });

    if (category.children) {
      category.children.forEach(child => {
        flattened.push({
          id: child.id,
          name: child.name,
          // Store parent info for hierarchical filtering
          parent: category.id
        });
      });
    }
  });

  // Process men's categories
  categories.MENS.forEach(category => {
    flattened.push({ id: category.id, name: category.name });

    if (category.children) {
      category.children.forEach(child => {
        flattened.push({
          id: child.id,
          name: child.name,
          // Store parent info for hierarchical filtering
          parent: category.id
        });
      });
    }
  });

  return flattened;
}

// Helper function to get all subcategories for a given category
export function getSubcategories(categoryId: string, categories: CategoryData): string[] {
  const subcategoryIds: string[] = [];

  // Check women's categories
  const womensCategory = categories.WOMENS.find(cat => cat.id === categoryId);
  if (womensCategory && womensCategory.children) {
    womensCategory.children.forEach(child => {
      subcategoryIds.push(child.id);
    });
    return subcategoryIds;
  }

  // Check men's categories
  const mensCategory = categories.MENS.find(cat => cat.id === categoryId);
  if (mensCategory && mensCategory.children) {
    mensCategory.children.forEach(child => {
      subcategoryIds.push(child.id);
    });
    return subcategoryIds;
  }

  return subcategoryIds;
}

// Helper function to get parent category for a subcategory
export function getParentCategory(subcategoryId: string, categories: CategoryData): string | null {
  // Check women's categories
  for (const category of categories.WOMENS) {
    if (category.children) {
      const found = category.children.find(child => child.id === subcategoryId);
      if (found) {
        return category.id;
      }
    }
  }

  // Check men's categories
  for (const category of categories.MENS) {
    if (category.children) {
      const found = category.children.find(child => child.id === subcategoryId);
      if (found) {
        return category.id;
      }
    }
  }

  return null;
}

// Helper function to find a category by ID in the nested structure
export function findCategoryById(id: string, categories: CategoryData): CategoryItem | null {
  // Check in WOMENS categories
  for (const category of categories.WOMENS) {
    if (category.id === id) {
      return category;
    }

    // Check in subcategories
    if (category.children) {
      for (const subcategory of category.children) {
        if (subcategory.id === id) {
          return subcategory;
        }
      }
    }
  }

  // Check in MENS categories
  for (const category of categories.MENS) {
    if (category.id === id) {
      return category;
    }

    // Check in subcategories
    if (category.children) {
      for (const subcategory of category.children) {
        if (subcategory.id === id) {
          return subcategory;
        }
      }
    }
  }

  return null;
}
