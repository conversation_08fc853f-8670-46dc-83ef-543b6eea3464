interface ClothesCategory {
  _id: string;
  name: string;
  category: string;
}

interface ClothesItem {
  _id: string;
  name: string;
  category: string;
  color: string;
  size: string;
  material: string;
  brand: string;
}

export const CLOTHES_CATEGORIES: ClothesCategory[] = [
  {
    _id: '1',
    name: 'Shirt',
    category: 'Shirt',
  },
  {
    _id: '2',
    name: 'T-shirt',
    category: 'T-shirt',
  },
  {
    _id: '3',
    name: '<PERSON><PERSON>',
    category: '<PERSON><PERSON>',
  },
  {
    _id: '4',
    name: 'Shorts',
    category: 'Shorts',
  },
  {
    _id: '5',
    name: 'Shoes',
    category: 'Shoes',
  },
  {
    _id: '6',
    name: 'Accessories',
    category: 'Accessories',
  },
  {
    _id: '7',
    name: 'Socks',
    category: 'Socks',
  },
];

export const CLOTHES: ClothesItem[] = [
  {
    _id: '1',
    name: 'Casual Shirt',
    category: 'Shirt',
    color: 'Blue',
    size: 'M',
    material: 'Cotton',
    brand: 'Brand A',
  },
  {
    _id: '2',
    name: 'Formal Shirt',
    category: 'Shirt',
    color: 'White',
    size: 'L',
    material: 'Polyester',
    brand: 'Brand B',
  },
  {
    _id: '3',
    name: 'Graphic T-shirt',
    category: 'T-shirt',
    color: 'Black',
    size: 'S',
    material: 'Cotton',
    brand: 'Brand C',
  },
  {
    _id: '4',
    name: 'Plain T-shirt',
    category: 'T-shirt',
    color: 'Red',
    size: 'M',
    material: 'Cotton',
    brand: 'Brand D',
  },
  {
    _id: '5',
    name: 'Skinny Jeans',
    category: 'Jeans',
    color: 'Dark Blue',
    size: '32',
    material: 'Denim',
    brand: 'Brand E',
  },
  {
    _id: '6',
    name: 'Regular Jeans',
    category: 'Jeans',
    color: 'Light Blue',
    size: '34',
    material: 'Denim',
    brand: 'Brand F',
  },
  {
    _id: '7',
    name: 'Denim Shorts',
    category: 'Shorts',
    color: 'Blue',
    size: 'M',
    material: 'Denim',
    brand: 'Brand G',
  },
  {
    _id: '8',
    name: 'Cargo Shorts',
    category: 'Shorts',
    color: 'Khaki',
    size: 'L',
    material: 'Cotton',
    brand: 'Brand H',
  },
  {
    _id: '9',
    name: 'Running Shoes',
    category: 'Shoes',
    color: 'Black',
    size: '10',
    material: 'Mesh',
    brand: 'Brand I',
  },
  {
    _id: '10',
    name: 'Formal Shoes',
    category: 'Shoes',
    color: 'Brown',
    size: '9',
    material: 'Leather',
    brand: 'Brand J',
  },
  {
    _id: '11',
    name: 'Sneakers',
    category: 'Shoes',
    color: 'White',
    size: '11',
    material: 'Canvas',
    brand: 'Brand K',
  },
  {
    _id: '12',
    name: 'Baseball Cap',
    category: 'Accessories',
    color: 'Red',
    size: 'One Size',
    material: 'Cotton',
    brand: 'Brand L',
  },
  {
    _id: '13',
    name: 'Belt',
    category: 'Accessories',
    color: 'Black',
    size: 'M',
    material: 'Leather',
    brand: 'Brand M',
  },
  {
    _id: '14',
    name: 'Scarf',
    category: 'Accessories',
    color: 'Grey',
    size: 'One Size',
    material: 'Wool',
    brand: 'Brand N',
  },
  {
    _id: '15',
    name: 'Wool Socks',
    category: 'Socks',
    color: 'White',
    size: 'L',
    material: 'Wool',
    brand: 'Brand O',
  },
  {
    _id: '16',
    name: 'Ankle Socks',
    category: 'Socks',
    color: 'Black',
    size: 'M',
    material: 'Cotton',
    brand: 'Brand P',
  },
  {
    _id: '17',
    name: 'Dress Socks',
    category: 'Socks',
    color: 'Navy',
    size: 'L',
    material: 'Silk',
    brand: 'Brand Q',
  },
  {
    _id: '18',
    name: 'Leather Jacket',
    category: 'Accessories',
    color: 'Black',
    size: 'L',
    material: 'Leather',
    brand: 'Brand R',
  },
  {
    _id: '19',
    name: 'Beanie',
    category: 'Accessories',
    color: 'Blue',
    size: 'One Size',
    material: 'Wool',
    brand: 'Brand S',
  },
  {
    _id: '20',
    name: 'Sunglasses',
    category: 'Accessories',
    color: 'Black',
    size: 'One Size',
    material: 'Plastic',
    brand: 'Brand T',
  },
];