import { Dimensions, Platform } from 'react-native';

// Get screen dimensions
const { width, height } = Dimensions.get('window');

// Check if device is a tablet (iPad or large Android)
export const isTablet = () => {
  const shortestSide = Math.min(width, height);
  return shortestSide >= 600; // Common breakpoint for tablets
};

// Base padding values
export const PADDING = {
  // Standard side padding for containers
  CONTAINER_HORIZONTAL: isTablet() ? 24 : 16,
  
  // Field padding (used in FieldView)
  FIELD_HORIZONTAL: isTablet() ? 20 : 16,
  FIELD_VERTICAL: isTablet() ? 16 : 12,
  
  // Modal content padding
  MODAL_CONTENT_HORIZONTAL: isTablet() ? 24 : 16,
  
  // Frame container width (for centered content in modals)
  FRAME_CONTAINER_WIDTH: isTablet() ? 500 : 343,
};

// Standard widths for different components
export const WIDTHS = {
  // Modal content width
  MODAL_CONTENT: isTablet() ? '80%' : '100%',
  
  // Field input width
  FIELD_INPUT: isTablet() ? '60%' : '50%',
  
  // Standard container width (percentage of screen)
  CONTAINER: isTablet() ? '85%' : '90%',
};

// Standard heights
export const HEIGHTS = {
  // Field height
  FIELD: isTablet() ? 64 : 56,
  
  // Input height
  INPUT: isTablet() ? 36 : 30,
};

// Font sizes
export const FONT_SIZES = {
  // Label font size
  LABEL: isTablet() ? 18 : 16,
  
  // Input font size
  INPUT: isTablet() ? 18 : 16,
  
  // Header font size
  HEADER: isTablet() ? 28 : 24,
};

// Border radius values
export const BORDER_RADIUS = {
  // Field group border radius
  FIELD_GROUP: isTablet() ? 20 : 16,
  
  // Modal border radius
  MODAL: isTablet() ? 28 : 24,
};
