import DDPClient from 'ddp-client';

// Initialize DDP client
const meteorClient = new DDPClient({
    host: 'dev.myuse.world',
    port: 443,
    ssl: true,
    autoReconnect: true,
    autoReconnectTimer: 10000,
    maintainCollections: true,
    ddpVersion: '1',
    useSockJs: true,
});

// Connect to the server
meteorClient.connect((error, wasReconnect) => {
    if (error) {
        console.error('DDP connection error:', error);
    } else {
        console.log('DDP connected', wasReconnect ? 'reconnected' : 'connected');
    }
});

export default meteorClient; 