import meteorClient from "@/lib/meteor-client";

export interface StyleQuizAnswer {
    questionId: string;
    selected: boolean;
}

export interface StyleQuizResponse {
    success: boolean;
    message?: string;
}

export const styleQuizService = {
    answer: async (data: StyleQuizAnswer): Promise<StyleQuizResponse> => {
        try {
            const result = await meteorClient.call('styleQuiz.answer', data);
            return result || { success: false, message: 'No response from server' };
        } catch (error) {
            return { success: false, message: 'Failed to submit answer' };
        }
    },
}; 