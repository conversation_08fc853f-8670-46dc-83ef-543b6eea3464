import Meteor, { Mongo, withTracker } from '@meteorrn/core';

// Type definition for EarnedReward document
export interface EarnedReward {
  _id: string;
  rewardCode: string;
  metadata?: any;
  seen: boolean;
  createdAt: Date;
  userId: string;
  points?: number;
}

// Collection definition for earned rewards
// This collection will be populated by the 'earnedRewards-getUnseen' subscription
export const EarnedRewards = new Mongo.Collection<EarnedReward>('earnedRewards');

// Helper functions for working with the collection
export const EarnedRewardsHelpers = {
  // Get all unseen earned rewards for the current user
  getUnseenRewards: (): EarnedReward[] => {
    const userId = Meteor.userId();
    if (!userId) {
      return [];
    }

    const rewards = EarnedRewards.find(
      { userId, seen: false },
    ).fetch();

    return rewards;
  },

  // Get all earned rewards for the current user
  getAllRewards: (): EarnedReward[] => {
    const userId = Meteor.userId();
    if (!userId) {
      return [];
    }

    const rewards = EarnedRewards.find(
      { userId, seen: { $ne: true } }
    ).fetch();

    return rewards;
  },

  // Get total points from unseen rewards
  getUnseenPoints: (): number => {
    const unseenRewards = EarnedRewardsHelpers.getUnseenRewards();
    const points = unseenRewards.reduce((total, reward) => total + (reward.points || 0), 0);
    return points;
  },

  // Get total points from all rewards
  getTotalPoints: (): number => {
    const allRewards = EarnedRewardsHelpers.getAllRewards();
    const points = allRewards.reduce((total, reward) => total + (reward.points || 0), 0);
    return points;
  },

  // Check if user has any unseen rewards
  hasUnseenRewards: (): boolean => {
    const hasUnseen = EarnedRewardsHelpers.getUnseenRewards().length > 0;
    return hasUnseen;
  },

  // Get count of unseen rewards
  getUnseenCount: (): number => {
    const count = EarnedRewardsHelpers.getUnseenRewards().length;
    return count;
  }
};

// Reactive data source using withTracker
export const withEarnedRewards = (component: any) => {
  return withTracker(() => {
    const userId = Meteor.userId();
    const subscription = Meteor.subscribe('earnedRewards-getUnseen');

    return {
      earnedRewards: userId ? EarnedRewardsHelpers.getUnseenRewards() : [],
      totalPoints: userId ? EarnedRewardsHelpers.getUnseenPoints() : 0,
      hasUnseenRewards: userId ? EarnedRewardsHelpers.hasUnseenRewards() : false,
      isLoading: !subscription.ready(),
      subscription
    };
  })(component);
};

export default EarnedRewards; 
