declare module '@meteorrn/core' {
    interface MeteorOptions {
        host: string;
        port: number;
        ssl: boolean;
        autoConnect: boolean;
        autoReconnect: boolean;
        reconnectInterval: number;
    }

    interface MeteorClient {
        call(methodName: string, ...args: any[]): Promise<any>;
        userId(): string | null;
        user(): any | null;
        status(): {
            connected: boolean;
            status: string;
            retryCount: number;
        };
    }

    const Meteor: {
        connect(options: MeteorOptions): MeteorClient;
    };

    export { Meteor };
} 