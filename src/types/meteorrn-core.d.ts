declare module '@meteorrn/core' {
  export function withTracker(component: any): any;

  export function useTracker(component: any): any;
  
  export class Mongo {
    static Collection: {
      new <T>(name: string): MongoCollection<T>;
    };
  }
  
  interface MongoCollection<T> {
    find(selector?: any, options?: any): {
      fetch(): T[];
      count(): number;
    };
    findOne(selector?: any, options?: any): T | undefined;
    insert(doc: T): string;
    update(selector: any, modifier: any, options?: any): number;
    remove(selector: any): number;
    observe(callback: () => void): () => void;
  }
  
  export default class Meteor {
    static connect(url: string, options?: any): void;
    static status(): { connected: boolean };
    static userId(): string | null;
    static call(method: string, ...args: any[]): Promise<any>;
    static subscribe(name: string, ...args: any[]): any;
    static _loginWithToken(token: string): void;
    static logout(callback?: () => void): void;
    static loginWithPassword(email: string, password: string, callback?: (err: any, res: any) => void): Promise<any>;
  }
}

// Add or update the type definition for the signUp response
interface SignUpResponse {
  success: boolean;
  data: {
    token: string; // Ensure the token is included within a data object
  };
  // other properties...
} 