import FaceIdSvg from '@/assets/svg/face-id.svg';
import FingerprintSvg from '@/assets/svg/fingerprint.svg';
import Button from '@/components/common/Button';
import ControlledInput from '@/components/common/ControlledInput';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent from '@/components/common/Text';
import { brand } from '@/constants/Colors';
import { signIn } from '@/methods/users';
import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Alert, Text, TouchableOpacity, View } from 'react-native';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';

export default function SignUp() {
  const router = useRouter();
  const signInMutation = signIn();
  const [isBiometricSupported, setIsBiometricSupported] = useState<boolean | null>(null);
  const [biometricType, setBiometricType] = useState<'face' | 'fingerprint' | null>(null);
  const [hasStoredCredentials, setHasStoredCredentials] = useState(false);

  const { control, handleSubmit, formState: { errors, isValid } } = useForm();

  useEffect(() => {
    checkBiometricSupport();
    checkStoredCredentials();
  }, []);

  const checkStoredCredentials = async () => {
    const credentials = await getStoredCredentials();
    setHasStoredCredentials(!!credentials);
  };

  const checkBiometricSupport = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      console.log('Biometric hardware compatible:', compatible);
      setIsBiometricSupported(compatible);

      if (compatible) {
        const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
        console.log('Supported authentication types:', supportedTypes);

        const hasFaceId = supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION);
        const hasFingerprint = supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT);

        if (hasFingerprint) {
          setBiometricType('fingerprint');
        } else if (hasFaceId) {
          setBiometricType('face');
        }
      }
    } catch (error) {
      console.error('Error checking biometric support:', error);
    }
  };

  const storeCredentials = async (email: string, password: string) => {
    try {
      await SecureStore.setItemAsync('userCredentials', JSON.stringify({ email, password }));
      console.log('Credentials stored successfully');
      setHasStoredCredentials(true);
    } catch (error) {
      console.error('Error storing credentials:', error);
    }
  };

  const getStoredCredentials = async () => {
    try {
      const credentials = await SecureStore.getItemAsync('userCredentials');
      return credentials ? JSON.parse(credentials) : null;
    } catch (error) {
      console.error('Error retrieving credentials:', error);
      return null;
    }
  };

  const handleBiometricAuth = async () => {
    try {
      console.log('Starting biometric authentication...');
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to sign in',
        fallbackLabel: 'Use password',
      });
      console.log('Authentication result:', result);

      if (result.success) {
        const credentials = await getStoredCredentials();
        if (credentials) {
          console.log('Found stored credentials, signing in...');
          signInMutation.mutate(credentials);
        } else {
          Alert.alert('Error', 'No stored credentials found. Please sign in with email and password first.');
        }
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      Alert.alert('Error', 'Biometric authentication failed');
    }
  };

  useEffect(() => {
    if (signInMutation.isSuccess) {
      console.log('Sign in successful, checking user data...');

      // Check user gender after successful login
      import('@/methods/users').then(({ getUserProfile }) => {
        const { refetch } = getUserProfile();
        refetch().then(result => {
          if (result.data?.data?.profile?.gender) {
            console.log('🧑‍🤝‍🧑 SIGN IN - USER GENDER:', result.data.data.profile.gender);
            console.log(`Signed in user is ${result.data.data.profile.gender === 'Male' ? 'MALE ♂️' : result.data.data.profile.gender === 'Female' ? 'FEMALE ♀️' : 'NON-BINARY/OTHER'}`);
          } else {
            console.log('⚠️ SIGN IN - No gender information found for user');
          }
        }).catch(err => {
          console.log('Error fetching user profile after sign in:', err);
        });
      });

      router.push('/(tabs)/home');
      return;
    }

    if (signInMutation.isError) {
      Alert.alert('Sign in failed', 'username or password is incorrect');
      return;
    }
  }, [signInMutation.isSuccess, signInMutation.isError]);

  const onSubmit = async (data: any) => {
    console.log(data);
    // Store credentials before signing in
    await storeCredentials(data.email, data.password);
    signInMutation.mutate(data);
  };

  const handleSignUpWithGoogle = () => {
    // router.push('/sign-up/sign-up-process');
    //add temporary disable
    Alert.alert('This feature is not available yet');
  };

  const handleSignUpWithApple = () => {
    // router.push('/sign-up/sign-up-process');
    //add temporary disable
    Alert.alert('This feature is not available yet');
  };

  return (
    <View>
      <HeaderPage />
      <View style={{ marginTop: 16 }}>
        <SectionTitle title="Sign In" />
        <View style={{ marginTop: 8 }}>
          <TextComponent string="Sign in now to be your own muse and elevate your fashion game!" />
        </View>
      </View>
      <View style={{ marginTop: 32, gap: 24 }}>
        <ControlledInput
          control={control}
          name="email"
          placeholder="Username or Email"
          rules={{
            required: 'Username or Email is required',
          }}
          error={errors?.email?.message as string}
        />
        <ControlledInput
          control={control}
          name="password"
          rules={{
            required: 'Password is required',
          }}
          placeholder="Password"
          secureTextEntry={true}
          error={errors?.password?.message as string}
        />
        <TouchableOpacity onPress={() => router.push('/forgot-password')} activeOpacity={1}>
          <Text style={{ fontFamily: 'MuktaVaani', color: brand.green[500], fontSize: 12 }}>
            Forgot password?
          </Text>
        </TouchableOpacity>
        <Button
          isDisabled={!isValid || signInMutation.isPending}
          isLoading={signInMutation.isPending}
          title={signInMutation.isPending ? 'Signing in...' : 'Sign In'}
          onPress={handleSubmit(onSubmit)}
        />
        <View>
          <Text style={{ fontFamily: 'MuktaVaani', fontSize: 14, textAlign: 'center' }}>
            Don't have an account yet?{' '}
            <Text
              style={{
                color: brand.green[500],
                textDecorationLine: 'underline',
              }}
              onPress={() => router.push('/sign-up')}
            >
              Sign up.
            </Text>
          </Text>
        </View>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: 32,
          }}
        >
          {isBiometricSupported && hasStoredCredentials && (
            <TouchableOpacity onPress={handleBiometricAuth}>
              {biometricType === 'face' ? <FaceIdSvg /> : <FingerprintSvg />}
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
}
