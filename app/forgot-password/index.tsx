import Button from '@/components/common/Button';
import ControlledInput from '@/components/common/ControlledInput';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { router } from 'expo-router';
import { useCallback, useContext } from 'react';
import { useForm } from 'react-hook-form';
import { View } from 'react-native';
import { ForgotPasswordContext, ForgotPasswordContextType } from './_layout';

export default function ForgotPassword() {
  const { userDetails, setUserDetails } = useContext(ForgotPasswordContext) as ForgotPasswordContextType;

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm({
    mode: 'onChange',
  });

  const handleNext = useCallback(() => {
    router.push('/forgot-password/confirm-identity');
  }, []);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Enter your email" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <Text string="We'll send you a code to this email." />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <ControlledInput
            control={control}
            name="email"
            rules={{
              required: 'Email is required',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Invalid email address',
              },
            }}
            placeholder="Email"
            error={errors?.email?.message as string}
            onInputChange={(value) => setUserDetails({ email: value, password: userDetails?.password || '' })}
            defaultValue={userDetails?.email}
          />
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title="Next"
          onPress={handleSubmit(handleNext)}
          isDisabled={!isValid}
        />
      </View>
    </View>
  );
}
