import Button from '@/components/common/Button';
import ControlledInput from '@/components/common/ControlledInput';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent, { TextError } from '@/components/common/Text';
import { router } from 'expo-router';
import { useCallback, useContext, useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Alert, View } from 'react-native';
import { ForgotPasswordContext, ForgotPasswordContextType } from './_layout';
import { useTheme } from 'styled-components/native';
import { Ionicons } from '@expo/vector-icons';
import { setNewPasswordViaEmail } from '@/methods/users';

// Password must contain at least one number or one special character and no spaces
const passwordRegex = /^(?=.*[!@#$%^&*(),.?":{}|<>]|.*\d)(?!.*\s).+$/;

interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

interface ResetPasswordResponse {
  success: boolean;
  message?: string;
  data: {
    token: string;
  };
}

export default function ResetPassword() {
  const { userDetails } = useContext(ForgotPasswordContext) as ForgotPasswordContextType;
  const { mutate: resetPasswordMutation, isPending: isResetting } = setNewPasswordViaEmail();

  const {
    control,
    handleSubmit,
    watch,
    trigger,
    formState: { errors, isValid },
  } = useForm<ResetPasswordFormData>({
    mode: 'onChange',
    defaultValues: {
      password: '',
      confirmPassword: '',
    }
  });

  const passwordValue = watch('password');
  const confirmPasswordValue = watch('confirmPassword');

  // Trigger validation when password changes
  useEffect(() => {
    trigger('confirmPassword');
  }, [passwordValue, confirmPasswordValue]);

  const handleNext = useCallback((data: ResetPasswordFormData) => {
    if (data.password && userDetails?.email) {
      resetPasswordMutation(
        { email: userDetails.email, password: data.password },
        {
          onSuccess: (response: ResetPasswordResponse) => {
            if (response.success) {
              Alert.alert('Password updated!', 'Your password has been successfully updated.', [
                {
                  text: 'OK',
                  onPress: () => router.push('/sign-in'),
                },
              ]);
            }
          },
          onError: (error: Error) => {
            Alert.alert('Error', error.message);
          },
        }
      );
    }
  }, [userDetails, resetPasswordMutation]);

  const isPasswordValid = (password: string) => {
    const minLengthValid = password?.length >= 8;
    const patternValid = passwordRegex.test(password);
    return { minLengthValid, patternValid };
  };

  const { minLengthValid, patternValid } = isPasswordValid(passwordValue);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Update your password" />
        </View>
        <View style={{ marginTop: 32, gap: 8 }}>
          <View style={{ position: 'relative' }}>
            <ControlledInput
              control={control}
              name="password"
              rules={{
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters long',
                },
                pattern: {
                  value: passwordRegex,
                  message: 'Password must contain at least one letter and one special character or number, and no spaces',
                },
              }}
              placeholder="New Password"
              secureTextEntry={true}
              isDisabled={isResetting}
            />
          </View>
          <View>
            <TextError
              string=" • Password must be at least 8 characters long"
              isValid={minLengthValid}
            />
            <TextError
              string=" • Must have at least one special character or number"
              isValid={patternValid}
            />
          </View>
        </View>
        <View style={{ marginTop: 32, gap: 8 }}>
          <View style={{ position: 'relative' }}>
            <ControlledInput
              control={control}
              name="confirmPassword"
              rules={{
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters long',
                },
                pattern: {
                  value: passwordRegex,
                  message: 'Password must contain at least one letter and one special character or number, and no spaces',
                },
                validate: (value: string) => value === passwordValue,
              }}
              placeholder="Confirm Password"
              secureTextEntry={true}
              isDisabled={isResetting}
            />
          </View>
          <View>
            <TextError
              string=" • Passwords must match"
              isValid={!errors.confirmPassword}
            />
          </View>
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title="Save changes"
          onPress={handleSubmit(handleNext)}
          isDisabled={!isValid || isResetting}
        />
      </View>
    </View>
  );
} 