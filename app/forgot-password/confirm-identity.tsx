import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent from '@/components/common/Text';
import { sendForgotPasswordEmail } from '@/methods/users';
import { useRouter } from 'expo-router';
import { useCallback, useContext, useEffect } from 'react';
import { Text, View } from 'react-native';
import { useTheme } from 'styled-components/native';
import { ForgotPasswordContext, ForgotPasswordContextType } from './_layout';

export default function ForgotPasswordVerification() {
  const { mutate: sendEmail, isSuccess: isEmailSent, isPending } = sendForgotPasswordEmail();
  const router = useRouter();
  const { userDetails } = useContext(ForgotPasswordContext) as ForgotPasswordContextType;

  const theme = useTheme();

  useEffect(() => {
    if (isEmailSent) {
      router.push('/forgot-password/verification');
    }
  }, [isEmailSent]);

  const handleNext = useCallback(() => {
    if (userDetails?.email) {
      sendEmail(userDetails.email);
    }
  }, []);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Confirm your identity" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <TextComponent string={<>For security reasons, we'll send a code to <Text style={{ color: theme.brand.green[500], fontFamily: 'MuktaVaani' }}>{userDetails?.email}</Text>.</>} />
          <TextComponent string="If you don't have access to this email address, please contact Myuse support." />
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title="Send code"
          onPress={handleNext}
          isDisabled={isPending}
        />
      </View>
    </View>
  );
}
