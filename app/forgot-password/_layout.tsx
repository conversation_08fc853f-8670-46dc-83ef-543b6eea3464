import Container from '@/components/common/Container';
import { Slot } from 'expo-router';
import { createContext, Dispatch, SetStateAction, useState } from 'react';

type UserDetails = {
  email: string;
  password: string;
};

export type ForgotPasswordContextType = {
  userDetails: UserDetails | null;
  setUserDetails: Dispatch<SetStateAction<UserDetails>>;
};

export const ForgotPasswordContext = createContext<ForgotPasswordContextType | null>(null);

export default function ForgotPasswordLayout() {
  const [userDetails, setUserDetails] = useState<UserDetails>({
    email: '',
    password: '',
  });

  return (
    <ForgotPasswordContext.Provider value={{ userDetails, setUserDetails }}>
      <Container>
        <Slot />
      </Container>
    </ForgotPasswordContext.Provider>
  );
}
