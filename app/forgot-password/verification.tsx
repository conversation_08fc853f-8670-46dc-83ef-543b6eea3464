import Button from '@/components/common/Button';
import ControlledInput from '@/components/common/ControlledInput';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent from '@/components/common/Text';
import { sendForgotPasswordEmail, verifyForgotPasswordCode } from '@/methods/users';
import { router, useLocalSearchParams } from 'expo-router';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ActivityIndicator, Text, View } from 'react-native';
import { useTheme } from 'styled-components/native';
import { ForgotPasswordContext, ForgotPasswordContextType } from './_layout';

interface VerificationFormData {
  verificationToken: string;
}

export default function ForgotPasswordVerification() {
  const { mutate: sendEmail, isPending: isSendingEmail } = sendForgotPasswordEmail();
  const { mutate: verifyCodeMutation, isPending: isVerifying } = verifyForgotPasswordCode();
  const { userDetails, setUserDetails } = useContext(ForgotPasswordContext) as ForgotPasswordContextType;
  const { token } = useLocalSearchParams<{ token: string }>();
  const theme = useTheme();
  const [verificationError, setVerificationError] = useState<string | undefined>(undefined);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
  } = useForm<VerificationFormData>({
    mode: 'onChange',
  });

  useEffect(() => {
    if (token) {
      setValue('verificationToken', token);
      handleVerifyCode(token);
    }
  }, [token, setValue]);

  const handleVerifyCode = useCallback((token: string) => {
    if (userDetails?.email) {
      verifyCodeMutation(
        { code: token, email: userDetails.email },
        {
          onSuccess: (data) => {
            if (data.success) {
              router.push('/forgot-password/reset-password');
            } else {
              setVerificationError('Invalid verification token');
            }
          },
          onError: () => {
            setVerificationError('Failed to verify token. Please try again.');
          },
        }
      );
    }
  }, [userDetails?.email, verifyCodeMutation]);

  const handleNext = useCallback((data: VerificationFormData) => {
    handleVerifyCode(data.verificationToken);
  }, [handleVerifyCode]);

  const handleResendEmail = useCallback(() => {
    if (userDetails?.email) {
      setVerificationError(undefined);
      sendEmail(userDetails.email, {
        onSuccess: () => {
          // Show success message or handle as needed
        },
        onError: () => {
          setVerificationError('Failed to send verification token. Please try again.');
        },
      });
    }
  }, [userDetails?.email, sendEmail]);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Check your email!" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <TextComponent string={<>To continue, please enter the verification token we sent to <Text style={{ color: theme.brand.green[500], fontFamily: 'MuktaVaani' }}>{userDetails?.email}</Text>.</>} />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <ControlledInput
            control={control}
            name="verificationToken"
            rules={{
              required: 'Verification token is required',
              minLength: {
                value: 4,
                message: 'Verification token must be 4 digits',
              },
              maxLength: {
                value: 4,
                message: 'Verification token must be 4 digits',
              },
              pattern: {
                value: /^\d{4}$/,
                message: 'Verification token must be 4 digits',
              },
            }}
            placeholder="Enter 4-digit token"
            error={errors?.verificationToken?.message || verificationError}
            onInputChange={(value) => {
              if (value.length <= 4) {
                setValue('verificationToken', value);
                setVerificationError(undefined);
              }
            }}
            isDisabled={isVerifying}
          />
        </View>
        <View style={{ marginTop: 16, gap: 4, flexDirection: 'row', alignItems: 'center' }}>
          <TextComponent
            string={<>Didn't receive it? <Text onPress={handleResendEmail} suppressHighlighting style={{ color: theme.brand.green[500], fontFamily: 'MuktaVaani' }}>Resend verification token.</Text></>}
          />
          {isSendingEmail &&
            <View>
              <ActivityIndicator size="small" color={theme.brand.green[500]} />
            </View>
          }
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title={isVerifying ? "Verifying..." : "Next"}
          onPress={handleSubmit(handleNext)}
          isDisabled={!isValid || isVerifying}
        />
      </View>
    </View>
  );
}
