import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text, { TextError } from '@/components/common/Text';
import { updateUserProfile } from '@/methods/users';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, TextInput, View } from 'react-native';

const ChangeUsername = () => {
  const [newUsername, setNewUsername] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const {
    mutate: updateProfile,
    isPending: isUpdatingProfile,
    data,
  } = updateUserProfile();

  const handleSave = useCallback(() => {
    updateProfile({ userName: newUsername });
  }, [newUsername]);

  const validateUsername = (username: string) => {
    const usernameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!username) {
      setErrorMessage('Username cannot be empty');
      setIsValid(false);
      return;
    }
    if (username.length < 3) {
      setErrorMessage('Username must be at least 3 characters long');
      setIsValid(false);
      return;
    }
    if (username.includes(' ')) {
      setErrorMessage('Username cannot contain spaces');
      setIsValid(false);
      return;
    }
    if (!usernameRegex.test(username)) {
      setErrorMessage(
        'Username can only contain letters, numbers, hyphens and underscores',
      );
      setIsValid(false);
      return;
    }
    setErrorMessage('');
    setIsValid(true);
  };

  const handleUsernameChange = (val: string) => {
    setNewUsername(val);
    validateUsername(val);
  };

  useEffect(() => {
    if (!data) return;

    const success = data.success;
    if (success) {
      setErrorMessage('');
      setIsValid(true);
      router.push('/(tabs)/profile');
      return;
    }

    setErrorMessage(data.message);
    setIsValid(false);
  }, [data]);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage noLogo />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Change Username" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <Text string="Pick a new username to refresh your profile. Make it unique and totally you!" />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <TextInput
            value={newUsername}
            onChangeText={handleUsernameChange}
            autoCapitalize="none"
            style={[
              styles.textInput,
              !isValid && newUsername ? styles.errorInput : null,
            ]}
          />
          {errorMessage ? <TextError string={errorMessage} /> : null}
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title="Save"
          onPress={handleSave}
          isDisabled={isUpdatingProfile || !isValid || !newUsername}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  textInput: {
    fontFamily: 'MuktaVaaniLight',
    fontSize: 16,
    height: 56,
    padding: 16,
    borderWidth: 1,
    borderColor: '#505050',
    borderRadius: 16,
  },
  errorInput: {
    borderColor: 'red',
  },
});

export default ChangeUsername;
