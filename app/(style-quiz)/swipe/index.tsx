import DislikeIcon from "@/assets/svg/dislike.svg";
import LikeIcon from "@/assets/svg/like.svg";
import { answerStyleQuiz, fetchStyleQuiz } from "@/methods/style-quiz";
import { useImagePreloader } from "@/hooks/useImagePreloader";
import { ImagePreloaderScreen } from "@/components/ImagePreloaderScreen";
import { Ionicons } from "@expo/vector-icons";
import { Stack, useRouter } from "expo-router";
import { useCallback, useEffect, useState, useRef } from "react";
import {
    Animated,
    Dimensions,
    Image,
    ImageSourcePropType,
    PanResponder,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import styled from "styled-components/native";

interface Theme {
    colors: {
        background: string;
        gray: {
            [key: number]: string;
        };
    };
    fonts: {
        mukta: string;
    };
}

interface QuizQuestion {
    _id: string;
    aesthetic: string;
    brand: string;
    categories: string[];
    styles: string[];
    colors: string[];
    details: string[];
    fabric: string;
    accessories: string[];
    shoe: string;
    image: string;
    answer: boolean | null;
}

const { width: SCREEN_WIDTH } = Dimensions.get("window");
const SWIPE_THRESHOLD = 120;

const Container = styled.View`
    flex: 1;
    background-color: #fff;
    justify-content: center;
    align-items: center;
`;

const Header = styled.View`
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-horizontal: 16px;
    position: absolute;
    top: 76px;
`;

const CloseButton = styled.TouchableOpacity`
    width: 32px;
    height: 32px;
    align-items: center;
    justify-content: center;
    background-color: #F5F5F5;
    border-radius: 16px;
`;

const HeaderTitleContainer = styled.View`
    flex: 1;
    align-items: center;
    justify-content: center;
`;

const HeaderText = styled(Text)`
    font-family: CormorantGaramondSemiBold;
    font-size: 32px;
    line-height: 40px;
    letter-spacing: 0;
    color: #0E7E61;
    text-align: center;
`;

const CounterText = styled(Text)`
    font-family: MuktaVaaniLight;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0;
    color: #6D6D6D;
    text-align: left;
    width: 32px;
    height: 20px;
`;

export default function SwipeScreen() {
    const router = useRouter();
    const [currentIndex, setCurrentIndex] = useState(0);
    const [position] = useState(new Animated.ValueXY());
    const [quizQuestions, setQuizQuestions] = useState<QuizQuestion[]>([]);
    const fadeAnim = useRef(new Animated.Value(1)).current;
    const [isLastQuestion, setIsLastQuestion] = useState(false);
    const [isQuizReady, setIsQuizReady] = useState(false);

    const { mutate: answerMutation } = answerStyleQuiz();
    const { data: fetchStyleQuizQuestions } = fetchStyleQuiz();
    const { preloadImages, progress, isPreloading, retryFailedImages } = useImagePreloader();

    useEffect(() => {
        if (fetchStyleQuizQuestions) {
            setQuizQuestions(fetchStyleQuizQuestions.data.quiz);
        }
    }, [fetchStyleQuizQuestions]);

    // Preload images when quiz questions are available
    useEffect(() => {
        if (quizQuestions.length > 0 && !isQuizReady) {
            const imageUrls = quizQuestions.map(question => question.image).filter(Boolean);
            console.log('Starting preload for quiz images:', imageUrls);
            preloadImages(imageUrls);
        }
    }, [quizQuestions, isQuizReady, preloadImages]);

    // Start quiz when preloading is complete
    useEffect(() => {
        if (progress.isComplete && !isPreloading && quizQuestions.length > 0) {
            console.log('Preloading complete, starting quiz. Failed images:', progress.failedUrls);
            setIsQuizReady(true);
        }
    }, [progress.isComplete, isPreloading, quizQuestions.length, progress.failedUrls]);

    // Fallback: start quiz after 10 seconds even if preloading is still in progress
    useEffect(() => {
        if (quizQuestions.length > 0 && !isQuizReady) {
            const timeout = setTimeout(() => {
                console.log('Preloading timeout reached, starting quiz anyway');
                setIsQuizReady(true);
            }, 10000); // 10 seconds timeout

            return () => clearTimeout(timeout);
        }
    }, [quizQuestions.length, isQuizReady]);

    useEffect(() => {
        if (isLastQuestion) {
            setTimeout(() => {
                router.push('/(style-quiz)/MoodBoardIntro');
            }, 300);
        }
    }, [isLastQuestion]);

    const handleSwipe = useCallback(async (direction: "right" | "left") => {
        if (currentIndex >= quizQuestions.length) return;

        const questionId = quizQuestions[currentIndex]._id;
        const selected = direction === "right";

        // Fade out current image with slightly longer duration
        Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
        }).start();

        answerMutation({ questionId, selected });

        // If this is the last question, let the fade complete before setting index
        if (currentIndex + 1 >= quizQuestions.length) {
            // Don't update state since we're navigating away
            setIsLastQuestion(true);
        } else {
            // Reset fade animation for next image only if not the last question
            fadeAnim.setValue(0);
            setCurrentIndex(prev => prev + 1);
        }
    }, [currentIndex, quizQuestions, answerMutation]);

    const panResponder = PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onPanResponderMove: (_, gesture) => {
            position.setValue({ x: gesture.dx, y: gesture.dy });
        },
        onPanResponderRelease: (_, gesture) => {
            if (gesture.dx > SWIPE_THRESHOLD) {
                forceSwipe("right");
            } else if (gesture.dx < -SWIPE_THRESHOLD) {
                forceSwipe("left");
            } else {
                resetPosition();
            }
        },
    });

    const forceSwipe = (direction: "right" | "left") => {
        const x = direction === "right" ? SCREEN_WIDTH : -SCREEN_WIDTH;
        Animated.timing(position, {
            toValue: { x, y: 0 },
            duration: 300,
            useNativeDriver: true,
        }).start(() => onSwipeComplete(direction));
    };

    const onSwipeComplete = (direction: "right" | "left") => {
        handleSwipe(direction);
        // Only reset position if not the last question
        if (currentIndex + 1 < quizQuestions.length) {
            position.setValue({ x: 0, y: 0 });
        }
    };

    const resetPosition = () => {
        Animated.spring(position, {
            toValue: { x: 0, y: 0 },
            useNativeDriver: true,
        }).start();
    };

    const getCardStyle = () => {
        const rotate = position.x.interpolate({
            inputRange: [-SCREEN_WIDTH * 1.5, 0, SCREEN_WIDTH * 1.5],
            outputRange: ["-120deg", "0deg", "120deg"],
        });

        return {
            transform: [
                { translateX: position.x },
                { translateY: position.y },
                { rotate }
            ],
        };
    };

    // Show preloader screen if quiz is not ready
    if (!isQuizReady) {
        return (
            <Container>
                <Stack.Screen
                    options={{
                        headerShown: false,
                    }}
                />
                <ImagePreloaderScreen
                    progress={progress}
                    isPreloading={isPreloading}
                    onRetry={retryFailedImages}
                />
            </Container>
        );
    }

    return (
        <Container>
            <Stack.Screen
                options={{
                    headerShown: false,
                }}
            />
            <Header>
                <CounterText>
                    {currentIndex + 1}/{quizQuestions.length}
                </CounterText>
                <HeaderTitleContainer>
                    <HeaderText>Swipe Your Style</HeaderText>
                </HeaderTitleContainer>
                <CloseButton onPress={() => router.push('/profile')}>
                    <Ionicons name="close" size={24} color="#333333" />
                </CloseButton>
            </Header>

            {/* Swipe Card with Fade Animation */}
            <Animated.View
                style={[
                    styles.card,
                    getCardStyle(),
                    { opacity: fadeAnim }
                ]}
                {...panResponder.panHandlers}
            >
                <Image
                    key={`image-${currentIndex}`}
                    source={{ uri: quizQuestions[currentIndex]?.image }}
                    style={styles.image}
                    resizeMode="cover"
                    onLoadStart={() => {
                        console.log(`🔄 Starting to load image ${currentIndex + 1}: ${quizQuestions[currentIndex]?.image}`);
                    }}
                    onLoad={() => {
                        console.log(`✅ Image ${currentIndex + 1} loaded successfully: ${quizQuestions[currentIndex]?.image}`);
                        Animated.timing(fadeAnim, {
                            toValue: 1,
                            duration: 200,
                            useNativeDriver: true,
                        }).start();
                    }}
                    onError={(error) => {
                        console.error(`❌ Image ${currentIndex + 1} failed to load:`, quizQuestions[currentIndex]?.image, error.nativeEvent);
                    }}
                />
            </Animated.View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
                <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => forceSwipe("left")}
                >
                    <DislikeIcon width={64} height={64} />
                </TouchableOpacity>
                <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => forceSwipe("right")}
                >
                    <LikeIcon width={64} height={64} />
                </TouchableOpacity>
            </View>
        </Container>
    );
}

const styles = StyleSheet.create({
    card: {
        width: 320,
        height: 400,
        borderRadius: 16,
        overflow: 'hidden',
    },
    image: {
        width: "100%",
        height: "100%",
    },
    actionButtons: {
        flexDirection: "row",
        justifyContent: "center",
        position: "absolute",
        bottom: 40,
        left: 0,
        right: 0,
        gap: 20,
    },
    actionButton: {
        alignItems: "center",
        justifyContent: "center",
    },
});