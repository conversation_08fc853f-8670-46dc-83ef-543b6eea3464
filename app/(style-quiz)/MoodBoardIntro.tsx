import React from 'react';
import { View, Image, Text, TouchableOpacity, ScrollView, Dimensions, Platform } from 'react-native';
import styled from 'styled-components/native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useRouter } from 'expo-router';
import { useFonts } from 'expo-font';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, runOnJS } from 'react-native-reanimated';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const IS_SMALL_DEVICE = SCREEN_WIDTH < 375 || SCREEN_HEIGHT < 667;

const Container = styled(Animated.View)`
  width: 100%;
  height: 100%;
  padding: ${IS_SMALL_DEVICE ? '40px 24px' : '68px 51px'};
  gap: ${IS_SMALL_DEVICE ? '16px' : '24px'};
  align-items: center;
`;

const Header = styled.View`
  width: 100%;
  align-items: center;
  position: relative;
`;

const Title = styled(Text)`
  font-family: CormorantGaramondSemiBold;
  font-size: ${IS_SMALL_DEVICE ? '28px' : '32px'};
  line-height: ${IS_SMALL_DEVICE ? '36px' : '40px'};
  text-align: center;
`;

const StyleTitle = styled(Title)`
  color: #0E7E61;
`;

const CloseButton = styled(TouchableOpacity)`
  position: absolute;
  top: ${Platform.OS === 'android' ? (IS_SMALL_DEVICE ? '40px' : '48px') : '68px'};
  right: 24px;
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
`;

const MoodboardImage = styled(Image)`
  width: ${IS_SMALL_DEVICE ? '280px' : '320px'};
  height: ${IS_SMALL_DEVICE ? '280px' : '320px'};
  resize-mode: contain;
`;

const Description = styled(Text)`
  width: ${IS_SMALL_DEVICE ? '280px' : '321px'};
  font-family: MuktaVaaniLight;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  color: #333333;
`;

const CTAButton = styled(TouchableOpacity)`
  align-items: center;
  margin-top: ${IS_SMALL_DEVICE ? '12px' : '20px'};
  padding: 16px;
`;

const CTAButtonText = styled(Text)`
  font-family: MuktaVaaniSemiBold;
  font-size: 16px;
  line-height: 24px;
  color: #0E7E61;
  text-align: center;
`;

const ArrowContainer = styled.View`
  margin-top: 8px;
`;

const ScrollContainer = styled(ScrollView)`
  flex: 1;
  width: 100%;
`;

const MoodBoardIntro = () => {
  const router = useRouter();
  const translateY = useSharedValue(0);

  const [fontsLoaded] = useFonts({
    CormorantGaramondSemiBold: require('@/assets/fonts/Cormorant-Garamond-Font/CormorantGaramond-SemiBold.ttf'),
    MuktaVaaniLight: require('@/assets/fonts/Mukta-Vaani-Font/MuktaVaani-Light.ttf'),
    MuktaVaaniSemiBold: require('@/assets/fonts/Mukta-Vaani-Font/MuktaVaani-SemiBold.ttf'),
  });

  const handleClose = () => {
    router.push('/profile');
  };

  const navigateToQuizComplete = () => {
    router.push('/(style-quiz)/StyleProfileIntro');
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const panGesture = Gesture.Pan()
    .onUpdate((e) => {
      if (e.translationY < 0) {
        translateY.value = e.translationY;
      }
    })
    .onEnd((e) => {
      if (e.translationY < -100) {
        translateY.value = withTiming(-SCREEN_HEIGHT, { duration: 300 });
        runOnJS(navigateToQuizComplete)();
      } else {
        translateY.value = withTiming(0);
      }
    });

  if (!fontsLoaded) {
    return null;
  }

  return (
    <View style={{ flex: 1 }}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <CloseButton onPress={handleClose}>
        <Ionicons name="close" size={24} color="#333333" />
      </CloseButton>
      <GestureDetector gesture={panGesture}>
        <Container style={animatedStyle}>
          <Header>
            <Title>Your current moodboard is</Title>
            <StyleTitle>Timeless with a Twist</StyleTitle>
          </Header>
          <MoodboardImage source={require('@/assets/images/mood-board/moodboard-intro-img.png')} />
          <Description>
            You blend classic elegance with contemporary flair. You're fond of enduring silhouettes and quality materials, while injecting them with unexpected details. This aesthetic creates a look that's sophisticated yet modern and timeless yet fresh, ensuring a wardrobe that's relevant and stylish for years to come.
          </Description>
          <CTAButton>
            <CTAButtonText>Swipe up to see looks that{'\n'}fit your current style</CTAButtonText>
            <ArrowContainer>
              <Ionicons name="arrow-up" size={24} color="#0E7E61" />
            </ArrowContainer>
          </CTAButton>
        </Container>
      </GestureDetector>
    </View>
  );
};

export default MoodBoardIntro;