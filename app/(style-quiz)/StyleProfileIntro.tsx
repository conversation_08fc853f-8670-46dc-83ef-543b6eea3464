import React from 'react';
import { View, Image, TouchableOpacity, Text, ScrollView } from 'react-native';
import styled from 'styled-components/native';
import { Stack, useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';

const Container = styled.ScrollView`
  flex: 1;
  background-color: #FFFFFF;
`;

const CloseButton = styled.TouchableOpacity`
  position: absolute;
  top: 48px;
  right: 24px;
  width: 32px;
  height: 32px;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-radius: 16px;
  z-index: 10;
`;

const ContentWrapper = styled.View`
  width: 343px;
  align-self: center;
  margin-top: 51px;
  padding-bottom: 58px;
  gap: 32px;
`;

const StyleImage = styled.Image`
  width: 343px;
  height: 343px;
  border-radius: 16px;
`;

const AddButton = styled.TouchableOpacity`
  width: 343px;
  height: 40px;
  background-color: #0E7E61;
  border-radius: 100px;
  justify-content: center;
  align-items: center;
`;

const ButtonText = styled.Text`
  font-family: MuktaVaaniSemiBold;
  font-size: 16px;
  color: #FFFFFF;
  text-align: center;
`;

export default function StyleProfileIntroScreen() {
    const router = useRouter();

    const handleAddToProfile = async () => {
        try {
            // Save style preference to indicate quiz completion
            await AsyncStorage.setItem('userStylePreference', 'timelessWithATwist');

            // Navigate to Profile tab
            router.push('/(tabs)/profile');
        } catch (error) {
            console.error('Error saving style preference:', error);
            router.push('/(tabs)/profile');
        }
    };

    const handleClose = () => {
        router.push('/profile');
    };

    return (
        <View style={{ flex: 1 }}>
            <Stack.Screen
                options={{
                    headerShown: false,
                }}
            />
            <CloseButton onPress={handleClose}>
                <Ionicons name="close" size={24} color="#333333" />
            </CloseButton>
            <Container showsVerticalScrollIndicator={false}>
                <ContentWrapper>
                <StyleImage
                    source={require('@/assets/images/style-pro-intro/style-profile-1.png')}
                    resizeMode="cover"
                />

                <StyleImage
                    source={require('@/assets/images/style-pro-intro/style-profile-2.png')}
                    resizeMode="cover"
                />

                <StyleImage
                    source={require('@/assets/images/style-pro-intro/style-profile-3.png')}
                    resizeMode="cover"
                />

                <AddButton onPress={handleAddToProfile}>
                    <ButtonText>Add to My Style Profile</ButtonText>
                </AddButton>
                </ContentWrapper>
            </Container>
        </View>
    );
}