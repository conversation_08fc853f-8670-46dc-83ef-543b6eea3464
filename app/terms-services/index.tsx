import LegalDocumentScreen from '@/components/LegalDocument';
import Colors from '@/constants/Colors';
import { privacyPolicy, termsAndServices } from '@/constants/strings';
import React from 'react';
import { View } from 'react-native';
import Modal from 'react-native-modal';
const TermsAndServices: React.FC = () => {

  const [modalIsVisible, setModalIsVisible] = React.useState(false);

  const handleLinkPress = (title: string) => {
    if (title === 'privacyPolicy') {
      setModalIsVisible(true);
    } else {
      // subscribe to plans
      console.log("Handle the Plans!")
    }
  };

  return (
    <>
      <LegalDocumentScreen
        firstSection={termsAndServices.firstSection}
        secondSection={termsAndServices.secondSection}
        title={termsAndServices.title}
        content={termsAndServices.content}
        onPress={handleLinkPress}
      />

      <Modal
        isVisible={modalIsVisible}
        onBackButtonPress={() => setModalIsVisible(false)}
        onBackdropPress={() => setModalIsVisible(false)}
        style={{ margin: 0 }}
       >
        <View style={{ flex: 1, backgroundColor: Colors.light.background, padding: 24 }}>
          <LegalDocumentScreen
            modal={modalIsVisible}
            title={privacyPolicy.title}
            content={privacyPolicy.content}
            onPress={() => setModalIsVisible(false)}
          />
        </View>
      </Modal>
    </>
  )
};

export default TermsAndServices;
