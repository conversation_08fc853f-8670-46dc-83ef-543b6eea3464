import { Accordion } from '@/components/Accordion';
import { ColorMatch } from '@/components/ColorMatch';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent from '@/components/common/Text';
import { STYLE_COLOR_MATCHES } from '@/constants/style-preferences';
import { View } from 'react-native';

export default function StyleColorMatch() {
  return (
    <>
      <HeaderPage noLogo />
      <View style={{ marginTop: 16 }}>
        <SectionTitle title="Style Color Match" />
      </View>
      <View style={{ gap: 24, marginTop: 24, marginBottom: 32 }}>
        <TextComponent string="Whether you love neutrals, pastels, bold hues, or monochrome looks, share your favorites so we can match your style effortlessly." />
        {STYLE_COLOR_MATCHES.map((item) => (
          <Accordion title={item.title} key={item.id}>
            <ColorMatch colors={item.colors} />
          </Accordion>
        ))}
      </View>
    </>
  );
}
