import DatePickerModal from '@/components/DatePickerModal';
import LocationAutocomplete from '@/components/LocationAutocomplete';
import Button from '@/components/common/Button';
import { Field, FieldGroup } from '@/components/common/Field';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent, { TextError } from '@/components/common/Text';
import GenericDropdown, { DropdownOption, GenericDropdownRef } from '@/components/GenericDropdown';
import { getUserProfile, updateUserProfile } from '@/methods/users';
import { formatDate } from '@/utils/tripHelpers';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import moment from 'moment';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useTheme } from 'styled-components';

type Location = {
  name: string;
  latitude: number;
  longitude: number;
};

export const NON_BINARY = 'Non-binary';
export const PREFER_NOT_TO_SAY = 'Prefer not to say';

const GENDER_OPTIONS: DropdownOption[] = [
  {
    label: 'Female',
    value: 'Female',
  },
  {
    label: 'Male',
    value: 'Male',
  },
  {
    label: NON_BINARY,
    value: NON_BINARY,
  },
  {
    label: PREFER_NOT_TO_SAY,
    value: PREFER_NOT_TO_SAY,
  },
];

const PersonalInformation = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [location, setLocation] = useState<Location>({
    name: '',
    latitude: 0,
    longitude: 0,
  });
  const [birthDate, setBirthDate] = useState(new Date());
  const [errorMessage, setErrorMessage] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [gender, setGender] = useState('');
  const [showGenderDropdown, setShowGenderDropdown] = useState(false);

  const nameInputRef = useRef<TextInput>(null);
  const homeCityAutocompleteRef = useRef<any>(null);
  const genderDropdownRef = useRef<GenericDropdownRef>(null);

  const { data: userProfile, isLoading: isLoadingProfile } = getUserProfile();
  const {
    mutate: updateProfile,
    isPending: isUpdatingProfile,
    isSuccess: isUpdateSuccess,
  } = updateUserProfile();

  const queryClient = useQueryClient();

  const theme = useTheme();

  useEffect(() => {
    console.log(gender);
  }, [gender]);

  useEffect(() => {
    console.log(userProfile);
    if (userProfile) {
      setName(userProfile.data?.profile?.name);
      setEmail(userProfile.data?.profile?.email);
      setLocation({
        name: userProfile.data?.profile?.cityName,
        latitude: userProfile.data?.profile?.latitude,
        longitude: userProfile.data?.profile?.longitude,
      });
      setBirthDate(userProfile.data?.profile?.birthDate);
      setGender(userProfile.data?.profile?.gender || PREFER_NOT_TO_SAY);
    }
  }, [userProfile]);

  const openDatePicker = useCallback(() => {
    setShowDatePicker(true);
  }, []);

  const handleBirthdayChange = useCallback((startDate?: Date) => {
    if (startDate) {
      setBirthDate(startDate);
      setShowDatePicker(false);
    }
  }, []);

  const handleCityChange = useCallback(
    (city: { name: string; latitude: number; longitude: number }) => {
      setLocation({
        name: city.name,
        latitude: city.latitude,
        longitude: city.longitude,
      });
    },
    [],
  );

  const handleGenderChange = useCallback((option: DropdownOption) => {
    setGender(option.value);
  }, []);

  const handleSaveChanges = useCallback(() => {
    if (!name) {
      setErrorMessage('Name is required');
      return;
    }

    updateProfile({
      name,
      cityName: location.name,
      latitude: location.latitude,
      longitude: location.longitude,
      birthDate: birthDate instanceof Date ? birthDate.toISOString() : birthDate,
      gender,
    });
  }, [name, location, birthDate, gender, updateProfile]);

  useEffect(() => {
    if (isUpdateSuccess) {
      queryClient.invalidateQueries({ queryKey: ['userProfile'] });
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      router.back();
    }
  }, [isUpdateSuccess]);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage noLogo />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Personal Information" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <TextComponent string="Change your personal information so other people will recognize you." />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <FieldGroup>
            <Field label="Name" onPress={() => nameInputRef.current?.focus()}>
              <TextInput
                ref={nameInputRef}
                style={styles.textInput}
                placeholder="Enter name"
                value={name}
                onChangeText={(text) => setName(text)}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field label="Email">
              <TextInput
                style={styles.textInput}
                value={email}
                editable={false}
              />
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field label="Home City" onPress={() => homeCityAutocompleteRef.current?.focus()} hasDropdown>
              <View
                style={{
                  width: '60%',
                  position: 'relative',
                  height: 30,
                }}
              >
                <LocationAutocomplete
                  ref={homeCityAutocompleteRef}
                  value={location.name}
                  onLocationSelect={(loc) => handleCityChange(loc)}
                />
              </View>
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field label="Birthday" onPress={openDatePicker}>
              <TouchableOpacity
                style={{
                  backgroundColor: theme.brand.green[500],
                  paddingVertical: 4,
                  paddingHorizontal: 8,
                  borderRadius: 4,
                }}
                onPress={openDatePicker}
              >
                <Text
                  style={{
                    color: '#fff',
                    fontSize: 16,
                    textAlign: 'center',
                    fontFamily: 'MuktaVaani',
                  }}
                >
                  {birthDate ? formatDate(birthDate) : 'Select birthday'}
                </Text>
              </TouchableOpacity>
            </Field>
          </FieldGroup>
          <FieldGroup>
            <Field label="Gender" onPress={() => {
              // Open the dropdown externally
              genderDropdownRef.current?.open();
            }}>
              <GenericDropdown
                ref={genderDropdownRef}
                options={GENDER_OPTIONS}
                selectedValue={gender}
                onSelect={handleGenderChange}
                placeholder="Select gender"
                buttonStyles={{
                  paddingRight: 0,
                }}
              />
            </Field>
          </FieldGroup>
          {errorMessage ? <TextError string={errorMessage} /> : null}
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title="Save changes"
          isDisabled={isLoadingProfile || isUpdatingProfile}
          onPress={handleSaveChanges}
        />
      </View>
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateChange={handleBirthdayChange}
        value={birthDate}
        maximumDate={moment().subtract(18, 'years').toDate()}
        title="Tell us your birthdate 🎂"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  textInput: {
    height: 30,
    width: '60%',
    fontFamily: 'MuktaVaani',
    fontSize: 16,
    padding: 0,
    textAlign: 'right',
  },
});

export default PersonalInformation;
