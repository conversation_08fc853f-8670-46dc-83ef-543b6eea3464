import { KeywordsList } from '@/components/KeywordsList';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { STYLE_KEYWORDS } from '@/constants/style-preferences';
import {
  getStylePreferences,
  updateStyleKeywords,
} from '@/methods/preferences';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { View } from 'react-native';

export default function StyleKeywords() {
  const queryClient = useQueryClient();
  const { data } = getStylePreferences();
  const { mutate: updateKeywords } = updateStyleKeywords();

  const onToggleKeyword = useCallback(
    (selectedTags: string[]) => {
      updateKeywords(
        { keywords: selectedTags },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
          },
        },
      );
    },
    [queryClient],
  );

  const selectedKeywords = data?.data?.stylePreferences?.keywords || [];

  return (
    <>
      <HeaderPage noLogo />
      <View style={{ marginTop: 16 }}>
        <SectionTitle title="Style Keywords" />
      </View>
      <View style={{ gap: 24, marginTop: 24 }}>
        <Text
          string={`Describe your style in a few key words! Think of words like 'minimalist', 'edgy', 'boho', 'classic', or 'streetwear'. These keywords will help us curate looks that truly match your vibe.`}
        />
        <Text string="Select up to 5 keywords" />
        <KeywordsList
          keywords={STYLE_KEYWORDS}
          selectedKeywords={selectedKeywords}
          onToggleKeyword={onToggleKeyword}
        />
      </View>
    </>
  );
}
