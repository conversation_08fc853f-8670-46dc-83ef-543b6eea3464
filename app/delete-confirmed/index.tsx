import Button from '@/components/common/Button';
import { DeleteConfirmed as DeleteConfirmedImage } from '@/constants/images';
import { router } from 'expo-router';
import React from 'react';
import { Image, SafeAreaView, StyleSheet, Text, View } from 'react-native';

const DeleteConfirmed = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.heading}>We're sad to see you leave</Text>
        <Text style={styles.contentText}>
          You can always come back and create your new account
        </Text>
        <View style={styles.imageContainer}>
          <Image
            source={DeleteConfirmedImage}
            style={{ width: '100%' }}
            resizeMode="contain"
          />
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <Button
          title="Okay"
          onPress={() => {
            router.push('/onboarding');
          }}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 50,
  },
  heading: {
    fontSize: 24,
    fontWeight: 500,
    marginBottom: 16,
  },
  contentText: {
    fontSize: 14,
    fontWeight: 300,
    textAlign: 'center',
  },
  imageContainer: {
    marginTop: 20,
    maxWidth: 200,
    width: '100%',
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
});

export default DeleteConfirmed;
