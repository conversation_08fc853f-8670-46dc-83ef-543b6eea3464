'use client'
import { ContainerStyles } from '@/components/common/Container/styles';
import HeaderPage from '@/components/common/HeaderPage';
import MyCalendarComponent from '@/components/MyCalendar';
import { getUpcomingTrips } from '@/methods/trips';
import { SafeAreaView } from 'react-native';
import { myCalendar } from '@/constants/strings';
interface Trip {
  startDate: string;
  endDate: string;
  styleDiaryDates: string[];
  cardColor: string;
  name: string;
}
export default function MyCalendar() {
  const { data: trips, refetch, isLoading } = getUpcomingTrips();
  const today = new Date().toISOString().split('T')[0];
  const formatDate = (date: string) => {
    const [month, day, year] = date.split('-');
    return `${year}-${month}-${day}`;
  };
  const tripsData = trips?.data?.events;

  const toExactUTCDate = (date: Date) => {
    // This way what you select is exactly what gets stored, no surprises from UTC conversions.
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, "0");
    const d = String(date.getDate()).padStart(2, "0");
    return `${y}-${m}-${d}`;
  };
  const processedTrips = tripsData?.map((trip: Trip) => {
    const start = toExactUTCDate(new Date(trip.startDate));
    const dates = trip.styleDiaryDates.map(formatDate)
    // remove start date from dates array bec. API gave start date converted to UTC
    const startWithUTC = new Date(trip.startDate).toISOString().split('T')[0];
    dates.splice(dates.indexOf(startWithUTC), 1);

    return {
      start,
      end: new Date(trip.endDate).toISOString().split('T')[0],
      dates,
      color: trip.cardColor,
      name: trip.name
    }
  });

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ContainerStyles>
        <HeaderPage noLogo title={myCalendar.title} />
      </ContainerStyles>
      <MyCalendarComponent trips={processedTrips} today={today} />
    </SafeAreaView>

  )
}