import OnboardingItem from '@/components/common/OnboardingItem';
import Paginator from '@/components/common/Paginator';
import { OnboardingGradient } from '@/constants/images';
import { useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Animated,
  BackHandler,
  Easing,
  FlatList,
  Image,
  Platform,
  StyleSheet,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import slides from './slides';
import Button from '@/components/common/Button';
import { router } from 'expo-router';

export default function Onboarding() {
  const scrollX = useRef(new Animated.Value(0)).current;
  const slidesRef = useRef(null);

  const spinValue = new Animated.Value(0);

  Animated.loop(
    Animated.timing(spinValue, {
      toValue: 1,
      duration: 5000,
      easing: Easing.linear,
      useNativeDriver: true,
    }),
  ).start();

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  useEffect(() => {
    // Prevent hardware back navigation
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', () => {
        return true;
      });
    }
  }, []);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: '#ffffff',
      }}
    >
      <SafeAreaView edges={['top', 'bottom']} style={{ flex: 1 }}>
        <View style={{ flex: 2, justifyContent: 'center' }}>
          <View>
            <View style={styles.gradientContainer}>
              <Animated.Image
                style={[styles.gradient, { transform: [{ rotate: spin }] }]}
                source={OnboardingGradient}
              />
            </View>
            <FlatList
              ref={slidesRef}
              data={slides}
              renderItem={({ item }) => <OnboardingItem item={item} />}
              horizontal
              showsHorizontalScrollIndicator={false}
              pagingEnabled
              bounces={false}
              scrollEventThrottle={32}
              keyExtractor={(item) => item.id.toString()}
              onScroll={Animated.event(
                [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                { useNativeDriver: false },
              )}
            />
          </View>
          <View>
            <Paginator data={slides} scrollX={scrollX} />
          </View>
        </View>
        <View style={styles.buttons}>
          <View>
            <Button title="Sign Up" onPress={() => router.push('/sign-up')} />
          </View>
          <View>
            <Button
              title="Sign In"
              onPress={() => router.push('/sign-in')}
              isLined
            />
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  gradientContainer: {
    display: 'flex',
    alignItems: 'center',
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  gradient: {
    flex: 1,
    top: -90,
    aspectRatio: 1,
  },
  buttons: {
    flexBasis: 140,
    justifyContent: 'flex-end',
    gap: 8,
    width: '90%',
    height: 104,
    marginLeft: 'auto',
    marginRight: 'auto',
    paddingBottom: 20,
  },
});
