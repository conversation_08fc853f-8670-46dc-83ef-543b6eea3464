
import OnlineShopping from '@/assets/svg/online-shopping.svg';
import AddNewItemStyleModal from '@/components/AddNewItemStyleModal';
import ItemSelector, { Item } from '@/components/ItemSelector';
import LoadingOverlay from '@/components/LoadingOverlay';
import { Canvas } from '@/components/Packinglist/components/canvas';
import { DraggableResizeableItem } from '@/components/Packinglist/components/dragableItem';
import ZIndexControls from '@/components/Packinglist/components/zIndexControl';
import { HeaderText, TemplateModalHeader, TemplateModalTitle } from '@/components/TemplateListModal/styles';
import Button from '@/components/common/Button';
import { styleDiary } from '@/constants/strings';
import { UploadImage, uploadImageToS3 } from '@/methods/cloths';
import { createStyleDiary } from '@/methods/style-diaries';
import { getPackingList } from '@/methods/trips';
import { makeImageFromView } from '@shopify/react-native-skia';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';

interface ItemChange {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
}
interface EventActivity {
  name: string;
  eventId: string;
  id: string;
  image: string;
}
interface StyleDiaryProps {
  eventId: string;
  activeStyleDiary: any;
  selectedEventActivity?: EventActivity;
  tripName?: string;
}
interface CanvasItem {
  id: string;
  source: string;
  x: number;
  y: number;
  width: number;
  height: number;
  type: string;
  zIndex: number;
}

export default function StylingCanvas() {
  const { eventId, activeStyleDiary, selectedEventActivity, tripName } = useLocalSearchParams() as unknown as StyleDiaryProps;

  // Parse back to objects
  const parsedDiary = activeStyleDiary ? JSON.parse(activeStyleDiary as string) : null;
  const parsedActivity = selectedEventActivity ? JSON.parse(selectedEventActivity as unknown as string) : null;

  // Calls
  const { data: packingList, refetch } = getPackingList(eventId as string);
  const { mutate: createStyleDiaryMutation } = createStyleDiary();
  const {
    mutate: uploadImageMutation,
    isSuccess: isImageUploaded,
    data: imageData,
  } = UploadImage();
  const {
    mutate: uploadImageToS3Mutation,
    isSuccess: isImageUploadedToS3,
    data: imageDataToS3,
  } = uploadImageToS3();
  // States
  const [image, setImage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [availableItems, setAvailableItems] = useState<Item[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [categories, setCategories] = useState<string[]>([]);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);
  const [canvasItems, setCanvasItems] = useState<CanvasItem[]>(parsedDiary?.items || []);
  const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);
  const [isAddNewItemVisible, setAddNewItemVisible] = useState(false);
  const [preSelectedImage, setPreSelectedImage] = useState<{ uri: string; base64?: string } | undefined>(undefined);
  const [search, setSearch] = useState('');

  // Reference to the canvas for capturing as image
  const canvasRef = useRef<View>(null) as React.RefObject<View>;

  useEffect(() => {
    refetch();
  }, []);

  // Effects
  useEffect(() => {
    if (packingList?.data?.packingList?.length > 0) {
      const Items: Item[] = [];
      const foundCategories = new Set<string>();
      packingList?.data?.packingList.forEach((category: any) => {
        if (category.type === 'category' && category.name) {
          foundCategories.add(category.name);
        }
        category.items.forEach((item: any) => {
          Items.push({
            category: category.name,
            id: item._id,
            name: item.name,
            source: item.imageUrl,
            type: item.type || 'Others', // Default to 'Others' if type is not specified
          });
        });
      });
      setAvailableItems(Items);
      setCategories(Array.from(foundCategories));
    }
  }, [packingList]);

  // Handle successful pre-signed URL generation
  useEffect(() => {
    if (isImageUploaded && imageData?.preSignedURL) {
      uploadImageToS3Mutation(
        {
          imageUrl: image,
          preSignedUrl: imageData.preSignedURL,
        },
        {
          onSuccess: (response) => {
            console.log('success from uploadImageToS3Mutation', JSON.stringify( response));
          },
          onError: (error) => {
            console.log('error from uploadImageToS3Mutation', JSON.stringify(error));
            setIsLoading(false);
          },
        },
      );
    }
  }, [isImageUploaded, imageData]);

  // Handle successful S3 upload
  useEffect(() => {
    if (isImageUploadedToS3 && imageDataToS3 && imageData?.fileURL) {
      createStyleDiaryMutation(
        {
          styleDiaryId: JSON.parse(activeStyleDiary)._id,
          items: canvasItems,
          imageURL: imageData.fileURL,
        },
        {
          onSuccess: (response) => {
            console.log('success from createStyleDiaryMutation', JSON.stringify(response));
            setIsLoading(false);
            setIsSuccessModalVisible(true);
          },
          onError: (error) => {
            console.log('error from createStyleDiaryMutation', JSON.stringify(error));
            setIsLoading(false);
          },
        },
      );
    }
  }, [isImageUploadedToS3, imageDataToS3, imageData]);

  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    const [month, day, year] = dateStr.split("-").map(Number);
    const date = new Date(year, month - 1, day);
    const weekday = date.toLocaleDateString("en-US", { weekday: "long" });
    const monthName = date.toLocaleDateString("en-US", { month: "long" });
    return `${weekday}, ${day} ${monthName}`;
  };
  const sortedCanvasItems = [...canvasItems].sort((a, b) => a.zIndex - b.zIndex);
  const highestZIndex = canvasItems.reduce((max: number, item: CanvasItem) => Math.max(max, item.zIndex || 1), 0);
  // Add item to canvas
  const handleAddItem = (item: Item) => {
    const newItem: CanvasItem = {
      id: Date.now().toString(),
      source: item.source,
      x: 50, // Initial position
      y: 50,
      width: 100, // Initial size
      height: 100,
      type: item.type,
      zIndex: highestZIndex + 1, // Place on top
    };
    setCanvasItems([...canvasItems, newItem]);
    // Select the newly added item
    setSelectedItemIndex(canvasItems.length);
  };
  // Update item position and size
  const handleItemChange = (index: number, changes: ItemChange) => {
    const updatedItems = [...canvasItems];
    updatedItems[index] = {
      ...updatedItems[index],
      x: changes.x !== undefined ? changes.x : updatedItems[index].x,
      y: changes.y !== undefined ? changes.y : updatedItems[index].y,
      width: changes.width !== undefined ? changes.width : updatedItems[index].width,
      height: changes.height !== undefined ? changes.height : updatedItems[index].height,
    };
    setCanvasItems(updatedItems);
  };

  // Delete selected item
  const handleDeleteItem = () => {
    if (selectedItemIndex !== null) {
      const updatedItems = canvasItems.filter((_: CanvasItem, index: number) => index !== selectedItemIndex);
      setCanvasItems(updatedItems);
      setSelectedItemIndex(null);
    }
  };

  const handleBringForward = () => {
    if (selectedItemIndex === null) return;
    const updatedItems = [...canvasItems];
    const currentItem = updatedItems[selectedItemIndex];
    // Find the item with the next highest z-index
    const itemsAbove = updatedItems.filter((item, index) => index !== selectedItemIndex && item.zIndex > currentItem.zIndex);
    if (itemsAbove.length === 0) return; // Already at the top
    // Sort by z-index to find the next one up
    itemsAbove.sort((a, b) => a.zIndex - b.zIndex);
    const nextItemUp = itemsAbove[0];
    // Swap z-indices
    const tempZIndex = currentItem.zIndex;
    updatedItems[selectedItemIndex].zIndex = nextItemUp.zIndex;
    // Find the index of the next item up
    const nextItemIndex = updatedItems.findIndex((item) => item.id === nextItemUp.id);
    updatedItems[nextItemIndex].zIndex = tempZIndex;
    setCanvasItems(updatedItems);
  };

  // Send the selected item backward (decrease z-index)
  const handleSendBackward = () => {
    if (selectedItemIndex === null) return;
    const updatedItems = [...canvasItems];
    const currentItem = updatedItems[selectedItemIndex];
    // Find the item with the next lowest z-index
    const itemsBelow = updatedItems.filter((item, index) => index !== selectedItemIndex && item.zIndex < currentItem.zIndex);
    if (itemsBelow.length === 0) return; // Already at the bottom
    // Sort by z-index in descending order to find the next one down
    itemsBelow.sort((a, b) => b.zIndex - a.zIndex);
    const nextItemDown = itemsBelow[0];
    // Swap z-indices
    const tempZIndex = currentItem.zIndex;
    updatedItems[selectedItemIndex].zIndex = nextItemDown.zIndex;
    // Find the index of the next item down
    const nextItemIndex = updatedItems.findIndex((item) => item.id === nextItemDown.id);
    updatedItems[nextItemIndex].zIndex = tempZIndex;
    setCanvasItems(updatedItems);
  };

  const captureView = async (canvas: React.RefObject<View>) => {
    if (!canvas.current) return;
    try {
      setIsLoading(true);
      setSelectedItemIndex(null);
      // wait for sec
      await new Promise((resolve) => setTimeout(resolve, 500));
      const image = await makeImageFromView(canvas);
      const base64Image = image?.encodeToBase64();
      if (!base64Image) {
        console.error('Failed to encode image to base64');
        setIsLoading(false);
        return;
      }
      setImage(base64Image);
      uploadImageMutation({
        fileName: `style-diary-${Date.now()}`,
        fileType: 'image/jpeg',
        folderPath: 'styleDiaries',
        imageUrl: base64Image,
      });
    } catch (error) {
      console.error('Error capturing view with Skia:', error);
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <View style={styles.container}>
      <TemplateModalHeader style={{ marginTop: 50 }}>
        <TouchableOpacity onPress={() => router.back()}>
          <HeaderText>{styleDiary.close}</HeaderText>
        </TouchableOpacity>
        <TemplateModalTitle>{styleDiary.styling}: {tripName}</TemplateModalTitle>
        <View style={{ width: 50 }} />
      </TemplateModalHeader>
      <ScrollView style={{ marginTop: 20, marginBottom: 150 }} showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        <LoadingOverlay visible={isLoading} />
        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>{formatDate(parsedDiary.date)}, {parsedActivity?.name}</Text>
        </View>
        <ItemSelector
          items={availableItems}
          onSelectItem={handleAddItem}
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
          categories={categories}
          handleNewItem={() => setAddNewItemVisible(true)}
        />
        <View ref={canvasRef} collapsable={false}>
          <Canvas showGrid={false}>
            {sortedCanvasItems.map((item, index) => (
              <DraggableResizeableItem
                key={item.id}
                source={item.source}
                initialX={item.x}
                initialY={item.y}
                initialWidth={item.width}
                initialHeight={item.height}
                isSelected={selectedItemIndex === index}
                onSelect={() => setSelectedItemIndex(index)}
                onPositionChange={(position) => handleItemChange(index, position)}
                onSizeChange={(changes) => handleItemChange(index, changes)}
              />
            ))}
          </Canvas>
        </View>
        <ZIndexControls
          onBringForward={handleBringForward}
          onSendBackward={handleSendBackward}
          onDelete={handleDeleteItem}
          disabled={selectedItemIndex === null}
        />
        <Button
          isLined
          title={styleDiary.deleteOutfit}
          onPress={() => router.back()}
          isDisabled={isLoading}
        />

      </ScrollView>
      <View style={styles.bottomContainer}>
        <Button
          title={styleDiary.saveOutfit}
          onPress={() => {
            // setIsSuccessModalVisible(true);
            //  setSelectedItemIndex(null);
            Alert.alert(
              styleDiary.alertTitle,
              styleDiary.alertDescription,
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Save', onPress: () => captureView(canvasRef) },
              ],
              { cancelable: true },
            );
          }}
          isDisabled={isLoading}
        />
      </View>
      {/* Modal for success */}
      <Modal
        style={styles.modal}
        isVisible={isSuccessModalVisible}
        onBackButtonPress={() => setIsSuccessModalVisible(false)}
        onBackdropPress={() => setIsSuccessModalVisible(false)}
      >
        <View style={styles.modalContent}>
          <Text style={styles.modalText}>{styleDiary.successMsg} {parsedActivity?.name}!</Text>
          <Text style={styles.modalText1}>{styleDiary.successMsg1 + tripName + styleDiary.successMsg2}</Text>
          <OnlineShopping />
          <Button
            style={{ marginTop: 40, marginBottom: 20 }}
            title="Done"
            onPress={() => {
              setIsSuccessModalVisible(false);
              setTimeout(() => {
                router.back();
              }, 500);
            }}
          />
        </View>
      </Modal>
      {/* modal for add new  item */}
      <AddNewItemStyleModal
        isAddNewItemVisible={isAddNewItemVisible}
        setAddNewItemVisible={setAddNewItemVisible}
        search={search}
        setSearch={setSearch}
        tripName={tripName || ''}
        setPreSelectedImage={setPreSelectedImage}
        setImage={setImage}
      />
    </View>
  )
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    padding: 20,
    backgroundColor: '#F0F0F0',
  },
  dateContainer: {
    alignItems: 'center',
    padding: 10,
  },
  dateText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 18,
  },
  bottomContainer: {
    height: '15%',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 50,
    backgroundColor: '#FFF',
    padding: 10,
    justifyContent: 'flex-end'
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0
  },
  modalContent: {
    alignItems: 'center',
    backgroundColor: '#FFF',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20
  },
  modalText: {
    width: '70%',
    textAlign: 'center',
    fontFamily: 'MuktaVaani-Bold',
    fontSize: 20,
    marginBottom: 10
  },
  modalText1: {
    width: '95%',
    textAlign: 'center',
    fontSize: 16,
    marginBottom: 10
  }
});
