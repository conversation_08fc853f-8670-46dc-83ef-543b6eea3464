import GoogleIcon from '@/assets/images/social-media-icons/google.svg';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import { SignUpButton } from '@/components/common/SignUpButton';
import { brand } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import { AppleIcon, MailIcon } from 'lucide-react-native';
import { useCallback, useEffect } from 'react';
import { Alert, BackHandler, Platform, Text, View } from 'react-native';
import * as AppleAuthentication from 'expo-apple-authentication';
import { signInWithApple } from '@/methods/users';
import Meteor from '@meteorrn/core';

// import {
//   GoogleSignin,
//   isSuccessResponse,
//   statusCodes,
// } from '@react-native-google-signin/google-signin';


export default function SignUp() {
  const router = useRouter();
  const { mutate: signInWithAppleMutation, isPending, data: appleData, error } = signInWithApple();


  useEffect(() => {
    console.log('appleData', appleData);
    if (appleData?.success === true) {
      console.log('appleData', appleData);
      Meteor._loginWithToken(appleData.data.token);
      router.push('/(tabs)/home');
    }
  }, [appleData]);

  const handleSignUpWithGoogle = useCallback(async () => {
    // try {
    //   await GoogleSignin.hasPlayServices();
    //   const response = await GoogleSignin.signIn();
    //   if (isSuccessResponse(response)) {
    //     console.log('response', response);
    //   } else {
    //     // sign in was cancelled by user
    //   } 
    // } catch (error) {
    //   console.log('error', error);
    // }
  }, []);

  const handleSignUpWithApple = useCallback(async () => {
    // router.push('/sign-up/sign-up-process');
    // //add temporary disable
    try {

      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      console.log('credential', credential);

      signInWithAppleMutation(credential);

      // signed in
    } catch (e: any) {
      if (e.code === 'ERR_REQUEST_CANCELED') {
        // handle that the user canceled the sign-in flow
      } else {
        // handle other errors
        console.log('error', e);
      }
    }
  }, []);

  useEffect(() => {
    // Prevent hardware back navigation
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', () => {
        return true;
      });
    }
  }, []);

  return (
    <View>
      <HeaderPage disableBack={true} />
      <View style={{ marginTop: 16 }}>
        <SectionTitle title="Sign Up" />
      </View>
      <View style={{ marginTop: 32, gap: 16 }}>
        <SignUpButton
          icon={<GoogleIcon />}
          title="Continue with Google"
          onPress={handleSignUpWithGoogle}
        />
        <SignUpButton
          icon={<AppleIcon size={24} color={'#333'} />}
          title="Continue with Apple"
          onPress={handleSignUpWithApple}
        />
        <SignUpButton
          icon={<MailIcon size={24} color={'#333'} />}
          title="Continue with Email"
          onPress={() => router.push('/sign-up/email')}
        />
      </View>
      <View style={{ marginTop: 32, gap: 16, alignItems: 'center' }}>
        <Text style={{ fontFamily: 'MuktaVaani', fontSize: 14 }}>
          Already have an account?{' '}
          <Text
            style={{
              color: brand.green[500],
              textDecorationLine: 'underline',
            }}
            onPress={() => router.push('/sign-in')}
          >
            Sign In
          </Text>
        </Text>
      </View>
    </View>
  );
}
