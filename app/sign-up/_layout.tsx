import Container from '@/components/common/Container';
import * as AppleAuthentication from 'expo-apple-authentication';
// import { auth } from '@/config/FirebaseConfig';
import { Slot, Stack } from 'expo-router';
// import { createUserWithEmailAndPassword } from 'firebase/auth';
import {
  Dispatch,
  SetStateAction,
  createContext,
  useCallback,
  useState
} from 'react';

type UserDetails = {
  email: string;
  password: string;
};

type SignUpContextType = {
  userDetails: UserDetails | null;
  setUserDetails: Dispatch<SetStateAction<UserDetails>>;
};

export const SignUpContext = createContext<SignUpContextType | null>(null);

export default function SignUpLayout() {
  const [userDetails, setUserDetails] = useState<UserDetails>({
    email: '',
    password: '',
  });

  const onCreateAccount = useCallback(() => {

  }, []);

  return (
    <SignUpContext.Provider value={{ userDetails, setUserDetails }}>
      <Container>
        <Slot />
      </Container>
    </SignUpContext.Provider>
  );
}
