import Button from '@/components/common/Button';
import ControlledInput from '@/components/common/ControlledInput';
import HeaderPage from '@/components/common/HeaderPage';
import ProgressBar from '@/components/common/ProgressBar';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { router } from 'expo-router';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { View } from 'react-native';
import { verifyCode } from '@/methods/users';
import { SignUpContext } from './_layout';

export default function SignUpVerification() {

  const verifyCodeMutation = verifyCode();

  const [progress, setProgress] = useState(2);
  const [resendCounter, setResendCounter] = useState(30); // 30 seconds countdown

  const signupContext = useContext(SignUpContext);
  const userDetails = signupContext?.userDetails;
  
  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm({
    mode: 'onChange',
  })

  useEffect(() => {
    if (resendCounter > 0) {
      const timer = setInterval(() => {
        setResendCounter((prevCounter) => prevCounter - 1);
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [resendCounter]);


  useEffect(() => {
    if (verifyCodeMutation?.data?.success) {
      router.push('/sign-up/password');
    }
  }, [verifyCodeMutation.data]);

  const handleNext = useCallback((data: any) => {
    if(data.verificationCode) {
      userDetails &&
      verifyCodeMutation.mutate({ code: data.verificationCode, email: userDetails.email });
    }
  }, []);

  const handleResend = () => {
    // Logic to resend the verification code
    setResendCounter(30); // Reset the counter after resending
  };

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage rightComponent={<Text string={`${progress} / 3`} />} />
        <View style={{ marginTop: 16 }}>
          <ProgressBar progress={(progress / 3) * 100} />
        </View>
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Verification" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <Text string="Sign up now to be your own muse and elevate your fashion game!" />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <ControlledInput
            control={control}
            name="verificationCode"
            rules={{
              required: 'Verification code is required',
            }}
            placeholder="Verification code"
            error={errors?.verificationCode?.message as string || (!verifyCodeMutation?.data?.success ? verifyCodeMutation?.data?.message as string : '')}
          />
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          isLined
          title={`Resend ${resendCounter > 0 ? `(${resendCounter}s)` : ''}`}
          onPress={handleResend}
          isDisabled={resendCounter > 0}
        />
        <Button
          title={verifyCodeMutation.isPending ? 'Verifying...' : 'Next'}
          onPress={handleSubmit(handleNext)}
          isDisabled={!isValid || verifyCodeMutation.isPending}
        />
      </View>
    </View>
  );
}
