import Button from '@/components/common/Button';
import ControlledInput from '@/components/common/ControlledInput';
import HeaderPage from '@/components/common/HeaderPage';
import ProgressBar from '@/components/common/ProgressBar';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { brand } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import { Square, SquareCheck } from 'lucide-react-native';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { TouchableOpacity, View } from 'react-native';
import { SignUpContext } from './_layout';
import { emailVerification } from '@/methods/users';

export default function SignUpProcess() {
  const signupContext = useContext(SignUpContext);
  const userDetails = signupContext?.userDetails;
  const setUserDetails = signupContext?.setUserDetails;

  const router = useRouter();

  const [email, setEmail] = useState(userDetails?.email || '');
  const [progress, setProgress] = useState(1);
  const [isChecked, setIsChecked] = useState(false);
  const [error, setError] = useState('');
  const emailVerificationMutation = emailVerification();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm({
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (emailVerificationMutation.data) {
      if (emailVerificationMutation.data.success) {
        setError('');
        router.push('/sign-up/verification')
      } else {
        setError(emailVerificationMutation.data.message || 'Something went wrong');
      }
    }
  }, [emailVerificationMutation.data])

  const handleNext = useCallback(
    (data: any) => {
      // console.log(data);
      // setProgress(progress + 1);
      console.log(data.email);
      setError('');

      if (data.email) {
        console.log('email');
        const res = emailVerificationMutation.mutate(data.email);

        userDetails &&
          setUserDetails &&
          setUserDetails({
            ...userDetails,
            email: data.email,
          });

        console.log(res, 'no');
      }
    },
    [email],
  );

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage rightComponent={<Text string={`${progress} / 3`} />} />
        <View style={{ marginTop: 16 }}>
          <ProgressBar progress={(progress / 3) * 100} />
        </View>
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Email" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <Text string="Sign up now to be your own muse and elevate your fashion game!" />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <ControlledInput
            control={control}
            isDisabled={emailVerificationMutation.isPending}
            name="email"
            rules={{
              required: 'Email is required',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Invalid email address',
              },
            }}
            placeholder="Email"
            error={error || errors?.email?.message as string}
            onInputChange={setEmail}
            defaultValue={email}
          />
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <TouchableOpacity
          style={{ marginTop: 32, gap: 10, flexDirection: 'row' }}
          onPress={() => setIsChecked(!isChecked)}
          activeOpacity={0.8}
        >
          {isChecked ? (
            <SquareCheck size={24} color={brand.green[300]} />
          ) : (
            <Square size={24} color={brand.green[300]} />
          )}
          <View style={{ flex: 1 }}>
            <Text string="By proceeding, I accept the Terms and Conditions and Privacy Policy" />
          </View>
        </TouchableOpacity>
        <Button
          title={emailVerificationMutation.isPending ? 'Sending...' : 'Next'}
          onPress={handleSubmit(handleNext)}
          isDisabled={!isValid || !isChecked || emailVerificationMutation.isPending}
        />
      </View>
    </View>
  );
}
