import Button from '@/components/common/Button';
import ControlledInput from '@/components/common/ControlledInput';
import HeaderPage from '@/components/common/HeaderPage';
import ProgressBar from '@/components/common/ProgressBar';
import SectionTitle from '@/components/common/SectionTitle';
import Text, { TextError } from '@/components/common/Text';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { View, Alert, TouchableOpacity } from 'react-native';
import { signUp, updateUserProfile } from '@/methods/users';
import Meteor from '@meteorrn/core';
import { Ionicons } from '@expo/vector-icons';

import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { SignUpContext } from './_layout';
import { router } from 'expo-router';
import { useSession } from '@/config/ctx';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Password must contain at least one number or one special character and no spaces
const passwordRegex = /^(?=.*[!@#$%^&*(),.?":{}|<>]|.*\d)(?!.*\s).+$/;

const passwordSchema = yup.object().shape({
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters long')
    .matches(
      passwordRegex,
      'Password must contain at least one letter and one number',
    ),
});

export default function SignUpPassword() {
  const [progress, setProgress] = useState(3);
  const [showPassword, setShowPassword] = useState(false);
  const { signIn } = useSession();
  const signUpMutation = signUp();
  const { mutate: updateProfile } = updateUserProfile();

  const signupContext = useContext(SignUpContext);
  const userDetails = signupContext?.userDetails;

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(passwordSchema),
    defaultValues: {
      password: '',
    },
  });

  const passwordValue = watch('password');

  const loginWithToken = async (token: string) => {
    console.log('Attempting to login with token:', token);
    try {
      await Meteor._loginWithToken(token);
      console.log('Login with token result - User ID:', Meteor.userId());
      console.log('Login with token result - Connection:', Meteor.status());

      // Check stored token
      const storedToken = await AsyncStorage.getItem('reactnativemeteor_usertoken');
      console.log('Stored token after login:', storedToken);

      router.push('/user-info/gender');
    } catch (error) {
      console.error('Login with token error:', error);
    }
  };

  useEffect(() => {
    console.log('Sign up mutation data:', signUpMutation.data);
    if (signUpMutation?.data?.success) {
      // Use the email's username as the user's username
      const userName = userDetails?.email.split('@')[0];
      console.log('Updating profile with username:', userName);
      updateProfile({
        userName,
      });

      console.log('Sign up success - token:', signUpMutation.data.data.token);
      loginWithToken(signUpMutation.data.data.token);
    }
  }, [signUpMutation.data]);

  const handleNext = useCallback((data: any) => {
    console.log('Handle Next - Form data:', data);

    const { minLengthValid, patternValid } = isPasswordValid(data.password);

    if (!minLengthValid || !patternValid) {
      Alert.alert('Password is not valid');
      return;
    }

    if (userDetails && data.password) {
      console.log('Attempting signup with:', {
        email: userDetails.email,
        passwordLength: data.password.length
      });
      signUpMutation.mutate({
        password: data.password,
        email: userDetails.email,
      });
    }
  }, []);

  const isPasswordValid = (password: string) => {
    const minLengthValid = password?.length >= 8;
    const patternValid = passwordRegex.test(password);
    return { minLengthValid, patternValid };
  };

  const { minLengthValid, patternValid } = isPasswordValid(passwordValue);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage rightComponent={<Text string={`${progress} / 3`} />} />
        <View style={{ marginTop: 16 }}>
          <ProgressBar progress={(progress / 3) * 100} />
        </View>
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Create Password" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <Text string="Sign up now to be your own muse and elevate your fashion game!" />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <View style={{ position: 'relative' }}>
            <ControlledInput
              isDisabled={signUpMutation.isPending}
              control={control}
              name="password"
              rules={{
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters long',
                },
                pattern: {
                  value: passwordRegex,
                  message:
                    'Password must contain at least one letter and one special character or number, and no spaces',
                },
              }}
              placeholder="Password"
              secureTextEntry={!showPassword}
            />
          </View>
          <View style={{ marginTop: 8 }}>
            <TextError
              string=" • Password must be at least 8 characters long"
              isValid={minLengthValid}
            />
            <TextError
              string=" • Must have at least one special character or number."
              isValid={patternValid}
            />
          </View>
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          title={signUpMutation.isPending ? 'Signing up...' : 'Next'}
          onPress={handleSubmit(handleNext)}
          isDisabled={!isValid || signUpMutation.isPending}
        />
      </View>
    </View>
  );
}
