import LogoWhite from '@/assets/images/logos/logo-white.svg';
import { brand } from '@/constants/Colors';
import Meteor from '@meteorrn/core';
import {
  Blend,
  Canvas,
  RadialGradient,
  Rect,
  vec,
} from '@shopify/react-native-skia';
import { useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import {
  BackHandler,
  Platform,
  StyleSheet,
  View,
  useWindowDimensions,
} from 'react-native';
import {
  Easing,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

export default function SplashScreen() {
  const router = useRouter();

  const { width, height } = useWindowDimensions();

  const radialGradientColors1 = useDerivedValue(() => {
    return [brand.green[300], '#FFF'];
  });

  const radialGradientColors2 = useDerivedValue(() => {
    return [brand.magenta, '#FFF'];
  });

  const radialGradientColors3 = useDerivedValue(() => {
    return [brand.orange, '#FFF'];
  });

  const radialGradientColors4 = useDerivedValue(() => {
    return [brand.skyBlue, '#FFF'];
  });

  const angleValue = useSharedValue(0);

  // Derived value to trigger re-render when angleValue changes

  const animatedAngle = useDerivedValue(() => {
    return angleValue.value;
  });

  // Shared values for radii to enable smooth transitions
  const radii = [
    useSharedValue(600),
    useSharedValue(600),
    useSharedValue(600),
    useSharedValue(600),
    useSharedValue(600),
  ];

  useEffect(() => {
    // Use withRepeat to animate angleValue in and out
    angleValue.value = withRepeat(
      withTiming(10, {
        duration: 10000,
        easing: Easing.inOut(Easing.quad),
      }),
      -1, // -1 for infinite repeats
      true, // reverse direction on each repeat
    );

    const interval = setInterval(() => {
      // Randomize the radii with smoother transitions
      radii.forEach((radius, i) => {
        const randomRadius = Math.random() * 50 + 600;
        radius.value = withTiming(randomRadius, {
          duration: 200,
          easing: Easing.inOut(Easing.quad),
        });
      });
    }, 2000);

    setTimeout(() => {
      // Log authentication state
      console.log('Splash Screen Auth Check:', {
        userId: Meteor.userId(),
        connectionStatus: Meteor.status(),
        isConnected: Meteor.status().connected,
        availableRoutes: router.canGoBack()
      });

      // if user is logged in, redirect to home
      if (Meteor.userId()) {
        console.log('User is logged in, attempting navigation to home');
        try {
          router.replace('/(tabs)/home/');  // Added trailing slash and explicit path
          console.log('Navigation completed');
        } catch (error) {
          console.error('Navigation error:', error);
        }
      } else {
        console.log('No user found, redirecting to onboarding');
        router.replace('/onboarding');
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [angleValue]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: animatedAngle.value,
    };
  });

  // Function to calculate position of each circle
  const calculateCirclePosition = (
    index: number,
    innerRadius: number,
    offset: number = 0,
  ) => {
    // Use a fixed random offset for each circle to ensure consistent positioning
    const randomOffset = Math.random() * 10 - 20; // Random offset between -5 and 5 degrees
    const angleOffset = (index * 72 + offset + randomOffset) * (Math.PI / 180); // 72 degrees apart
    const radius = innerRadius; // Distance from center

    return useDerivedValue(() => {
      const currentAngle = animatedAngle.value + angleOffset;
      return vec(
        width / 1.9 + radius * Math.cos(currentAngle),
        height / 1.9 + radius * Math.sin(currentAngle),
      );
    });
  };

  useEffect(() => {
    // Prevent hardware back navigation
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', () => {
        return true;
      });
    }
  }, []);

  return (
    <View style={styles.container}>
      <Canvas style={styles.canvas}>
        <Rect x={0} y={0} width={width} height={height} color="#FFF">
          <Blend mode="multiply">
            {[2, 0, 4, 1].map((i) => (
              <RadialGradient
                key={`inner-${i}`}
                c={calculateCirclePosition(i, 340)}
                r={radii[i]} // Use animated radius
                colors={
                  i === 0
                    ? radialGradientColors1
                    : i === 1
                      ? radialGradientColors2
                      : i === 2
                        ? radialGradientColors3
                        : radialGradientColors4
                }
              />
            ))}
            {/* {[3, 1, 4, 0, 2].map((i) => (
              <RadialGradient
                key={`outer-${i}`}
                c={calculateCirclePosition(i, 400, 180)} // Offset movement by 180 degrees
                r={outerRadii[i].value} // Use animated outer radius
                colors={i === 0 ? radialGradientColors1 : i === 1 ? radialGradientColors2 : i === 2 ? radialGradientColors3 : i === 3 ? radialGradientColors4 : radialGradientColors5}
              />
            ))} */}
          </Blend>
        </Rect>
      </Canvas>
      <LogoWhite
        width={256}
        height={160}
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: [{ translateX: -128 }, { translateY: -80 }],
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF',
  },
  canvas: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});
