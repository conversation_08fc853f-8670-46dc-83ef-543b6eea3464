import { RewardsGrid } from '@/components/RewardsGrid';
import HeaderPage from '@/components/common/HeaderPage';
import { getUserProfile } from '@/methods/users';
import { ScrollView, StyleSheet, Text, View } from 'react-native';

export default function Rewards() {
  const { data: userProfile, isLoading } = getUserProfile();
  const { points } = userProfile?.data?.profile || {};

  return (
    <View style={{ height: '100%', paddingBottom: 20 }}>
      <View>
        <HeaderPage title="Rewards" />
        <View>
          <Text style={styles.pointsEarned}>
            Points Earned: {isLoading ? '...' : points}
          </Text>
        </View>
      </View>
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <RewardsGrid />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  pointsEarned: {
    fontFamily: 'Mukt<PERSON>VaaniSemiBold',
    fontSize: 12,
    fontWeight: 700,
    lineHeight: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
});
