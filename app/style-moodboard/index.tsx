import { StyleMoodBoard } from '@/components/StyleMoodBoard';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { View } from 'react-native';

export default function StyleMoodboard() {
  return (
    <>
      <HeaderPage noLogo />
      <View style={{ marginTop: 16 }}>
        <SectionTitle title="Style Moodboard" />
      </View>
      <View style={{ gap: 24, marginTop: 24 }}>
        <Text string="Discover your personal style! Browse through the moodboard and select the ones that best match your vibe." />
        <StyleMoodBoard selectable={true} />
      </View>
    </>
  );
}
