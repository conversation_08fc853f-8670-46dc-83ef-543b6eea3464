import { QuizCard } from '@/components/QuizCard';
import { SettingsGroup } from '@/components/SettingsGroup';
import { SettingsSection } from '@/components/SettingsSection';
import HeaderPage from '@/components/common/HeaderPage';
import { STYLE_MOOD_BOARD, STYLE_PREFERENCES } from '@/constants/settings';
import { getStylePreferences } from '@/methods/preferences';
import { View } from 'react-native';

export default function StyleProfile() {
  const { data } = getStylePreferences();

  const stylePreferences = data?.data.stylePreferences;
  const styleKeywords = stylePreferences?.keywords || [];
  const styleHardpasses = stylePreferences?.hardPasses || [];

  // Show the style keywords and hardpasses under the titles
  const updatedStylePreferences = STYLE_PREFERENCES.map((item) => {
    if (item.itemKey === 'styleKeywords') {
      return {
        ...item,
        ...(styleKeywords.length > 0 && { keywords: styleKeywords }),
      };
    }
    if (item.itemKey === 'styleHardpasses') {
      return {
        ...item,
        ...(styleHardpasses.length > 0 && { keywords: styleHardpasses }),
      };
    }
    return item;
  });

  return (
    <>
      <HeaderPage title="Style Profile" />
      <View style={{ gap: 24, marginTop: 24 }}>
        <SettingsSection>
          <SettingsGroup buttons={STYLE_MOOD_BOARD} />
          <SettingsGroup buttons={updatedStylePreferences} />
          <QuizCard />
        </SettingsSection>
      </View>
    </>
  );
}
