import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BackHandler, <PERSON> } from 'react-native';
import Modal from 'react-native-modal';
import styled from 'styled-components/native';

// Bar indicator at the top of the modal
const BarIndicatorContainer = styled.View`
  width: 100%;
  height: 34px;
  align-items: center;
  justify-content: center;
`;

const BarIndicator = styled.View`
  width: 56px;
  height: 5px;
  border-radius: 5px;
  background-color: rgba(51, 51, 51, 0.2); /* #33333333 */
`;

// Modal container - height will be set dynamically based on active tab
interface ModalContainerProps {
  isOutfitsTab: boolean;
}

const ModalContainer = styled.View<ModalContainerProps>`
  width: 100%;
  height: ${(props: ModalContainerProps) =>
    props.isOutfitsTab
      ? '276px'
      : '220px'}; /* Increased height for Clothes filter */
  background-color: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 0 16px 24px 16px;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.15);
`;

// Modal title
const ModalTitle = styled.Text`
  width: 100%;
  height: 32px;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 24px;
  line-height: 32px;
  letter-spacing: 0px;
  color: #1c1c1c;
  margin: 0 0 16px 0;
  padding: 0 8px;
`;

// Button container
const ButtonContainer = styled.View`
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
`;

// Filter button
interface FilterButtonProps {
  active: boolean;
}

const FilterButton = styled.TouchableOpacity<FilterButtonProps>`
  min-width: 45px;
  height: 36px;
  border-radius: 8px;
  padding: 8px 16px;
  background-color: ${(props: FilterButtonProps) =>
    props.active ? '#0E7E61' : 'transparent'};
  border: ${(props: FilterButtonProps) =>
    props.active ? 'none' : '1px solid #0E7E61'};
  justify-content: center;
  align-items: center;
  flex-direction: row;
`;

// Filter button text
const FilterButtonText = styled.Text<FilterButtonProps>`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 14px;
  line-height: 18px;
  text-align: center;
  color: ${(props: FilterButtonProps) =>
    props.active ? '#FFFFFF' : '#0E7E61'};
`;

interface FilterModalProps {
  isVisible: boolean;
  onClose: () => void;
  activeTab: string;
  onFilterSelect: (category: string) => void;
  selectedFilter: string;
  outfitCategories: string[];
  clothesCategories: string[];
}

export default function FilterModal({
  isVisible,
  onClose,
  activeTab,
  onFilterSelect,
  selectedFilter,
  outfitCategories,
  clothesCategories,
}: FilterModalProps) {
  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        if (isVisible) {
          onClose();
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  // Determine which categories to show based on the active tab
  const filterCategories =
    activeTab === 'Outfits' ? outfitCategories : clothesCategories;

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{ margin: 0, justifyContent: 'flex-end' }}
      swipeDirection="down"
      onSwipeComplete={onClose}
      onModalHide={onClose}
      useNativeDriver={true}
    >
      <ModalContainer isOutfitsTab={activeTab === 'Outfits'}>
        <BarIndicatorContainer>
          <BarIndicator />
        </BarIndicatorContainer>
        <ModalTitle>Filter {activeTab}</ModalTitle>
        <ScrollView
          horizontal={false}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={{ paddingBottom: 16 }}
        >
          <ButtonContainer>
            {filterCategories.map((category: string) => (
              <FilterButton
                key={category}
                active={selectedFilter === category}
                onPress={() => {
                  onFilterSelect(category);
                  onClose();
                }}
              >
                <FilterButtonText active={selectedFilter === category}>
                  {category}
                </FilterButtonText>
              </FilterButton>
            ))}
          </ButtonContainer>
        </ScrollView>
      </ModalContainer>
    </Modal>
  );
}
