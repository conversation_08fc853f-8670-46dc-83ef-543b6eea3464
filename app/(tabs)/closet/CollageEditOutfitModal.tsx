import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Image,
} from 'react-native';
import { getClothes } from '@/methods/cloths';
import DatePickerModal from '@/components/DatePickerModal';

interface CollageEditOutfitModalProps {
  isVisible: boolean;
  onClose: () => void;
  editMode?: boolean;
  existingOutfit?: any;
}

export const CollageEditOutfitModal = ({
  isVisible,
  onClose,
  editMode = false,
  existingOutfit,
}: CollageEditOutfitModalProps) => {
  console.log('🎨 CollageEditOutfitModal - Step 4 - Render called');
  console.log('🎨 CollageEditOutfitModal - isVisible:', isVisible);
  console.log('🎨 CollageEditOutfitModal - editMode:', editMode);
  console.log('🎨 CollageEditOutfitModal - existingOutfit FULL OBJECT:', JSON.stringify(existingOutfit, null, 2));

  // Debug outfit data structure
  if (existingOutfit) {
    console.log('🎨 CollageEditOutfitModal - Outfit properties:');
    console.log('🎨 - _id:', existingOutfit._id);
    console.log('🎨 - name:', existingOutfit.name);
    console.log('🎨 - itemIds:', existingOutfit.itemIds);
    console.log('🎨 - selectedItems:', existingOutfit.selectedItems);
    console.log('🎨 - items:', existingOutfit.items);
    console.log('🎨 - All keys:', Object.keys(existingOutfit));
  }

  // Form state
  const [outfitName, setOutfitName] = useState('');
  const [location, setLocation] = useState('London, United Kingdom');
  const [eventDate, setEventDate] = useState(new Date());

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Populate form with existing outfit data
  useEffect(() => {
    if (editMode && existingOutfit && isVisible) {
      console.log('🎨 CollageEditOutfitModal - Step 4 - Populating form with existing outfit data');

      setOutfitName(existingOutfit.name || '');
      setLocation(existingOutfit.location || 'London, United Kingdom');

      // Handle date conversion
      if (existingOutfit.eventDate) {
        const date = existingOutfit.eventDate instanceof Date
          ? existingOutfit.eventDate
          : new Date(existingOutfit.eventDate);
        setEventDate(date);
      }

      console.log('🎨 CollageEditOutfitModal - Step 4 - Form populated with:', {
        name: existingOutfit.name,
        location: existingOutfit.location,
        eventDate: existingOutfit.eventDate,
        itemIds: existingOutfit.itemIds,
      });
    } else if (!editMode) {
      // Reset form for new outfit
      console.log('🎨 CollageEditOutfitModal - Step 4 - Resetting form for new outfit');
      setOutfitName('');
      setLocation('London, United Kingdom');
      setEventDate(new Date());
    }
  }, [editMode, existingOutfit, isVisible]);

  // Fetch clothes data
  const { data: clothesData, isLoading: clothesLoading, error: clothesError } = getClothes();

  const clothes = clothesData?.data || [];

  // Debug clothes loading
  console.log('🎨 CollageEditOutfitModal - Clothes loading debug:');
  console.log('🎨 - clothesLoading:', clothesLoading);
  console.log('🎨 - clothesError:', clothesError);
  console.log('🎨 - clothesData:', clothesData);
  console.log('🎨 - clothesData?.data:', clothesData?.data);
  console.log('🎨 - clothes.length:', clothes.length);
  if (clothes.length > 0) {
    console.log('🎨 - First few clothes:', clothes.slice(0, 3).map((item: any) => ({
      id: item._id,
      name: item.name,
    })));
  }

  // Get items to display - use outfit's items directly if in edit mode
  const displayItems = React.useMemo(() => {
    console.log('🎨 CollageEditOutfitModal - Step 4 - Getting items to display');
    console.log('🎨 CollageEditOutfitModal - editMode:', editMode);

    if (editMode && existingOutfit?.items && Array.isArray(existingOutfit.items)) {
      // Use the outfit's items directly - they're already full objects!
      console.log('🎨 CollageEditOutfitModal - Using outfit items directly:', {
        outfitItemsCount: existingOutfit.items.length,
        outfitItems: existingOutfit.items.map((item: any) => ({
          id: item._id,
          name: item.name,
          imageUrl: item.imageUrl,
        })),
      });

      return existingOutfit.items;
    } else if (editMode && existingOutfit?.selectedItems && Array.isArray(existingOutfit.selectedItems)) {
      // Fallback: try to match selectedItems with loaded clothes
      console.log('🎨 CollageEditOutfitModal - Trying to match selectedItems with clothes');
      const outfitItems = clothes.filter((item: any) =>
        existingOutfit.selectedItems.includes(item._id)
      );

      console.log('🎨 CollageEditOutfitModal - Matched items:', {
        selectedItemIds: existingOutfit.selectedItems,
        totalClothes: clothes.length,
        matchedItems: outfitItems.length,
      });

      return outfitItems;
    } else {
      // Show first 8 items for new outfit creation
      console.log('🎨 CollageEditOutfitModal - Step 4 - Showing first 8 items for new outfit');
      return clothes.slice(0, 8);
    }
  }, [editMode, existingOutfit, clothes]);

  console.log('🎨 CollageEditOutfitModal - Step 4 - Form state:', {
    outfitName,
    location,
    eventDate: eventDate.toDateString(),
    editMode,
    existingOutfitName: existingOutfit?.name,
  });

  console.log('🎨 CollageEditOutfitModal - Step 4 - Clothes data:', {
    clothesLoading,
    clothesCount: clothes.length,
    displayItemsCount: displayItems.length,
    isEditMode: editMode,
  });

  if (!isVisible) {
    console.log('🎨 CollageEditOutfitModal - Not visible, returning null');
    return null;
  }

  console.log('🎨 CollageEditOutfitModal - Rendering modal content');

  // Format date for display
  const formatDate = (date: Date): string => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
  };

  return (
    <Modal
      visible={isVisible}
      transparent={false}
      animationType="slide"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
    >
      <View style={{
        flex: 1,
        backgroundColor: 'white',
      }}>
        <View style={{
          flex: 1,
          paddingTop: 50, // Safe area padding for status bar
        }}>
          {/* Header */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingVertical: 15,
            backgroundColor: '#f8f8f8',
            borderBottomWidth: 1,
            borderBottomColor: '#e0e0e0',
            marginBottom: 0,
          }}>
            <TouchableOpacity onPress={onClose}>
              <Text style={{ color: '#0E7E61', fontSize: 16, fontWeight: '500' }}>Cancel</Text>
            </TouchableOpacity>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#333',
            }}>
              {editMode ? 'Edit Outfit (Collage)' : 'Create Outfit (Collage)'}
            </Text>
            <TouchableOpacity onPress={() => {
              console.log('🎨 CollageEditOutfitModal - Step 4 - Update button pressed');
              console.log('🎨 CollageEditOutfitModal - Step 4 - Form data:', { outfitName, location, eventDate });
            }}>
              <Text style={{ color: '#0E7E61', fontSize: 16, fontWeight: '500' }}>
                {editMode ? 'Update' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={{ padding: 20 }}
            showsVerticalScrollIndicator={false}
          >


            {/* Outfit Name */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '500',
                color: '#333',
                marginBottom: 8,
              }}>
                Outfit Name
              </Text>
              <TextInput
                value={outfitName}
                onChangeText={(text) => {
                  console.log('🎨 CollageEditOutfitModal - Step 2 - Outfit name changed:', text);
                  setOutfitName(text);
                }}
                placeholder="Enter outfit name"
                style={{
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 16,
                  backgroundColor: '#f8f8f8',
                }}
              />
            </View>

            {/* Location */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '500',
                color: '#333',
                marginBottom: 8,
              }}>
                Location
              </Text>
              <TextInput
                value={location}
                onChangeText={(text) => {
                  console.log('🎨 CollageEditOutfitModal - Step 2 - Location changed:', text);
                  setLocation(text);
                }}
                placeholder="Enter location"
                style={{
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 16,
                  backgroundColor: '#f8f8f8',
                }}
              />
            </View>

            {/* Event Date */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '500',
                color: '#333',
                marginBottom: 8,
              }}>
                Event Date
              </Text>
              <TouchableOpacity
                onPress={() => {
                  console.log('🎨 CollageEditOutfitModal - Opening date picker');
                  setShowDatePicker(true);
                }}
                style={{
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  borderRadius: 8,
                  padding: 12,
                  backgroundColor: '#f8f8f8',
                }}
              >
                <Text style={{ fontSize: 16, color: '#333' }}>
                  {formatDate(eventDate)}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Canvas with Static Items */}
            <View style={{
              backgroundColor: '#f8f8f8',
              borderWidth: 2,
              borderColor: '#e0e0e0',
              borderStyle: 'dashed',
              borderRadius: 12,
              minHeight: 400,
              padding: 20,
              marginBottom: 20,
            }}>
              <Text style={{
                fontSize: 16,
                color: '#0E7E61',
                fontWeight: '600',
                marginBottom: 15,
                textAlign: 'center',
              }}>
                Canvas Area
              </Text>

              {clothesLoading ? (
                <View style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 200,
                }}>
                  <Text style={{ color: '#999', fontSize: 16 }}>Loading clothes...</Text>
                </View>
              ) : displayItems.length === 0 ? (
                <View style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 200,
                }}>
                  <Text style={{ color: '#999', fontSize: 16, textAlign: 'center' }}>
                    {editMode
                      ? `No items found for this outfit.{'\n'}The outfit might have been created with items that no longer exist.{'\n\n'}Outfit ID: ${existingOutfit?._id}{'\n'}Expected ${existingOutfit?.itemIds?.length || 0} items`
                      : `No clothes found.{'\n'}Add some clothes to your closet first!`
                    }
                  </Text>
                </View>
              ) : (
                <View style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-around',
                  alignItems: 'flex-start',
                  gap: 15,
                }}>
                  {displayItems.map((item: any, index: number) => (
                    <TouchableOpacity
                      key={item._id || `item-${index}`}
                      onPress={() => {
                        console.log('🎨 CollageEditOutfitModal - Step 3 - Item pressed:', item.name);
                      }}
                      style={{
                        backgroundColor: 'white',
                        borderRadius: 8,
                        padding: 8,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 4,
                        elevation: 3,
                        width: 80,
                        alignItems: 'center',
                      }}
                    >
                      <Image
                        source={
                          item.imageUrl
                            ? { uri: item.imageUrl }
                            : require('../../../assets/images/placeholder-item.png')
                        }
                        style={{
                          width: 60,
                          height: 60,
                          borderRadius: 6,
                          backgroundColor: '#f0f0f0',
                        }}
                        resizeMode="cover"
                      />
                      <Text
                        style={{
                          fontSize: 10,
                          color: '#333',
                          textAlign: 'center',
                          marginTop: 4,
                          fontWeight: '500',
                        }}
                        numberOfLines={2}
                      >
                        {item.name || `Item ${index + 1}`}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              {!editMode && clothes.length > 8 && (
                <Text style={{
                  fontSize: 12,
                  color: '#999',
                  textAlign: 'center',
                  marginTop: 15,
                  fontStyle: 'italic',
                }}>
                  Showing first 8 items (Step 5 will add item selection)
                </Text>
              )}
            </View>
          </ScrollView>
        </View>
      </View>

      {/* Date Picker Modal */}
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => {
          console.log('🎨 CollageEditOutfitModal - Date picker closed');
          setShowDatePicker(false);
        }}
        onDateChange={(date) => {
          if (date) {
            console.log('🎨 CollageEditOutfitModal - Date selected:', date);
            setEventDate(date);
          }
        }}
        value={eventDate}
        title="Select Event Date"
      />
    </Modal>
  );
};
