import React, { useEffect } from 'react';
import Modal from 'react-native-modal';
import { BackHandler, Platform } from 'react-native';
import {
  Camera,
  Image as ImageIcon,
  ShoppingBag,
  Barcode,
  History,
  Share2
} from 'lucide-react-native';
// Import styled components
import styled from 'styled-components/native';
// Import responsive utilities
import useResponsive from '@/hooks/useResponsive';
import { PADDING, BORDER_RADIUS, FONT_SIZES } from '@/constants/responsive';

// Bar indicator at the top of the modal
const BarIndicatorContainer = styled.View`
  width: 100%;
  height: 34px;
  align-items: center;
  justify-content: center;
`;

const BarIndicator = styled.View`
  width: 56px;
  height: 5px;
  border-radius: 5px;
  background-color: rgba(51, 51, 51, 0.2);
`;

// Modal container - height and padding will be set dynamically
const ModalContainer = styled.View<{ isTablet: boolean }>`
  width: 100%;
  height: ${(props: { isTablet: boolean }) => props.isTablet ? '420px' : '356px'};
  background-color: #F0F0F0;
  border-top-left-radius: ${(props: { isTablet: boolean }) => props.isTablet ? BORDER_RADIUS.MODAL : 8}px;
  border-top-right-radius: ${(props: { isTablet: boolean }) => props.isTablet ? BORDER_RADIUS.MODAL : 8}px;
  padding: 0px ${(props: { isTablet: boolean }) => props.isTablet ? PADDING.MODAL_CONTENT_HORIZONTAL : 16}px 24px ${(props: { isTablet: boolean }) => props.isTablet ? PADDING.MODAL_CONTENT_HORIZONTAL : 16}px;
`;

// Grid container for buttons
const ButtonsContainer = styled.View<{ isTablet: boolean }>`
  flex: 1;
  width: 100%;
  padding-top: ${(props: { isTablet: boolean }) => props.isTablet ? '32px' : '24px'};
  align-items: center;
  justify-content: center;
`;

// Row of buttons - width will be set dynamically
const ButtonRow = styled.View<{ isTablet: boolean, rowWidth: number }>`
  width: ${(props: { rowWidth: number }) => props.rowWidth}px;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: ${(props: { isTablet: boolean }) => props.isTablet ? '24px' : '16px'};
`;

// Individual button - dimensions will be set dynamically
const ActionButton = styled.TouchableOpacity<{ isTablet: boolean, buttonWidth: number }>`
  width: ${(props: { buttonWidth: number }) => props.buttonWidth}px;
  height: ${(props: { isTablet: boolean }) => props.isTablet ? '160px' : '112px'};
  background-color: #B4D7CE;
  border-radius: ${(props: { isTablet: boolean }) => props.isTablet ? '16px' : '8px'};
  padding: ${(props: { isTablet: boolean }) => props.isTablet ? '24px' : '16px'};
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
`;

// Icon container - size will be set dynamically
const IconContainer = styled.View<{ isTablet: boolean }>`
  width: ${(props: { isTablet: boolean }) => props.isTablet ? '32px' : '24px'};
  height: ${(props: { isTablet: boolean }) => props.isTablet ? '32px' : '24px'};
  justify-content: center;
  align-items: center;
`;

// Button text - font size will be set dynamically
const ButtonText = styled.Text<{ isTablet: boolean }>`
  font-family: 'MuktaVaani';
  font-weight: 500;
  font-size: ${(props: { isTablet: boolean }) => props.isTablet ? FONT_SIZES.LABEL : '14px'};
  line-height: ${(props: { isTablet: boolean }) => props.isTablet ? '22px' : '18px'};
  text-align: center;
  color: #0E7E61;
  width: 100%;
`;

interface ClosetBottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  onOptionPress: (option: string) => void;
}

export default function ClosetBottomSheetModal({
  isVisible,
  onClose,
  onOptionPress
}: ClosetBottomSheetModalProps) {
  // Get responsive values using the hook
  const { isTablet, width } = useResponsive();

  // Calculate responsive dimensions
  // For tablet, we want to use more of the available width
  // Use a larger percentage of screen width for tablet
  const rowWidth = isTablet ? Math.min(900, width * 0.95) : 343;
  const buttonGap = isTablet ? 20 : 16;
  const buttonsPerRow = 3;
  const buttonWidth = (rowWidth - (buttonGap * (buttonsPerRow - 1))) / buttonsPerRow;

  // Calculate icon size based on device type
  const iconSize = isTablet ? 32 : 24;

  // Log when the modal visibility changes
  React.useEffect(() => {
    console.log('ClosetBottomSheetModal isVisible:', isVisible);
  }, [isVisible]);

  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        if (isVisible) {
          onClose();
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  // Define the options for adding clothes
  const options = [
    {
      id: 'camera',
      name: 'Camera',
      icon: <Camera size={iconSize} color="#0E7E61" />,
    },
    {
      id: 'photo-gallery',
      name: 'Photo Gallery',
      icon: <ImageIcon size={iconSize} color="#0E7E61" />,
    },
    {
      id: 'social-media',
      name: 'Social Media',
      icon: <Share2 size={iconSize} color="#0E7E61" />,
    },
    {
      id: 'online-store',
      name: 'Online Store',
      icon: <ShoppingBag size={iconSize} color="#0E7E61" />,
    },
    {
      id: 'past-purchases',
      name: 'Past Purchases',
      icon: <History size={iconSize} color="#0E7E61" />,
    },
    {
      id: 'barcode',
      name: 'Barcode',
      icon: <Barcode size={iconSize} color="#0E7E61" />,
    },
  ];

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{ margin: 0, justifyContent: 'flex-end' }}
      swipeDirection="down"
      onSwipeComplete={onClose}
      swipeThreshold={15}
      backdropOpacity={0.15}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={250}
      animationOutTiming={200}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={200}
      useNativeDriver={true}
      useNativeDriverForBackdrop={true}
      hideModalContentWhileAnimating={true}
      propagateSwipe={true}
      onModalHide={onClose}
    >
      <ModalContainer isTablet={isTablet}>
        <BarIndicatorContainer>
          <BarIndicator />
        </BarIndicatorContainer>

        <ButtonsContainer isTablet={isTablet}>
          {/* First row of buttons */}
          <ButtonRow isTablet={isTablet} rowWidth={rowWidth}>
            {options.slice(0, 3).map((option) => (
              <ActionButton
                key={option.id}
                isTablet={isTablet}
                buttonWidth={buttonWidth}
                onPress={() => {
                  onOptionPress(option.id);
                }}
              >
                <IconContainer isTablet={isTablet}>{option.icon}</IconContainer>
                <ButtonText isTablet={isTablet}>{option.name}</ButtonText>
              </ActionButton>
            ))}
          </ButtonRow>

          {/* Second row of buttons */}
          <ButtonRow isTablet={isTablet} rowWidth={rowWidth}>
            {options.slice(3, 6).map((option) => (
              <ActionButton
                key={option.id}
                isTablet={isTablet}
                buttonWidth={buttonWidth}
                onPress={() => {
                  onOptionPress(option.id);
                }}
              >
                <IconContainer isTablet={isTablet}>{option.icon}</IconContainer>
                <ButtonText isTablet={isTablet}>{option.name}</ButtonText>
              </ActionButton>
            ))}
          </ButtonRow>
        </ButtonsContainer>
      </ModalContainer>
    </Modal>
  );
}
