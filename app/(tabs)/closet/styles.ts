import styled from 'styled-components/native';
import { Dimensions } from 'react-native';
import { PADDING, HEIGHTS, FONT_SIZES, BORDER_RADIUS, isTablet } from '@/constants/responsive';
import { Image } from 'expo-image';

// Check if device is a tablet
const isTabletDevice = isTablet();

// Get screen dimensions for responsive calculations
const screenWidth = Dimensions.get('window').width;

// Header Container
export const HeaderContainer = styled.View`
  width: 100%;
  height: 40px;
  margin-top: 1px; /* Reduced from 68px to match Home screen */
  padding: 0;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

// Header Title
export const HeaderTitle = styled.Text`
  width: 158px;
  height: 40px;
  font-family: CormorantGaramondSemiBold;
  font-size: 32px;
  line-height: 40px;
  color: #1C1C1C;
  letter-spacing: -0.5px;
`;

// Icon Button
export const IconButton = styled.TouchableOpacity`
  width: 40px;
  height: 40px;
  border-radius: 99px;
  padding: 12px;
  background-color: #B4D7CE;
  justify-content: center;
  align-items: center;
`;

// Action Buttons Container
export const ActionButtonsContainer = styled.View`
  flex-direction: row;
  gap: 8px;
`;

// Main Content Container
export const ContentContainer = styled.View`
  width: 100%;
  margin-top: 24px;
  padding: 0 0 ${isTabletDevice ? 160 : 120}px;
  gap: 24px;
`;

// Toggle Button Container
export const ToggleContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

// Toggle Buttons Group
export const ToggleButtonsGroup = styled.View`
  flex-direction: row;
  gap: 8px;
`;

// Toggle Button
export const ToggleButton = styled.TouchableOpacity<{ active: boolean }>`
  height: 36px;
  min-width: 80px;
  border-radius: 8px;
  background-color: ${(props: { active: boolean }) => props.active ? '#0E7E61' : 'transparent'};
  border: ${(props: { active: boolean }) => props.active ? 'none' : '1px solid #0E7E61'};
  justify-content: center;
  align-items: center;
`;

// Toggle Button Text
export const ToggleButtonText = styled.Text<{ active: boolean }>`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 14px;
  color: ${(props: { active: boolean }) => props.active ? '#FFFFFF' : '#0E7E61'};
`;

// Filter Button
export const FilterButton = styled.TouchableOpacity`
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
`;

// Status Text
export const StatusText = styled.Text`
  width: 100%;
  height: 16px;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 12px;
  line-height: 16px;
  letter-spacing: -0.2px;
  color: #5C5C5C;
  margin-bottom: 16px;
`;

// Item Card
export const ItemCard = styled.TouchableOpacity`
  width: 48%;
  height: ${isTabletDevice ? 300 : 240}px;
  margin-bottom: ${isTabletDevice ? 24 : 16}px;
  overflow: hidden;
`;

// Item Image
export const ItemImage = styled(Image)`
  width: 100%;
  height: ${isTabletDevice ? 220 : 160}px;
  border-radius: 8px;
  background-color: #F0F0F0;
  align-self: center;
  justify-content: center;
`;

// Item Name Container
export const ItemNameContainer = styled.View`
  width: 100%;
  padding: 0;
  margin-top: 8px;
  flex: 1;
  justify-content: flex-start;
`;

// Item Name Text
export const ItemNameText = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 500;
  font-size: ${isTabletDevice ? 16 : 14}px;
  color: #1C1C1C;
  margin-bottom: 4px;
`;

// Item Category Text
export const ItemCategoryText = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${isTabletDevice ? 14 : 12}px;
  color: #767676;
  line-height: ${isTabletDevice ? 18 : 16}px;
`;

// Grid Container
export const GridContainer = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  padding: 0 ${isTabletDevice ? PADDING.CONTAINER_HORIZONTAL : 0}px;
`;

// Empty State Container
export const EmptyStateContainer = styled.View`
  width: 100%;
  align-items: center;
  padding: 24px 0;
  margin-top: 16px;
`;

// Empty State Title
export const EmptyStateTitle = styled.Text`
  font-family: CormorantGaramondSemiBold;
  font-size: 24px;
  line-height: 32px;
  text-align: center;
  color: #1C1C1C;
  margin-bottom: 24px;
`;

// Empty State Image
export const EmptyStateImage = styled.Image`
  width: ${isTabletDevice ? 320 : 240}px;
  height: ${isTabletDevice ? 320 : 240}px;
  margin-bottom: 16px;
`;

// Empty State Text
export const EmptyStateText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: ${isTabletDevice ? 18 : 16}px;
  line-height: ${isTabletDevice ? 28 : 24}px;
  text-align: center;
  color: #767676;
  margin-bottom: 32px;
`;

// Action Button
export const ActionButton = styled.TouchableOpacity<{ primary?: boolean }>`
  width: 100%;
  height: ${isTabletDevice ? 56 : 48}px;
  border-radius: 8px;
  background-color: ${(props: { primary?: boolean }) => props.primary ? '#0E7E61' : 'transparent'};
  border: ${(props: { primary?: boolean }) => props.primary ? 'none' : '1px solid #0E7E61'};
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  elevation: 0;
`;

// Action Button Text
export const ActionButtonText = styled.Text<{ primary?: boolean }>`
  font-family: 'MuktaVaaniSemiBold';
  font-size: ${isTabletDevice ? 18 : 16}px;
  color: ${(props: { primary?: boolean }) => props.primary ? '#FFFFFF' : '#0E7E61'};
  text-align: center;
`;
