import styled from 'styled-components/native';

// Styled components for ClothingStats screen
export const Container = styled.View`
  flex: 1;
  background-color: white;
`;

export const BackButton = styled.TouchableOpacity`
  position: absolute;
  top: 60px;
  left: 16px;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 20px;
  z-index: 10;
`;

export const ScrollContainer = styled.ScrollView`
  flex: 1;
`;

export const ContentContainer = styled.View`
  width: 343px;
  margin-top: 68px;
  margin-left: 16px;
  margin-right: 16px;
  align-self: center;
`;

export const HeaderContainer = styled.View`
  width: 279px;
  padding-top: 16px;
  padding-bottom: 16px;
  background-color: #0E7E61;
  border-radius: 8px;
  align-self: center;
  margin-bottom: 40px;
  justify-content: center;
  align-items: center;
`;

export const HeaderText = styled.Text`
  font-family: CormorantGaramond;
  font-size: 26px;
  text-align: center;
  color: white;
  padding-left: 8px;
  padding-right: 8px;
`;

export const ImageContainer = styled.View`
  width: 240px;
  height: 240px;
  border-radius: 8px;
  background-color: #EBEBEB;
  margin-bottom: 40px;
  overflow: hidden;
  align-self: center;
`;

export const ItemImage = styled.Image`
  width: 100%;
  height: 100%;
`;

export const WearButton = styled.TouchableOpacity<{ disabled?: boolean }>`
  width: 104px;
  height: 32px;
  background-color: ${(props: { disabled?: boolean }) => props.disabled ? '#AAAAAA' : '#0E7E61'};
  border-radius: 99px;
  justify-content: center;
  align-items: center;
  align-self: center;
  margin-bottom: 40px;
  opacity: ${(props: { disabled?: boolean }) => props.disabled ? 0.7 : 1};
`;

export const ButtonText = styled.Text`
  color: white;
  font-size: 14px;
  font-family: 'MuktaVaaniSemiBold';
  line-height: 16px;
`;

export const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  min-height: 300px;
`;

export const ErrorText = styled.Text`
  color: red;
  font-size: 16px;
  text-align: center;
  margin: 20px;
`;

export const StatsContainer = styled.View`
  width: 100%;
  margin-bottom: 40px;
  align-items: center;
`;

export const StatsRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  width: 240px;
  margin-bottom: 8px;
`;

export const StatsLabel = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 14px;
  color: #333;
`;

export const StatsValue = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 14px;
  color: #0E7E61;
`;
