import React, { useEffect } from 'react';
import { TouchableOpacity, TouchableWithoutFeedback, View, Text, Modal, StyleSheet, BackHandler, Platform } from 'react-native';

interface SimpleAddItemModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddOutfit: () => void;
  onAddClothes: () => void;
}

export default function SimpleAddItemModal({
  isVisible,
  onClose,
  onAddOutfit,
  onAddClothes
}: SimpleAddItemModalProps) {
  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android' && isVisible) {
      const backAction = () => {
        onClose();
        return true; // Prevent default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.container}>
          <View style={styles.modalContainer}>
            <TouchableOpacity
              style={styles.optionButton}
              onPress={() => {
                console.log('Add outfit selected');
                onAddOutfit();
                onClose();
              }}
            >
              <Text style={styles.optionText}>Add outfit</Text>
            </TouchableOpacity>

            <View style={styles.separator} />

            <TouchableOpacity
              style={styles.optionButton}
              onPress={() => {
                console.log('Add clothes selected');
                onAddClothes();
                onClose();
              }}
            >
              <Text style={styles.optionText}>Add clothes</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingRight: 32,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: 250,
    height: 100, // Increased height to accommodate text
    marginTop: 116,
    overflow: 'hidden',
  },
  optionButton: {
    paddingVertical: 14, // Increased padding for better touch area
    width: '100%',
  },
  optionText: {
    fontFamily: 'MuktaVaani',
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1C',
    paddingLeft: 16,
    height: 24, // Increased height to prevent text cropping
  },
  separator: {
    height: 1,
    width: '100%',
    backgroundColor: '#E5E5E5',
  }
});
