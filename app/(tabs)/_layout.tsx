import BottomSheetComponent from '@/components/BottomSheetComponent';
import CustomTabs from '@/components/CustomTabs';
import DeleteConfirmation from '@/components/DeleteConfirmation';
import { useClientOnlyValue } from '@/components/useClientOnlyValue';
import { useColorScheme } from '@/components/useColorScheme';
import Colors from '@/constants/Colors';
import { ProfileProvider } from '@/context/ProfileContext';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import BottomSheet from '@gorhom/bottom-sheet';
import { Tabs, useRouter } from 'expo-router';
import { useCallback, useEffect, useRef } from 'react';
import { View } from 'react-native';

// You can explore the built-in icon families and icons on the web at https://icons.expo.fyi/
function TabBarIcon(props: {
  name: React.ComponentProps<typeof FontAwesome>['name'];
  color: string;
}) {
  return <FontAwesome size={28} style={{ marginBottom: -3 }} {...props} />;
}

export default function TabLayout() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const deleteBottomSheetRef = useRef<BottomSheet>(null);
  const editUserBottomSheetRef = useRef<BottomSheet>(null);

  useEffect(() => {
    editUserBottomSheetRef.current?.expand();
  }, []);

  const handleAddPress = useCallback(() => {
    router.push('/create-trip');
  }, []);

  const handleDeletePress = useCallback(() => {
    deleteBottomSheetRef.current?.expand();
  }, []);

  const handleCancelDelete = useCallback(() => {
    deleteBottomSheetRef.current?.close();
  }, []);

  return (
    <ProfileProvider onDeletePress={handleDeletePress}>
      <View style={{ flex: 1 }}>
        <Tabs
          tabBar={(props) => (
            <CustomTabs {...props} onAddPress={handleAddPress} />
          )}
          screenOptions={{
            tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
            headerShown: useClientOnlyValue(false, true),
          }}
        >
          <Tabs.Screen
            name="home"
            options={{
              title: 'Home',
              headerShown: false,
              tabBarIcon: ({ color }) => (
                <TabBarIcon name="code" color={color} />
              ),
            }}
          />
          <Tabs.Screen
            name="closet"
            options={{
              title: 'Closet',
              headerShown: false,
            }}
            initialParams={{ title: 'My Closet' }}
          />
          <Tabs.Screen
            name="add"
            options={{
              title: 'Add',
              headerShown: false,
            }}
          />
          <Tabs.Screen
            name="musings"
            options={{
              title: 'Musings',
              headerShown: false,
            }}
          />
          <Tabs.Screen
            name="profile"
            options={{
              title: 'Profile',
              headerShown: false,
            }}
          />
          <Tabs.Screen
            name="settings"
            options={{
              title: 'Settings',
              headerShown: false,
            }}
          />
          <Tabs.Screen
            name="all-trips"
            options={{
              title: 'All Trips',
              headerShown: false,
            }}
          />
          <Tabs.Screen
            name="clothing-stats"
            options={{
              title: 'Clothing Stats',
              headerShown: false,
              tabBarButton: () => null, // Hide from tab bar
            }}
          />
        </Tabs>
      </View>

      <BottomSheetComponent
        ref={deleteBottomSheetRef}
        enableDynamicSizing={true}
      >
        <DeleteConfirmation onCancel={handleCancelDelete} />
      </BottomSheetComponent>
    </ProfileProvider>
  );
}
