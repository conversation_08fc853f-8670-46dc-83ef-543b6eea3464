import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Stack } from 'expo-router';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Meteor from '@meteorrn/core';
import AddCategoryForm from '@/components/CategoryManagement/AddCategoryForm';
import { addMainCategoryWithSubcategories } from '@/methods/category-management';

export default function CategoryManagementScreen() {
  const [activeTab, setActiveTab] = useState<'women' | 'men'>('women');
  const queryClient = useQueryClient();

  // Fetch categories for the active gender
  const {
    data: categoriesData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['categories', activeTab],
    queryFn: () => {
      return new Promise((resolve, reject) => {
        Meteor.call(
          'itemCategories-fetch',
          { gender: activeTab },
          (err: any, res: any) => {
            if (err) {
              console.error('Error fetching categories:', err);
              reject(err);
              return;
            }

            // Ensure we never resolve with undefined
            if (!res || !res.data) {
              resolve({ itemCategories: [] });
              return;
            }

            resolve(res.data);
          },
        );
      });
    },
  });

  // Handle category refresh
  const handleRefreshCategories = () => {
    queryClient.invalidateQueries({ queryKey: ['categories'] });
  };

  // Handle adding predefined categories
  const handleAddPredefinedCategories = async () => {
    try {
      // Define basic categories for the selected gender
      const basicCategories = [
        {
          name: 'Tops',
          subcategories: [
            'T-Shirts',
            'Shirts',
            'Blouses',
            'Sweaters',
            'Hoodies',
          ],
        },
        {
          name: 'Bottoms',
          subcategories: ['Pants', 'Jeans', 'Shorts', 'Skirts', 'Leggings'],
        },
        {
          name: 'Dresses',
          subcategories: [
            'Casual Dresses',
            'Formal Dresses',
            'Maxi Dresses',
            'Mini Dresses',
          ],
        },
        {
          name: 'Outerwear',
          subcategories: ['Jackets', 'Coats', 'Blazers', 'Cardigans'],
        },
        {
          name: 'Shoes',
          subcategories: ['Sneakers', 'Boots', 'Heels', 'Sandals', 'Flats'],
        },
        {
          name: 'Accessories',
          subcategories: [
            'Bags',
            'Jewelry',
            'Watches',
            'Belts',
            'Hats',
            'Scarves',
          ],
        },
        {
          name: 'Tech',
          subcategories: [
            'Phones',
            'Laptops',
            'Headphones',
            'Smartwatches',
            'Cameras',
          ],
        },
      ];

      // Confirm with the user
      Alert.alert(
        'Add Predefined Categories',
        `This will add ${basicCategories.length} main categories with subcategories for ${activeTab}. Continue?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Add Categories',
            onPress: async () => {
              // Show loading alert
              Alert.alert(
                'Adding Categories',
                'Please wait while we add the categories...',
              );

              // Add each category
              for (const category of basicCategories) {
                try {
                  await addMainCategoryWithSubcategories(
                    category.name,
                    activeTab,
                    `${category.name} for ${activeTab}`,
                    category.subcategories,
                  );
                  console.log(`Added category: ${category.name}`);
                } catch (error) {
                  console.error(
                    `Error adding category ${category.name}:`,
                    error,
                  );
                }
              }

              // Refresh categories
              handleRefreshCategories();

              // Show success message
              Alert.alert(
                'Success',
                'Predefined categories added successfully',
              );
            },
          },
        ],
      );
    } catch (error) {
      console.error('Error adding predefined categories:', error);
      Alert.alert('Error', 'Failed to add predefined categories');
    }
  };

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Category Management',
          headerStyle: {
            backgroundColor: '#0E7E61',
          },
          headerTintColor: '#fff',
        }}
      />

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'women' && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab('women')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'women' && styles.activeTabText,
            ]}
          >
            Women
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 'men' && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab('men')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'men' && styles.activeTabText,
            ]}
          >
            Men
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Categories</Text>
          {isLoading ? (
            <Text style={styles.loadingText}>Loading categories...</Text>
          ) : error ? (
            <Text style={styles.errorText}>Error loading categories</Text>
          ) : (
            <View style={styles.categoriesList}>
              {categoriesData?.itemCategories?.length > 0 ? (
                categoriesData.itemCategories.map(
                  (category: any, index: number) => (
                    <View key={index} style={styles.categoryCard}>
                      <Text style={styles.categoryName}>{category.name}</Text>
                      {category.description && (
                        <Text style={styles.categoryDescription}>
                          {category.description}
                        </Text>
                      )}
                      {category.subCategories &&
                        category.subCategories.length > 0 && (
                          <View style={styles.subcategoriesContainer}>
                            <Text style={styles.subcategoriesTitle}>
                              Subcategories:
                            </Text>
                            {category.subCategories.map(
                              (subcat: any, subIndex: number) => (
                                <Text
                                  key={subIndex}
                                  style={styles.subcategoryItem}
                                >
                                  • {subcat.name}
                                </Text>
                              ),
                            )}
                          </View>
                        )}
                    </View>
                  ),
                )
              ) : (
                <Text style={styles.emptyText}>
                  No categories found for {activeTab}
                </Text>
              )}
            </View>
          )}

          <TouchableOpacity
            style={styles.refreshButton}
            onPress={handleRefreshCategories}
          >
            <Text style={styles.refreshButtonText}>Refresh Categories</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.predefinedButton}
            onPress={handleAddPredefinedCategories}
          >
            <Text style={styles.predefinedButtonText}>
              Add Predefined Categories
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Add New Category</Text>
          <AddCategoryForm onSuccess={handleRefreshCategories} />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#0E7E61',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#0E7E61',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#0E7E61',
  },
  loadingText: {
    textAlign: 'center',
    padding: 16,
    color: '#666',
  },
  errorText: {
    textAlign: 'center',
    padding: 16,
    color: '#FF6B6B',
  },
  emptyText: {
    textAlign: 'center',
    padding: 16,
    color: '#666',
    fontStyle: 'italic',
  },
  categoriesList: {
    marginBottom: 16,
  },
  categoryCard: {
    backgroundColor: '#F0F9F6',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#0E7E61',
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  subcategoriesContainer: {
    marginTop: 8,
  },
  subcategoriesTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  subcategoryItem: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    marginBottom: 2,
  },
  refreshButton: {
    backgroundColor: '#F0F9F6',
    borderWidth: 1,
    borderColor: '#0E7E61',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  refreshButtonText: {
    color: '#0E7E61',
    fontSize: 16,
    fontWeight: '500',
  },
  predefinedButton: {
    backgroundColor: '#0E7E61',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  predefinedButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});
