import React, { useState, useCallback, useEffect } from 'react';
import { View, TouchableOpacity, TextInput, FlatList, ActivityIndicator, Alert, BackHandler, Platform } from 'react-native';
import Modal from 'react-native-modal';
import { Search, ChevronDown } from 'lucide-react-native';
import DatePickerModal from '@/components/DatePickerModal';
import Meteor from '@meteorrn/core';
import { useQuery } from '@tanstack/react-query';
import { Checkbox } from '@/components/common/Checkbox';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';
import useResponsive from '@/hooks/useResponsive';
import {
  ModalContainer,
  ContentContainer,
  FrameContainer,
  FormGroup,
  InputFieldContainer,
  InputFieldLabel,
  DateButton,
  DateButtonText,
  SectionTitle,
  DropdownContainer,
  DropdownText,
  SearchContainer,
  SearchInput,
  FilterContainer,
  FilterButtonsContainer,
  FilterButton,
  FilterButtonText,
  ClothesGridContainer,
  ClothesItemContainer,
  ClothesItemImage,
  ModalHeader,
  HeaderButton,
  HeaderButtonText,
  HeaderTitle,
  ClothItemName
} from './WearItModal.styles';

interface WearItModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (wearData: WearData) => void;
  itemId: string;
}

interface WearData {
  name: string;
  location: string;
  eventDate: Date;
  packingListId?: string;
  selectedItems: string[];
}

interface PackingList {
  _id: string;
  name: string;
}

function WearItModal({ isVisible, onClose, onSave, itemId }: WearItModalProps) {
  // Get responsive values
  const { fontSizes, isTablet } = useResponsive();

  // Form state
  const [outfitName, setOutfitName] = useState('Casual Outfit');
  const [location, setLocation] = useState('London, United Kingdom');
  const [eventDate, setEventDate] = useState(new Date());
  const [selectedPackingList, setSelectedPackingList] = useState<PackingList>({ _id: 'none', name: 'None (No Trip)' });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedItems, setSelectedItems] = useState<string[]>([itemId]); // Pre-select the current item

  // Image loading states
  const [imageLoadingStates, setImageLoadingStates] = useState<{[key: string]: boolean}>({});

  // Modal states
  const [showPackingListDropdown, setShowPackingListDropdown] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Fetch clothes data
  const { data: clothesData = [], isLoading: isLoadingClothes } = useQuery({
    queryKey: ['clothes'],
    queryFn: () => {
      return new Promise<any>((resolve, reject) => {
        Meteor.call('items-fetchAll', {}, (error: any, response: any) => {
          if (error) {
            reject(error);
          } else {
            resolve(response);
          }
        });
      });
    },
    select: (data: any) => {
      // Extract items from the nested data structure
      const items = data?.data?.items || [];

      // Filter out test items and ensure imageUrl is properly formatted
      return items
        .filter((item: any) => {
          return item.name !== 'test' && item.name !== 'test1';
        })
        .map((item: any) => {
          // Ensure imageUrl is properly formatted
          if (item.imageUrl && !item.imageUrl.startsWith('http')) {
            item.imageUrl = `https://myuse.s3.ap-southeast-1.amazonaws.com/${item.imageUrl}`;
          }
          return item;
        });
    }
  });

  // Fetch packing lists from the backend
  const { data: tripsData, isLoading: isLoadingTrips } = useQuery({
    queryKey: ['trips'],
    queryFn: () => {
      return new Promise<any>((resolve, reject) => {
        Meteor.call('events-fetchAll', {}, (error: any, response: any) => {
          if (error) {
            console.error('Error fetching trips:', error);
            reject(error);
          } else {
            resolve(response);
          }
        });
      });
    },
    select: (data: any) => {
      // Extract trips from the nested data structure
      return data?.data?.events || [];
    }
  });

  // Check if a trip is in the future (has an end date that is today or later)
  const isFutureTrip = (trip: any): boolean => {
    if (!trip.endDate) return false;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tripEnd = new Date(trip.endDate);
    tripEnd.setHours(0, 0, 0, 0);

    return tripEnd >= today;
  };

  // Format trips as packing lists, filtering out past trips
  const packingLists = [
    // Add a "None" option as the first item
    { _id: 'none', name: 'None (No Trip)' },
    // Add actual trips with their date information, filtering out past trips
    ...(tripsData?.map((trip: any) => ({
      _id: trip._id,
      name: trip.name || 'Unnamed Trip',
      startDate: trip.startDate ? new Date(trip.startDate) : undefined,
      endDate: trip.endDate ? new Date(trip.endDate) : undefined
    }))
    .filter(trip => isFutureTrip(trip)) || [
      // Fallback to hardcoded values if no trips are available
      { _id: 'trip1', name: 'US Trip' },
      { _id: 'trip2', name: 'Europe Vacation' },
      { _id: 'trip3', name: 'Beach Getaway' },
      { _id: 'trip4', name: 'Business Trip' }
    ])
  ];

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<string[]>(['All', 'Tops', 'Dresses', 'Bottoms', 'Shoes', 'Accessories']);

  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        if (isVisible) {
          onClose();
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
        }

        if (!gender) {
          setDisplayCategories(['All', 'Tops', 'Bottoms', 'Dresses', 'Shoes', 'Accessories']);
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        // Extract category names
        const categoryNames = backendCategories.map(category => category.name);

        // Always include 'All' as the first category
        if (!categoryNames.includes('All')) {
          categoryNames.unshift('All');
        }

        // Make sure we have all the basic categories at minimum
        const basicCategories = ['Tops', 'Bottoms', 'Dresses', 'Shoes', 'Accessories'];

        // Add any missing basic categories
        basicCategories.forEach(basicCat => {
          if (!categoryNames.includes(basicCat)) {
            categoryNames.push(basicCat);
          }
        });

        setDisplayCategories(categoryNames);
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to default categories if there's an error
        setDisplayCategories(['All', 'Tops', 'Bottoms', 'Dresses', 'Shoes', 'Accessories']);
      }
    };

    loadGenderCategories();
  }, [userProfile]);

  // Filter clothes based on search query and selected category
  const filteredClothes = clothesData.filter((item: any) => {
    // Check if the item has a name and it matches the search query
    const matchesSearch = item.name?.toLowerCase().includes(searchQuery.toLowerCase());

    // Check if the category matches
    const matchesCategory =
      selectedCategory === 'All' ||
      (item.category?.name === selectedCategory);

    return matchesSearch && matchesCategory;
  });

  // Toggle item selection
  const toggleItemSelection = useCallback((itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  }, [selectedItems]);

  // Format date for display
  const formatDate = (date: Date): string => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
  };

  // Handle save
  const handleSave = async () => {
    try {
      // Check if any items are selected
      if (selectedItems.length === 0) {
        Alert.alert('No items selected', 'Please select at least one clothing item.');
        return;
      }

      const wearData: WearData = {
        name: outfitName,
        location,
        eventDate,
        packingListId: selectedPackingList && selectedPackingList._id !== 'none' ? selectedPackingList._id : undefined,
        selectedItems,
      };

      onSave(wearData);
      resetForm();
      onClose();
    } catch (error) {
      console.error('Error saving wear data:', error);
      Alert.alert('Error', 'Failed to save. Please try again.');
    }
  };

  // Reset form
  const resetForm = () => {
    setOutfitName('Casual Outfit');
    setLocation('London, United Kingdom');
    setEventDate(new Date());
    setSelectedPackingList({ _id: 'none', name: 'None (No Trip)' });
    setSearchQuery('');
    setSelectedCategory('All');
    setSelectedItems([itemId]); // Reset to just the current item
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{
        margin: 0,
        justifyContent: 'flex-end',
        flex: 1,
        width: '100%'
      }}
      swipeDirection="down"
      onSwipeComplete={onClose}
      backdropOpacity={0.15}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={250}
      animationOutTiming={200}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={200}
      useNativeDriver={true}
      useNativeDriverForBackdrop={true}
      hideModalContentWhileAnimating={true}
      propagateSwipe={true}
      avoidKeyboard={true}
      onModalHide={onClose}
    >
      <ModalContainer>
        <ModalHeader>
          <HeaderButton onPress={onClose}>
            <HeaderButtonText>Cancel</HeaderButtonText>
          </HeaderButton>
          <HeaderTitle>Wear It</HeaderTitle>
          <HeaderButton onPress={handleSave}>
            <HeaderButtonText>Save</HeaderButtonText>
          </HeaderButton>
        </ModalHeader>

        <ContentContainer
          showsVerticalScrollIndicator={true}
          bounces={true}
          contentContainerStyle={{
            paddingBottom: isTablet ? 40 : 20,
            flexGrow: 1
          }}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
          scrollEventThrottle={16}
        >
          <FrameContainer>
            <FormGroup>
              {/* Outfit Name Input */}
              <InputFieldContainer>
                <InputFieldLabel>Outfit Name</InputFieldLabel>
                <TextInput
                  value={outfitName}
                  onChangeText={setOutfitName}
                  style={{
                    fontFamily: 'MuktaVaani',
                    fontStyle: 'normal',
                    fontWeight: '400',
                    fontSize: fontSizes.INPUT,
                    lineHeight: 24,
                    letterSpacing: 0.5,
                    color: '#333333',
                    textAlign: 'right',
                    width: isTablet ? '60%' : '50%'
                  }}
                />
              </InputFieldContainer>

              {/* Location Input */}
              <InputFieldContainer>
                <InputFieldLabel>Location</InputFieldLabel>
                <TextInput
                  value={location}
                  onChangeText={setLocation}
                  style={{
                    fontFamily: 'MuktaVaani',
                    fontStyle: 'normal',
                    fontWeight: '400',
                    fontSize: fontSizes.INPUT,
                    lineHeight: 24,
                    letterSpacing: 0.5,
                    color: '#333333',
                    textAlign: 'right',
                    width: isTablet ? '60%' : '50%'
                  }}
                />
              </InputFieldContainer>

              {/* Event Date Input */}
              <InputFieldContainer>
                <InputFieldLabel>Event Date</InputFieldLabel>
                <DateButton onPress={() => setShowDatePicker(true)}>
                  <DateButtonText>{formatDate(eventDate)}</DateButtonText>
                </DateButton>
              </InputFieldContainer>
            </FormGroup>

            {/* Add to Packing List Section */}
            <View style={{ marginTop: isTablet ? 24 : 16 }}>
              <SectionTitle>Add item to packing list</SectionTitle>
              <DropdownContainer
                style={{ marginTop: isTablet ? 8 : 2 }}
                onPress={() => {
                if (!isLoadingTrips) {
                  setShowPackingListDropdown(true);
                }
              }}>
                <DropdownText>
                  {isLoadingTrips
                    ? 'Loading trips...'
                    : selectedPackingList
                      ? selectedPackingList.name
                      : packingLists.length > 0
                        ? packingLists[0].name
                        : 'No trips available'}
                </DropdownText>
                {isLoadingTrips
                  ? <ActivityIndicator size="small" color="#0E7E61" />
                  : <ChevronDown size={18} color="#333333" />}
              </DropdownContainer>
            </View>

            {/* Search Input */}
            <SearchContainer>
              <Search size={16} color="#767676" />
              <SearchInput
                placeholder="Search"
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholderTextColor="#767676"
              />
            </SearchContainer>

            {/* Filter Categories */}
            <FilterContainer horizontal showsHorizontalScrollIndicator={false}>
              <FilterButtonsContainer>
                {displayCategories.map((category) => (
                  <FilterButton
                    key={category}
                    active={selectedCategory === category}
                    onPress={() => {
                      setSelectedCategory(category);
                    }}
                  >
                    <FilterButtonText active={selectedCategory === category}>
                      {category}
                    </FilterButtonText>
                  </FilterButton>
                ))}
              </FilterButtonsContainer>
            </FilterContainer>

            {/* Clothes Grid */}
            <ClothesGridContainer>
              {filteredClothes.map((item: any) => (
                <ClothesItemContainer key={item._id} onPress={() => toggleItemSelection(item._id)}>
                  {/* Display image with proper error handling */}
                  <ClothesItemImage
                    source={item.imageUrl
                      ? { uri: item.imageUrl }
                      : require('@/assets/images/placeholder-item.png')}
                    resizeMode="cover"
                    onLoadStart={() => {
                      setImageLoadingStates(prev => ({ ...prev, [item._id]: true }));
                    }}
                    onLoadEnd={() => {
                      setImageLoadingStates(prev => ({ ...prev, [item._id]: false }));
                    }}
                    onError={(e: any) => {
                      console.error(`Image error for ${item.name}:`, e.nativeEvent?.error || 'Unknown error');
                      setImageLoadingStates(prev => ({ ...prev, [item._id]: false }));
                    }}
                  />

                  {/* Item name below the image */}
                  <ClothItemName numberOfLines={1}>{item.name}</ClothItemName>

                  {(isLoadingClothes || imageLoadingStates[item._id]) && (
                    <View style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(240, 240, 240, 0.7)',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: isTablet ? 12 : 8,
                      width: '100%',
                      height: '100%'
                    }}>
                      <ActivityIndicator
                        size="large"
                        color="#0E7E61"
                      />
                    </View>
                  )}
                  <Checkbox
                    checked={selectedItems.includes(item._id)}
                    onToggle={() => toggleItemSelection(item._id)}
                    size={isTablet ? 28 : 24}
                    containerStyle={{
                      position: 'absolute',
                      bottom: isTablet ? 12 : 8,
                      right: isTablet ? 12 : 8
                    }}
                  />
                </ClothesItemContainer>
              ))}
            </ClothesGridContainer>
          </FrameContainer>
        </ContentContainer>
      </ModalContainer>

      {/* Packing List Dropdown (simplified implementation) */}
      {showPackingListDropdown && (
        <Modal
          isVisible={showPackingListDropdown}
          onBackdropPress={() => setShowPackingListDropdown(false)}
          style={{
            margin: 20,
            justifyContent: 'center',
            alignItems: 'center'
          }}
          backdropOpacity={0.5}
        >
          <View style={{
            backgroundColor: 'white',
            borderRadius: 16,
            padding: 8,
            maxHeight: 300,
            width: '90%',
            alignSelf: 'center'
          }}>
            <FlatList
              data={packingLists}
              keyExtractor={(item) => item._id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    padding: 16,
                    borderBottomWidth: 1,
                    borderBottomColor: '#EEEEEE',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                  onPress={() => {
                    setSelectedPackingList(item);
                    setShowPackingListDropdown(false);
                  }}
                >
                  <DropdownText>{item.name}</DropdownText>
                  <Checkbox
                    checked={selectedPackingList?._id === item._id}
                    onToggle={() => {
                      setSelectedPackingList(item);
                      setShowPackingListDropdown(false);
                    }}
                    size={20}
                  />
                </TouchableOpacity>
              )}
            />
          </View>
        </Modal>
      )}

      {/* Date Picker Modal */}
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateChange={(date) => {
          if (date) {
            setEventDate(date);
          }
        }}
        value={eventDate}
        title="Select Event Date"
      />
    </Modal>
  );
}

// Add displayName to the component
WearItModal.displayName = 'WearItModal';

// Export the component wrapped in React.memo for better performance
export default React.memo(WearItModal);
