import { useEffect } from 'react';
import { TouchableOpacity, TouchableWithoutFeedback, View, Text, Modal, StyleSheet, BackHandler, Platform } from 'react-native';

interface DeleteOptionsModalProps {
  isVisible: boolean;
  onClose: () => void;
  onDeletePress: () => void;
}

export default function DeleteOptionsModal({
  isVisible,
  onClose,
  onDeletePress
}: DeleteOptionsModalProps) {
  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android' && isVisible) {
      const backAction = () => {
        onClose();
        return true; // Prevent default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.container}>
          <View style={styles.modalContainer}>
            <TouchableOpacity
              style={styles.optionButton}
              onPress={() => {
                console.log('Delete this item selected');
                onDeletePress();
              }}
            >
              <Text style={styles.optionText}>Delete this item</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingRight: 32,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: 200,
    height: 50,
    marginTop: 116,
    overflow: 'hidden',
  },
  optionButton: {
    paddingVertical: 14,
    width: '100%',
  },
  optionText: {
    fontFamily: 'MuktaVaani',
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1C1C',
    paddingLeft: 16,
    height: 24,
  },
});
