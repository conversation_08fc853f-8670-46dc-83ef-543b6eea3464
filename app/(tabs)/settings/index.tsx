import { SettingsGroup } from '@/components/SettingsGroup';
import { SettingsSection } from '@/components/SettingsSection';
import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import { useSession } from '@/config/ctx';
import {
  ACCOUNT_SETTINGS,
  LEGAL_POLICIES,
  PREFERENCES,
  VEGAN_PREFERENCE,
} from '@/constants/settings';
import { useProfile } from '@/context/ProfileContext';
import { updateVegan, getStylePreferences } from '@/methods/preferences';
import { updateUserPreferences, getUserProfile } from '@/methods/users';
import Meteor from '@meteorrn/core';
import { router } from 'expo-router';
import { View } from 'react-native';

export default function Settings() {
  const { signOut } = useSession();
  const { onDeletePress } = useProfile();
  const { mutate: updatePreferences } = updateUserPreferences();
  const { mutate: updateVeganMutation } = updateVegan();
  const { data: userProfile, isPending: isLoadingUserProfile } = getUserProfile();
  const { data: stylePreferencesData, isPending: isLoadingStylePreferences } = getStylePreferences();

  const preferencesData = userProfile?.data?.preferences || {};
  const stylePreferences = stylePreferencesData?.data?.stylePreferences || {};

  // Add individual mutations and data to buttons
  const veganPreferenceWithMutation = VEGAN_PREFERENCE.map(button => ({
    ...button,
    mutation: { mutate: updateVeganMutation },
    queryKey: 'stylePreferences',
    isLoading: isLoadingStylePreferences,
    data: stylePreferences,
  }));

  const profilePreferencesWithMutations = PREFERENCES.map(button => ({
    ...button,
    mutation: { mutate: updatePreferences },
    queryKey: 'profile',
    isLoading: isLoadingUserProfile,
    data: preferencesData,
  }));

  const preferencesWithMutations = [...veganPreferenceWithMutation, ...profilePreferencesWithMutations];

  return (
    <>
      <HeaderPage
        title="Settings"
        backCallback={() => router.push('/(tabs)/profile')}
      />
      <View style={{ gap: 24, marginTop: 24, paddingBottom: 20 }}>
        <SettingsSection title="Account and Settings">
          <SettingsGroup buttons={ACCOUNT_SETTINGS} />
        </SettingsSection>
        <SettingsSection title="Preferences">
          <SettingsGroup
            buttons={preferencesWithMutations}
          />
        </SettingsSection>
        <SettingsSection title="Legal Policies">
          <SettingsGroup buttons={LEGAL_POLICIES} />
        </SettingsSection>
        <Button
          title="Logout"
          buttonColor="red"
          onPress={() => {
            Meteor.logout(() => {
              router.replace('/onboarding');
              signOut();
            });
          }}
        />
        <Button
          title="Delete Account"
          buttonColor="red"
          isTransparent
          onPress={onDeletePress}
        />
      </View>
    </>
  );
}
