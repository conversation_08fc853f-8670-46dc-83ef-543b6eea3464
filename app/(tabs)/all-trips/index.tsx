import { AllTripsNav } from '@/components/AllTripsNav';
import TripCard from '@/components/TripCard';
import HeaderPage from '@/components/common/HeaderPage';
import { getPastTrips, getUpcomingTrips } from '@/methods/trips';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { useTheme } from 'styled-components';

export default function AllTrips() {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const { data: upcomingTrips } = getUpcomingTrips();
  const { data: pastTrips } = getPastTrips();

  const [activeTab, setActiveTab] = useState<'upcoming' | 'past'>('upcoming');
  const [trips, setTrips] = useState(upcomingTrips?.data.events || []);

  useEffect(() => {
    if (activeTab === 'upcoming') {
      upcomingTrips && setTrips(upcomingTrips.data?.events);
    } else {
      pastTrips && setTrips(pastTrips.data?.events);
    }
  }, [activeTab, upcomingTrips, pastTrips]);

  const handleTabChange = (tab: 'upcoming' | 'past') => {
    setActiveTab(tab);
    // Invalidate both queries to ensure fresh data
    queryClient.invalidateQueries({ queryKey: ['upcomingTrips'] });
    queryClient.invalidateQueries({ queryKey: ['pastTrips'] });
  };

  const handleTripDeleted = () => {
    // Invalidate both queries to ensure fresh data after deletion
    queryClient.invalidateQueries({ queryKey: ['upcomingTrips'] });
    queryClient.invalidateQueries({ queryKey: ['pastTrips'] });
  };

  return (
    <>
      <HeaderPage title="All Trips" />
      <View style={{ gap: 24, marginTop: 24, paddingBottom: 20 }}>
        <AllTripsNav activeTab={activeTab} setActiveTab={handleTabChange} />
        <View style={{ gap: 16 }}>
          {trips?.length === 0 ? (
            <View style={{ alignItems: 'center', padding: 20 }}>
              <Text style={{ fontFamily: 'MuktaVaani', color: theme.brand.green[500] }}>
                No {activeTab} trips
              </Text>
            </View>
          ) : (
            trips?.map((trip: any, index: number) => (
              <TripCard
                key={trip._id}
                trip={trip}
                isDeletable={activeTab === 'upcoming'}
                onDelete={handleTripDeleted}
              />
            ))
          )}
        </View>
      </View>
    </>
  );
}
