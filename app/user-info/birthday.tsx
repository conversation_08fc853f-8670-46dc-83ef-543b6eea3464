import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { useRouter } from 'expo-router';
import { CalendarDaysIcon } from 'lucide-react-native';
import { useEffect, useState } from 'react';
import { Pressable, View } from 'react-native';
import Input from '@/components/common/Input';
import moment from 'moment';
import { updateUserProfile, getUserProfile } from '@/methods/users';
import DatePickerModal from '@/components/DatePickerModal';

export default function Birthday() {
  const { mutate: updateProfile, isPending, data } = updateUserProfile();
  const { data: userProfile, isLoading } = getUserProfile();
  const router = useRouter();
  const [birthday, setBirthday] = useState<Date>(moment().subtract(18, 'years').toDate());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (userProfile?.data?.profile?.birthDate) {
      setBirthday(moment(userProfile.data.profile.birthDate).toDate());
    }
  }, [isLoading]);

  useEffect(() => {
    if (data?.success) {
      router.push('/user-info/location');
    }
  }, [isPending]);

  const handleNext = () => {
    //set validation that age must be 18 years old
    const age = moment().diff(moment(birthday, 'DD/MM/YYYY'), 'years');
    if (age < 18) {
      setErrorMessage('You must be at least 18 years old');
      return;
    }
    //save gender to server 
    updateProfile({ birthDate: birthday });
  };

  const openDatePicker = () => {
    setErrorMessage(null);
    setShowDatePicker(true);
  };

  const handleSkip = () => {
    updateProfile({ birthDate: null });
    router.push('/user-info/location');
  }

  return (
    <View style={{ height: '100%', justifyContent: 'space-between' }}>
      <View >
        <View>
          <HeaderPage
            rightComponent={<Pressable onPress={handleSkip}><Text string="Skip" /></Pressable>}
          />
          <View style={{ marginTop: 32 }}>
            <SectionTitle title="Birthday" />
          </View>
          <View style={{ marginTop: 16, gap: 16 }}>
            <Text string="When is your special day? It'll help us discover your style DNA." />
          </View>

          <View style={{ marginTop: 32, gap: 16 }}>
            <Pressable style={{ position: 'relative' }} onPress={openDatePicker}>
              <View pointerEvents="none">
                <Input
                  placeholder="DD/MM/YYYY"
                  value={moment(birthday).format('DD/MM/YYYY')}
                  readOnly
                  error={errorMessage || undefined}
                  iconComponent={<CalendarDaysIcon size={25} color={'#000'} />}
                />
              </View>
            </Pressable>
          </View>
        </View>
      </View>
      <View style={{ marginTop: 32, gap: 16 }}>
        <Button
          title="Next"
          isDisabled={!birthday}
          onPress={handleNext}
        />
      </View>
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateChange={(date) => date && setBirthday(date)}
        value={birthday}
        title="Tell us your birthdate 🎂"
        maximumDate={moment().subtract(18, 'years').toDate()}
        mode="single"
      />
    </View>
  );
}
