import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { View, Pressable } from 'react-native';
import { updateUserProfile, getUserProfile } from '@/methods/users';
import LocationAutocomplete from '@/components/LocationAutocomplete';

export default function Location() {
  const { mutate: updateProfile, isPending, data } = updateUserProfile();
  const { data: userProfile, isLoading } = getUserProfile();
  const router = useRouter();
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
  }>({ latitude: 0, longitude: 0 });
  const [address, setAddress] = useState<string>('');

  useEffect(() => {
    if (userProfile?.data?.profile?.cityName) {
      setAddress(userProfile.data.profile.cityName);
      setLocation({
        latitude: userProfile.data.profile.latitude,
        longitude: userProfile.data.profile.longitude,
      });
    }
  }, [isLoading]);

  const handleNext = () => {
    updateProfile({
      cityName: address,
      latitude: location.latitude,
      longitude: location.longitude,
    });
  };

  useEffect(() => {
    if (data?.success) {
      router.push('/(tabs)/home');
    }
  }, [isPending]);

  const handleSkip = () => {
    router.push('/(tabs)/home');
  };

  return (
    <View style={{ height: '100%', justifyContent: 'space-between' }}>
      <View>
        <View>
          <HeaderPage
            rightComponent={
              <Pressable onPress={handleSkip}>
                <Text string="Skip" />
              </Pressable>
            }
          />
          <View style={{ marginTop: 32 }}>
            <SectionTitle title="Home city" />
          </View>
          <View style={{ marginTop: 16, gap: 16 }}>
            <Text string="Home is where the heart and your closet are. Which city do you call home?" />
          </View>

          <View style={{ marginTop: 32, gap: 16 }}>
            <LocationAutocomplete
              value={address}
              onLocationSelect={(location) => {
                setAddress(location.name);
                setLocation({
                  latitude: location.latitude,
                  longitude: location.longitude,
                });
              }}
              placeholder="Enter City"
              style={{
                borderWidth: 1,
                borderRadius: 16,
                height: 56,
                paddingLeft: 16,
                paddingRight: 16,
                textAlign: 'left',
              }}
            />
          </View>
        </View>
      </View>
      <View style={{ marginTop: 32, gap: 16 }}>
        <Button
          title="Next"
          isDisabled={!address}
          isLoading={isPending}
          onPress={handleNext}
        />
      </View>
    </View>
  );
}
