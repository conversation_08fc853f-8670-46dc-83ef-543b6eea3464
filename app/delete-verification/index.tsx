import Button from '@/components/common/Button';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import Text from '@/components/common/Text';
import { deleteVerification, userDeleteAccount } from '@/methods/users';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, TextInput, View } from 'react-native';

const DeleteVerification = () => {
  const [code, setCode] = useState('');
  const [resendCounter, setResendCounter] = useState(30); // 30 seconds countdown

  const deleteAccount = userDeleteAccount();
  const deleteVerificationMutation = deleteVerification();

  const handleResend = useCallback(() => {
    setResendCounter(30); // Reset the counter after resending
    deleteVerificationMutation.mutate();
  }, []);

  // Resend verification code
  useEffect(() => {
    if (deleteVerificationMutation.isSuccess) {
      router.push('/delete-verification');
    } else if (deleteVerificationMutation.isError) {
      alert('Something went wrong. Please try again.');
    }
  }, [deleteVerificationMutation.data]);

  useEffect(() => {
    if (resendCounter > 0) {
      const timer = setInterval(() => {
        setResendCounter((prevCounter) => prevCounter - 1);
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [resendCounter]);

  const handleNext = useCallback(() => {
    deleteAccount.mutate({ code });
  }, [code]);

  useEffect(() => {
    if (deleteAccount?.data?.success) {
      router.push('/delete-confirmed');
    }
  }, [deleteAccount.data]);

  return (
    <View style={{ justifyContent: 'space-between', height: '100%' }}>
      <View>
        <HeaderPage backCallback={() => router.push('/(tabs)/settings')} />
        <View style={{ marginTop: 32 }}>
          <SectionTitle title="Delete Account" />
        </View>
        <View style={{ marginTop: 16, gap: 16 }}>
          <Text string="Enter the 5-digit verification code sent to your email to confirm deletion." />
        </View>
        <View style={{ marginTop: 32, gap: 16 }}>
          <TextInput
            value={code}
            onChangeText={(val) => setCode(val)}
            style={styles.textInput}
          />
        </View>
      </View>
      <View style={{ gap: 16 }}>
        <Button
          isLined
          title={`Resend ${resendCounter > 0 ? `(${resendCounter}s)` : ''}`}
          onPress={handleResend}
          isDisabled={resendCounter > 0}
        />
        <Button
          title={deleteAccount.isPending ? 'Verifying...' : 'Next'}
          onPress={handleNext}
          isDisabled={deleteAccount.isPending}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  textInput: {
    height: 56,
    padding: 16,
    borderWidth: 1,
    borderColor: '#505050',
    borderRadius: 16,
  },
});

export default DeleteVerification;
