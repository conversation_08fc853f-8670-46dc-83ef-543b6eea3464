{"extends": "expo/tsconfig.base", "compilerOptions": {"jsx": "react-jsx", "target": "es5", "module": "commonjs", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./node_modules/@types", "./src/types"], "paths": {"@/*": ["./*"], "tailwind.config": ["./tailwind.config.js"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "styled.d.ts", "src"]}