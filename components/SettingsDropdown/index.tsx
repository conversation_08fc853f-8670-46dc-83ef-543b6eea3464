import Check from '@/assets/svg/check.svg';
import { Fragment, useCallback, useRef, useState, useEffect } from 'react';
import {
  Dimensions,
  FlatList,
  Modal,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { SettingsOption } from '../SettingsItem';
import {
  SelectedText,
  SettingsTouchable,
  SettingsTouchableText,
} from '../SettingsItem/styles';
import {
  CheckView,
  Divider,
  DropdownItem,
  DropdownOverlay,
  DropdownText,
} from './styles';

type SettingsDropdownProps = {
  title: string;
  options: SettingsOption[];
  selectedItem?: SettingsOption;
  onSelect: (item: SettingsOption) => void;
  isLoading?: boolean;
};

// Constants for dropdown sizing
const MAX_VISIBLE_ITEMS = 5;
const ITEM_HEIGHT = 44;

export const SettingsDropdown: React.FC<SettingsDropdownProps> = ({
  title,
  options,
  selectedItem,
  onSelect,
  isLoading = false,
}: SettingsDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showAbove, setShowAbove] = useState(false);

  const touchableRef = useRef<View>(null);
  const flatListRef = useRef<FlatList>(null);
  const [position, setPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });

  const handleSelect = useCallback((item: SettingsOption) => {
    onSelect(item);
    setIsOpen(false);
  }, []);

  const isLastOption = (index: number) => index === options.length - 1;

  // Scroll to selected item when dropdown opens
  useEffect(() => {
    if (isOpen && flatListRef.current) {
      const selectedIndex = options.findIndex(
        (option) => option.value === (selectedItem?.value ?? options[0]?.value)
      );

      if (selectedIndex >= 0) {
        // Add a small delay to ensure the FlatList is fully rendered
        setTimeout(() => {
          flatListRef.current?.scrollToIndex({
            index: selectedIndex,
            animated: false,
            viewPosition: 0.5, // Center the item in the visible area
          });
        }, 100);
      }
    }
  }, [isOpen, selectedItem, options]);

  const measureTouchable = useCallback(() => {
    if (!touchableRef.current) return;

    touchableRef.current?.measure((_fx, _fy, width, height, px, py) => {
      const windowHeight = Dimensions.get('window').height;
      const maxDropdownHeight = Math.min(options.length, MAX_VISIBLE_ITEMS) * ITEM_HEIGHT;
      const spaceBelow = windowHeight - py - height;
      const shouldShowAbove = spaceBelow < maxDropdownHeight + 40; // Add some padding

      setShowAbove(shouldShowAbove);
      setPosition({
        x: px,
        y: py,
        width,
        height,
      });
    });
  }, [options]);

  return (
    <>
      <SettingsTouchable
        ref={touchableRef}
        onLayout={measureTouchable}
        onPress={() => {
          measureTouchable();
          setIsOpen((prev) => !prev);
        }}
        activeOpacity={0.8}
      >
        <SettingsTouchableText>{title}</SettingsTouchableText>
        <SelectedText numberOfLines={1}>
          {isLoading ? '-' : selectedItem?.label || options[0]?.label}
        </SelectedText>
      </SettingsTouchable>

      {isOpen && (
        <Modal
          visible={isOpen}
          transparent
          animationType="none"
          statusBarTranslucent={Platform.OS === 'android'}
        >
          <TouchableWithoutFeedback onPress={() => setIsOpen(false)}>
            <DropdownOverlay>
              <View
                style={{
                  position: 'absolute',
                  top: showAbove
                    ? position.y - Math.min(options.length, MAX_VISIBLE_ITEMS) * ITEM_HEIGHT
                    : position.y + position.height,
                  left: position.x,
                  width: position.width,
                  maxHeight: MAX_VISIBLE_ITEMS * ITEM_HEIGHT,
                  backgroundColor: '#fff',
                  borderRadius: 16,
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  overflow: 'hidden',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.15,
                  shadowRadius: 3.84,
                  elevation: 5,
                }}
                onStartShouldSetResponder={() => true}
              >
                <FlatList
                  ref={flatListRef}
                  data={options}
                  keyExtractor={(item) => item.value.toString()}
                  style={{ maxHeight: MAX_VISIBLE_ITEMS * ITEM_HEIGHT }}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled
                  keyboardShouldPersistTaps="handled"
                  bounces={Platform.OS === 'ios'}
                  overScrollMode={Platform.OS === 'android' ? 'never' : 'auto'}
                  removeClippedSubviews={Platform.OS === 'android'}
                  getItemLayout={(_, index) => ({
                    length: ITEM_HEIGHT,
                    offset: ITEM_HEIGHT * index,
                    index,
                  })}
                  onScrollToIndexFailed={(info) => {
                    // Fallback if scrollToIndex fails
                    const wait = new Promise(resolve => setTimeout(resolve, 500));
                    wait.then(() => {
                      flatListRef.current?.scrollToIndex({
                        index: info.index,
                        animated: false,
                        viewPosition: 0.5,
                      });
                    });
                  }}
                  renderItem={({ item, index }) => (
                    <Fragment>
                      <DropdownItem onPress={() => handleSelect(item)}>
                        {(selectedItem?.label
                          ? selectedItem.value === item.value
                          : options[0].value === item.value) && (
                            <CheckView>
                              <Check />
                            </CheckView>
                          )}
                        <DropdownText>{item.label}</DropdownText>
                      </DropdownItem>
                      {!isLastOption(index) && <Divider />}
                    </Fragment>
                  )}
                />
              </View>
            </DropdownOverlay>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </>
  );
};
