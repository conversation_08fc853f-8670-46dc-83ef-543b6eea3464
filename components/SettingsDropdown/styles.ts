import styled from 'styled-components/native';

export const OptionsContainer = styled.View`
  position: absolute;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.15;
  shadow-radius: 3.84px;
  elevation: 5;
`;

export const OptionsView = styled.View`
  position: absolute;
  background-color: #fff;
  width: 100%;
  border-radius: 16px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  z-index: 999;
`;

export const DropdownItem = styled.TouchableOpacity`
  position: relative;
  background-color: #fff;
  height: 44px;
  padding: 10px 16px 10px 30px;
`;

export const DropdownText = styled.Text`
  color: #333;
  font-family: 'MuktaVaani';
  font-size: 16px;
`;

export const CheckView = styled.View`
  position: absolute;
  left: 0;
  top: 0;
  align-items: center;
  justify-content: center;
  height: 44px;
  width: 30px;
`;

export const Divider = styled.View`
  height: 1px;
  background-color: #e0e0e0;
  margin: 0;
`;

export const DropdownOverlay = styled.View`
  padding: 20px;
  justify-content: center;
  align-items: center;
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
`;

export const ScrollableOptionsView = styled.ScrollView.attrs(() => ({
  contentContainerStyle: {
    flexGrow: 1,
  },
}))`
  flex-grow: 0;
  flex-shrink: 1;
`;
