import { ContainerStyles } from './styles';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ContainerProps {
  children: React.ReactNode;
  edges?: Array<'top' | 'right' | 'bottom' | 'left'>;
}

export default function Container({
  children,
  edges = ['top', 'bottom'],
}: ContainerProps) {
  return (
    <ContainerStyles>
      <SafeAreaView edges={edges}>{children}</SafeAreaView>
    </ContainerStyles>
  );
}
