import { ProgressBarContainer, ProgressBarFill } from "./styles";
import { LinearGradient } from 'expo-linear-gradient';

export default function ProgressBar({ progress }: { progress: number }) {
  return (
    <ProgressBarContainer>
      <LinearGradient
        colors={['#0E7E61', '#BCD7EA', '#E9631A', '#A92A73']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{ width: `${progress}%`, height: '100%', borderRadius: 10 }}
      />
    </ProgressBarContainer>
  );
}
