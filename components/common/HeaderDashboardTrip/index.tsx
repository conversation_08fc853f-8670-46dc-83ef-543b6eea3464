import React from 'react';
import { HeaderDashboardContainer, HeaderDashboardTitle, HeaderButton } from './styles';
import { TouchableOpacity, View } from 'react-native';
import Search from '@/assets/svg/search.svg';
import Bell from '@/assets/svg/bell.svg';
import { ArrowLeft, EllipsisIcon } from 'lucide-react-native';
import { router } from 'expo-router';
interface HeaderDashboardProps {
  title: string;
  onBackPress?: () => void;
  backButton?: boolean;
}

export default function HeaderDashboard({
  title,
  backButton = false,
  onBackPress,
}: HeaderDashboardProps) {
  return <HeaderDashboardContainer>
    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
      {backButton && <TouchableOpacity onPress={onBackPress}><ArrowLeft size={24} color="#000" /></TouchableOpacity>}
      <HeaderDashboardTitle>{title}</HeaderDashboardTitle>
    </View>
    <View style={{ flexDirection: 'row', gap: 8 }}>
      <HeaderButton>
        <Search />
      </HeaderButton>
      <HeaderButton>
        <EllipsisIcon />
      </HeaderButton>
    </View>
  </HeaderDashboardContainer>;
}
