import { DefaultTheme } from 'styled-components';
import styled from 'styled-components/native';

export const CheckboxButton = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
`;

export const CheckboxComponent = styled.View`
  border-width: 2px;
  border-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[500]};
  border-radius: 2px;
  justify-content: center;
  align-items: center;
`;

export const CheckboxLabel = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
  line-height: 24px;
  margin-left: 8px;
`;
