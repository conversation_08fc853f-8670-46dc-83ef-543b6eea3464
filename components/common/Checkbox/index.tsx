import CheckWhite from '@/assets/svg/check-white.svg';
import { FC, CSSProperties } from 'react';
import { useTheme } from 'styled-components';
import { CheckboxButton, CheckboxComponent, CheckboxLabel } from './styles';

interface CheckboxProps {
  label?: string;
  checked?: boolean;
  size?: number;
  onToggle?: () => void;
  containerStyle?: CSSProperties;
}

export const Checkbox: FC<CheckboxProps> = ({
  label,
  checked,
  size = 20,
  onToggle,
  containerStyle,
}) => {
  const theme = useTheme();

  return (
    <CheckboxButton onPress={onToggle} activeOpacity={1} style={containerStyle}>
      <CheckboxComponent
        style={{
          width: size,
          height: size,
          ...(checked && { backgroundColor: theme.brand.green[500] }),
        }}
      >
        {checked && <CheckWhite width="75%" height="75%" />}
      </CheckboxComponent>
      {label && <CheckboxLabel>{label}</CheckboxLabel>}
    </CheckboxButton>
  );
};
