import { brand } from '@/constants/Colors';
import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components/native';

interface ButtonProps {
  px?: number;
  py?: number;
}

export const ButtonContainer = styled.TouchableOpacity<ButtonProps>`
  height: 48px;
  width: 100%;
  opacity: ${({ disabled }: { disabled?: boolean }) => (disabled ? 0.5 : 1)};
  background-color: ${({
    theme,
    buttonColor,
  }: {
    theme: DefaultTheme;
    buttonColor: string;
  }) => (buttonColor === 'red' ? theme.brand.red : theme.brand.green[500])};
  border-radius: 100px;
  padding-top: ${(props: ButtonProps) => props.py || 10}px;
  padding-bottom: ${(props: ButtonProps) => props.py || 10}px;
  padding-left: ${(props: ButtonProps) => props.px || 10}px;
  padding-right: ${(props: ButtonProps) => props.px || 10}px;
  align-items: center;
  justify-content: center;
`;

export const ButtonText = styled.Text<{
  isLined?: boolean;
  isInverted?: boolean;
  isRed?: boolean;
}>`
  color: ${({
    isLined,
    isInverted,
    isRed,
  }: {
    isLined?: boolean;
    isInverted?: boolean;
    isRed?: boolean;
  }) =>
    isRed
      ? ({ theme }: { theme: DefaultTheme }) => theme.brand.red
      : isLined
      ? ({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]
      : isInverted
      ? ({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]
      : '#fff'};
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
`;

export const ButtonTransparent = styled(ButtonContainer)`
  background-color: transparent;
`;

export const ButtonRed = styled(ButtonContainer)`
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.red};
`;

export const ButtonLined = styled(
  ButtonContainer<{
    isRed?: boolean;
  }>,
)`
  border-width: 1px;
  background-color: transparent;
  border-color: ${({ isRed }: { isRed?: boolean }) =>
    ({ theme }: { theme: DefaultTheme }) =>
      isRed ? theme.brand.red : theme.brand.green[500]};
`;

export const ButtonInverted = styled(ButtonContainer)`
  border-width: 1px;
  background-color: #fff;
  border-color: #fff;
`;

export const ButtonNav = styled(ButtonContainer)<{ isActive?: boolean }>`
  background-color: transparent;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[500]};
  border-radius: 100px;
  padding: 0 16px;
  flex: 1;
  height: 32px;
  background-color: ${({ isActive, theme }: { isActive?: boolean, theme: DefaultTheme }) =>
    isActive ? theme.brand.green[500] : 'transparent'};
`;

export const ButtonNavText = styled(ButtonText)<{ isActive?: boolean }>`
  color: ${({ isActive, theme }: { isActive?: boolean, theme: DefaultTheme }) =>
    isActive ? '#ffffff' : theme.brand.green[500]};
  font-size: 12px;
`;
