import { TextInputProps, View } from 'react-native';
import { ErrorText, InputContainer, IconContainer, InputGhostContainer } from './styles';

// Define a new interface that extends TextInputProps and includes the error property
interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  placeholder?: string;
  isDisabled?: boolean;
  iconComponent?: React.ReactNode;
  value?: string;
  onChangeText?: (text: string) => void;
}

const BaseInput = ({ 
  error, 
  placeholder, 
  isDisabled, 
  iconComponent, 
  value, 
  onChangeText,
  style,
  ...props 
}: InputProps) => {
  return (
    <View>
      <View>
        <InputContainer
          autoCapitalize="none"
          placeholder={placeholder}
          editable={!isDisabled}
          onChangeText={onChangeText}
          value={value}
          style={[
            iconComponent && { paddingRight: 50 },
            style
          ]}
          {...props}
        />
        {iconComponent && (
          <IconContainer>
            {iconComponent}
          </IconContainer>
        )}
      </View>
      {error && <ErrorText>{error}</ErrorText>}
    </View>
  );
};

export default function Input(props: InputProps) {
  return <BaseInput {...props} />;
}

export const InputGhost = ({ placeholder, value, onChangeText, ...props }: InputProps) => {
  return (
    <InputGhostContainer 
      placeholder={placeholder} 
      value={value} 
      onChangeText={onChangeText} 
      {...props} 
    />
  );
}
