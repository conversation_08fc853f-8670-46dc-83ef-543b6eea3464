import React, { useState } from 'react';
import { Controller, Control, FieldValues, Path } from 'react-hook-form';
import Input from '@/components/common/Input';
import { TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ControlledInputProps<T extends FieldValues> {
  control: Control<T>;
  name: string;
  rules?: object;
  placeholder: string;
  defaultValue?: string;
  error?: string;
  isDisabled?: boolean;
  secureTextEntry?: boolean;
  onInputChange?: (value: string) => void;
  iconComponent?: React.ReactNode;
}

const PasswordToggle = ({ showPassword, onToggle }: { showPassword: boolean; onToggle: () => void }) => (
  <TouchableOpacity
    onPress={onToggle}
    style={{
      position: 'absolute',
      right: 8,
      top: '50%',
      transform: [{ translateY: -12 }],
    }}
  >
    <Ionicons
      name={showPassword ? 'eye-off' : 'eye'}
      size={24}
      color="#000000"
    />
  </TouchableOpacity>
);

const ControlledInput = <T extends FieldValues>({
  control,
  name,
  rules,
  placeholder,
  defaultValue,
  error,
  secureTextEntry,
  isDisabled,
  onInputChange,
  iconComponent,
}: ControlledInputProps<T>) => {
  const [showPassword, setShowPassword] = useState(false);

  const handlePasswordToggle = () => setShowPassword(!showPassword);

  const renderIcon = () => {
    if (secureTextEntry) {
      return <PasswordToggle showPassword={showPassword} onToggle={handlePasswordToggle} />;
    }
    return iconComponent;
  };

  return (
    <Controller
      control={control}
      name={name as Path<T>}
      rules={rules}
      render={({ field: { onChange, onBlur, value } }) => (
        <Input
          placeholder={placeholder}
          onBlur={onBlur}
          onChangeText={(t) => {
            onChange(t);
            onInputChange?.(t);
          }}
          value={value}
          secureTextEntry={secureTextEntry && !showPassword}
          error={error}
          isDisabled={isDisabled}
          iconComponent={renderIcon()}
        />
      )}
    />
  );
};

export default ControlledInput;
