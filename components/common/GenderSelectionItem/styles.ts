import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components';

export const Container = styled.TouchableOpacity<{ isSelected: boolean }>`
  border-radius: 8px;
  border-width: ${({ isSelected, theme }: { isSelected: boolean, theme: DefaultTheme }) => isSelected ? 2 : 1}px;
  height: 52px;
  min-width: 47%;
  align-items: center;
  justify-content: center;
  border-color: ${({ isSelected, theme }: { isSelected: boolean, theme: DefaultTheme }) => isSelected ? theme.brand.green[300] : '#E2E2E2'};
`;

export const Content = styled.View`
  flex: 1;
  width: 100%;
  align-items: center;
  justify-content: center;
`;

export const Text = styled.Text`
  font-size: 14px;
  font-family: MuktaVaaniMedium;
  line-height: 20px;
  color: #333333;
`;
