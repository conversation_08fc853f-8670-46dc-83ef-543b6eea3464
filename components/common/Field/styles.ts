import styled, { DefaultTheme } from 'styled-components/native';

export const FieldGroupView = styled.View`
  position: relative;
  border-radius: 16px;
  border-width: 1px;
  border-color: #a1a1a1;
`;

export const FieldView = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  max-height: 56px;
`;

export const FieldLabel = styled.Text`
  color: #333333;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
`;

export const DeleteButton = styled.TouchableOpacity`
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0px;
  top: -12px;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.red};
  height: 24px;
  width: 24px;
  border-radius: 24px;
  z-index: 99999;
  elevation: 5;
`;
