import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components';

export const HeaderDashboardContainer = styled.View`
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    margin-bottom: 8px;
`;

export const HeaderDashboardTitle = styled.Text`
    font-family: CormorantGaramondSemiBold;
    font-size: 32px;
`;

export const HeaderButton = styled.TouchableOpacity`
    background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[100]};
    width: 40px;
    height: 40px;
    border-radius: 100px;
    justify-content: center;
    align-items: center;
`;
