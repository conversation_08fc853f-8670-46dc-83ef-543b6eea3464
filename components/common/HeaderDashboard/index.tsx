import React from 'react';
import {
  HeaderDashboardContainer,
  HeaderDashboardTitle,
  HeaderButton,
} from './styles';
import { View } from 'react-native';
import Search from '@/assets/svg/search.svg';
import Bell from '@/assets/svg/bell.svg';
import { ArrowLeft } from 'lucide-react-native';
import { router } from 'expo-router';
interface HeaderDashboardProps {
  title: string;
  onBackPress?: () => void;
  backButton?: boolean;
}

export default function HeaderDashboard({
  title,
  backButton = false,
  onBackPress,
}: HeaderDashboardProps) {
  return (
    <HeaderDashboardContainer>
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          gap: 8,
          marginRight: 10,
        }}
      >
        {backButton && (
          <ArrowLeft onPress={onBackPress} size={24} color="#000" />
        )}
        <HeaderDashboardTitle numberOfLines={2}>{title}</HeaderDashboardTitle>
      </View>
      <View
        style={{ flexDirection: 'row', gap: 8, marginLeft: 'auto' }}
      >
        <HeaderButton>
          <Search />
        </HeaderButton>
        <HeaderButton>
          <Bell />
        </HeaderButton>
      </View>
    </HeaderDashboardContainer>
  );
}
