import { ReactNode } from 'react';
import { TextDefault, TitleSection as TitleSectionStyle } from './styles';
import { useTheme } from 'styled-components/native';

export default function TextComponent({ string }: { string: ReactNode }) {
  return <TextDefault>{string}</TextDefault>;
}

export function TextError({
  string,
  isValid,
}: {
  string: string;
  isValid?: boolean;
}) {
  const theme = useTheme();

  return (
    <TextDefault style={{ color: isValid ? theme.brand.green[500] : theme.brand.red, fontSize: 12 }}>
      {string}
    </TextDefault>
  );
}

export function SubtitleText({ string }: { string: ReactNode }) {
  return <TextDefault style={{ fontSize: 12 }}>{string}</TextDefault>;
}

export const TitleSection = ({ string }: { string: ReactNode }) => {
  return <TitleSectionStyle>{string}</TitleSectionStyle>;
};
