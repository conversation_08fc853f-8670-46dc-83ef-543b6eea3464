import React from 'react';
import { Animated, StyleSheet, View, useWindowDimensions } from 'react-native';

type PaginatorProps = {
  data: any[];
  scrollX: Animated.Value;
};

const Paginator = ({ data, scrollX }: PaginatorProps) => {
  const { width } = useWindowDimensions();
  return (
    <View style={styles.container}>
      {data.map((_, i) => {
        const inputRange = [(i - 1) * width, i * width, (i + 1) * width];
        const dotColor = scrollX.interpolate({
          inputRange,
          outputRange: ['#fff', '#000', '#fff'],
          extrapolate: 'clamp',
        });

        return (
          <Animated.View
            key={i.toString()}
            style={[styles.dot, { backgroundColor: dotColor }]}
          />
        );
      })}
    </View>
  );
};

export default Paginator;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'center',
    height: 64,
    paddingTop: 10,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    borderColor: '#000',
    borderWidth: 1,
  },
});
