import { Logo as LogoImage } from '@/constants/images';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useRouter } from 'expo-router';
import { View } from 'react-native';
import { HeaderPageContainer, Logo, TitleText } from './styles';

type HeaderPageProps = {
  title?: string;
  disableBack?: boolean;
  noLogo?: boolean;
  rightComponent?: React.ReactNode;
  backCallback?: () => void;
};

export default function HeaderPage({
  title,
  disableBack = false,
  noLogo = false,
  rightComponent,
  backCallback,
}: HeaderPageProps) {
  const router = useRouter();
  const canGoBack = router.canGoBack();
  return (
    <HeaderPageContainer>
      {!disableBack && canGoBack ? (
        <MaterialIcons
          name="arrow-back"
          size={24}
          color="black"
          onPress={() => {
            if (backCallback) {
              backCallback();
              return;
            }
            router.back();
          }}
        />
      ) : (
        <View />
      )}
      <View
        style={{
          position: 'absolute',
          left: '50%',
          transform: [{ translateX: '-50%' }],
        }}
      >
        {title ? (
          <TitleText>{title}</TitleText>
        ) : (
          <>{!noLogo && <Logo resizeMode="cover" source={LogoImage} />}</>
        )}
      </View>
      {rightComponent || <View />}
    </HeaderPageContainer>
  );
}
