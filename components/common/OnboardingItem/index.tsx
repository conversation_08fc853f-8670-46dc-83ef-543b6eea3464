import {
  Image,
  StyleSheet,
  Text,
  View,
  useWindowDimensions,
} from 'react-native';
import {
  ImageContainer,
  OnboardingContent,
  OnboardingDescription,
  OnboardingTitle,
} from './style';

type OnboardingItemProps = {
  item: Record<string, any>;
};

export default function OnboardingItem({ item }: OnboardingItemProps) {
  const { width } = useWindowDimensions();

  return (
    <View style={{ width, padding: 45, alignItems: 'center' }}>
      <ImageContainer>
        <Image
          source={item.image}
          style={{
            width: '100%',
            height: '100%',
            borderRadius: 100,
          }}
        />
      </ImageContainer>
      <OnboardingContent>
        <OnboardingTitle>{item.title}</OnboardingTitle>
        <OnboardingDescription>{item.description}</OnboardingDescription>
      </OnboardingContent>
    </View>
  );
}
