import { DefaultTheme } from 'styled-components';
import styled from 'styled-components/native';

export const TodayContainer = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 16px;
`;

export const TodayText = styled.Text`
  font-family: MuktaVaaniSemiBold;
  font-size: 16px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;

export const TodayTag = styled.View`
  border-radius: 9px;
  padding: 0px 10px;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.magenta};
`;

export const TodayTagText = styled.Text`
  font-family: MuktaVaani;
  font-size: 12px;
  color: #fff;
`;
