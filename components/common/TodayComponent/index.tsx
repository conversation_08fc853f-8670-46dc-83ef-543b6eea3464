import React from 'react';
import { TodayContainer, TodayTag, TodayTagText, TodayText } from './styles';
import { useQuery } from '@tanstack/react-query';
import { getUpcomingTrips } from '@/methods/trips';
import moment from 'moment';

export default function TodayComponent() {
  const { data: trips, refetch, isLoading } = getUpcomingTrips();

  const today = moment().startOf('day');
  const endOfWeek = moment().endOf('week');

  // Calculate how many trips are within this week
  const tripsThisWeek = React.useMemo(() => {
    if (isLoading || !trips?.data?.events) {
      return 0;
    }

    return trips.data.events.filter((trip: any) => {
      const tripStartDate = moment(trip.startDate);
      return tripStartDate.isBetween(today, endOfWeek, 'day', '[]'); // inclusive of today and end of week
    }).length;
  }, [trips, isLoading, today, endOfWeek]);

  // Check if there are any trips at all
  const hasAnyTrips = React.useMemo(() => {
    return !isLoading && trips?.data?.events && trips.data.events.length > 0;
  }, [trips, isLoading]);

  return (
    <TodayContainer>
      <TodayText>Today</TodayText>
      {hasAnyTrips && (
        <TodayTag>
          <TodayTagText>
            {tripsThisWeek > 0 ? `You have ${tripsThisWeek} trip${tripsThisWeek === 1 ? '' : 's'} this week!` : 'No trips this week!'}
          </TodayTagText>
        </TodayTag>
      )}
    </TodayContainer>
  );
}
