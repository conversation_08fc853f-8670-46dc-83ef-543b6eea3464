import {
  <PERSON>odBoard01,
  <PERSON>od<PERSON>oard02,
  <PERSON>odBoard03,
  <PERSON>odBoard04,
  <PERSON>odBoard05,
  <PERSON>odBoard06,
  <PERSON>odBoard07,
  <PERSON>odBoard08,
  <PERSON>odBoard09,
  <PERSON>odBoard10,
  <PERSON>odBoard11,
  <PERSON>odBoard12,
} from '@/constants/images';
import { fetchStyleQuiz } from '@/methods/style-quiz';
import { MoodBoardImage, RowContainer, StyleMoodBoardContainer, TouchableImage } from './styles';
import { TouchableWithoutFeedback, TouchableOpacity, View } from 'react-native';
import { useState } from 'react';
import { useTheme } from 'styled-components/native';

type StyleMoodBoardProps = {
  rowsToShow?: number;
  onPress?: () => void;
  selectable?: boolean;
  onImageSelect?: (image: string, index: number) => void;
};

export const StyleMoodBoard = ({
  rowsToShow = 4,
  onPress,
  selectable = false,
  onImageSelect
}: StyleMoodBoardProps) => {
  const { data: styleQuizData } = fetchStyleQuiz();
  const quizAnswers = styleQuizData?.data?.quiz?.filter((quiz: any) => quiz.answer);
  const images = quizAnswers?.map((quiz: any) => quiz.image).filter(Boolean) || [];

  const theme = useTheme();

  // State to store all selected images
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  // Create dynamic rows based on available images
  const createDynamicRows = (imageList: string[]) => {
    if (imageList.length === 0) {
      // Fallback to original images if no quiz images available
      return [
        {
          id: 1,
          images: [MoodBoard01, MoodBoard02, MoodBoard03],
        },
        {
          id: 2,
          images: [MoodBoard04, MoodBoard05],
        },
        {
          id: 3,
          images: [MoodBoard06, MoodBoard07, MoodBoard08, MoodBoard09],
        },
        {
          id: 4,
          images: [MoodBoard10, MoodBoard11, MoodBoard12],
        },
      ];
    }

    // Distribute images across rows dynamically
    const rows = [];
    let imageIndex = 0;

    for (let rowId = 1; rowId <= 4 && imageIndex < imageList.length; rowId++) {
      const imagesInRow = [];
      const maxImagesInRow = rowId === 1 ? 3 : rowId === 2 ? 2 : rowId === 3 ? 4 : 3;

      for (let i = 0; i < maxImagesInRow && imageIndex < imageList.length; i++) {
        imagesInRow.push(imageList[imageIndex]);
        imageIndex++;
      }

      if (imagesInRow.length > 0) {
        rows.push({
          id: rowId,
          images: imagesInRow,
        });
      }
    }

    return rows;
  };

  const rows = createDynamicRows(images);
  const rowsToRender = rows.slice(0, rowsToShow);

  const handleImagePress = (image: string, index: number) => {
    if (selectable) {
      // Toggle image selection
      setSelectedImages(prevSelected => {
        const isSelected = prevSelected.includes(image);
        if (isSelected) {
          // Remove image if already selected
          return prevSelected.filter(img => img !== image);
        } else {
          // Add image if not selected
          return [...prevSelected, image];
        }
      });

      // Call the parent's onImageSelect callback if provided
      if (onImageSelect) {
        onImageSelect(image, index);
      }
    }
  };

  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <StyleMoodBoardContainer>
        {rowsToRender.map((row) => (
          <RowContainer key={row.id}>
            {row.images.map((image, index) => {
              const isSelected = selectedImages.includes(image);

              return selectable ? (
                <TouchableImage
                  key={index}
                  onPress={() => handleImagePress(image, index)}
                  activeOpacity={0.7}
                >
                  <MoodBoardImage
                    source={typeof image === 'string' ? { uri: image } : image}
                  />
                  {isSelected && (
                    <View
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        borderWidth: 3,
                        borderColor: theme.brand.green[500],
                        borderRadius: 8,
                        backgroundColor: 'rgba(10, 92, 71, 0.2)',
                      }}
                    />
                  )}
                </TouchableImage>
              ) : (
                <MoodBoardImage
                  key={index}
                  source={typeof image === 'string' ? { uri: image } : image}
                />
              );
            })}
          </RowContainer>
        ))}
      </StyleMoodBoardContainer>
    </TouchableWithoutFeedback>
  );
};
