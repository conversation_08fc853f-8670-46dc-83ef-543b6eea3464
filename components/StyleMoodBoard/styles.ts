import styled from "styled-components/native";

export const StyleMoodBoardContainer = styled.View`
  flex: 1;
  gap: 8px;
`;

export const RowContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
`;

export const TouchableImage = styled.TouchableOpacity`
  position: relative;
  flex: 1;
  height: 168px;
  border-radius: 8px;
  overflow: hidden;
`;

export const MoodBoardImage = styled.Image`
  flex: 1;
  height: 168px;
  border-radius: 8px;
  overflow: hidden;
`;