import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components/native';

export const DatePickerModalOverlay = styled.TouchableOpacity`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.4);
  justifycontent: flex-end;
`;

export const DatePickerModalContent = styled.TouchableOpacity`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding-bottom: 20px;
`;

export const DatePickerHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom-width: 1px;
  border-bottom-color: #eee;
`;

export const DatePickerHeaderButton = styled.Text`
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
`;

export const DatePickerTitle = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
`;

export const DatePickerBody = styled.View`
  justify-content: center;
  align-items: center;
`;
