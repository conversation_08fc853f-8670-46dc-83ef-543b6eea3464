import LeftChevronBlack from '@/assets/svg/left-chevron-black.svg';
import RightChevronBlack from '@/assets/svg/right-chevron-black.svg';
import RNDateTimePicker from '@react-native-community/datetimepicker';
import { useCallback, useEffect, useState } from 'react';
import {
  GestureResponderEvent,
  Modal,
  Platform,
  TouchableOpacity
} from 'react-native';
import DateTimePicker, { DateType, useDefaultStyles } from 'react-native-ui-datepicker';
import { useTheme } from 'styled-components/native';
import {
  DatePickerBody,
  DatePickerHeader,
  DatePickerHeaderButton,
  DatePickerModalContent,
  DatePickerModalOverlay,
  DatePickerTitle,
} from './styles';

interface DatePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onDateChange: (startDate?: Date | undefined, endDate?: Date | undefined) => void;
  value?: Date;
  title?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'single' | 'range' | 'multiple';
  startDate?: Date;
  endDate?: Date;
}

export default function DatePickerModal({
  visible,
  onClose,
  onDateChange,
  value,
  title,
  minimumDate,
  maximumDate,
  mode = 'single',
  startDate,
  endDate,
}: DatePickerModalProps) {
  const theme = useTheme();
  const defaultStyles = useDefaultStyles();
  const [selectedDates, setSelectedDates] = useState<DateType[]>([startDate, endDate].filter(Boolean) as DateType[]);
  const [selectedDate, setSelectedDate] = useState<DateType>(value || new Date());
  const [tempDate, setTempDate] = useState<Date | undefined>(startDate);
  const [tempEndDate, setTempEndDate] = useState<Date | undefined>(endDate);

  // Update selectedDate when value prop changes
  useEffect(() => {
    if (value) {
      setSelectedDate(value);
    }
  }, [value]);

  // Update tempDate when startDate prop changes
  useEffect(() => {
    if (startDate) {
      setTempDate(startDate);
    }
  }, [startDate]);

  // Update tempEndDate when endDate prop changes
  useEffect(() => {
    if (endDate) {
      setTempEndDate(endDate);
    }
  }, [endDate]);

  const handleDone = useCallback(() => {
    if (mode === 'multiple') {
      // For multiple mode, we'll use the first selected date
      const dateToUse = selectedDates[0]?.toString() || tempDate?.toString();
      dateToUse && onDateChange(new Date(dateToUse));
    } else if (mode === 'range') {
      const startDateToUse = tempDate?.toString();
      const endDateToUse = tempEndDate?.toString();
      startDateToUse && onDateChange(new Date(startDateToUse), endDateToUse ? new Date(endDateToUse) : undefined);
    } else {
      // For single mode, use the selectedDate
      const dateToUse = selectedDate?.toString();
      dateToUse && onDateChange(new Date(dateToUse));
    }
    onClose();
  }, [onDateChange, onClose, tempDate, mode, selectedDates, tempEndDate, selectedDate]);

  const renderDateTimePicker = () => {
    const gray = '#f4f4f5';
    const borderGray = '#e4e4e7';
    const fontStyles = {
      fontFamily: 'MuktaVaani',
      fontSize: 16,
      color: '#000000',
    };

    const commonProps = {
      minDate: minimumDate,
      maxDate: maximumDate,
      styles: {
        ...defaultStyles,
        // Force light mode colors
        day_label: { ...fontStyles },
        month_label: { ...fontStyles },
        year_label: { ...fontStyles },
        time_label: { ...fontStyles },
        outside_label: { ...fontStyles, color: '#CCCCCC' },
        today: { backgroundColor: gray },
        today_label: { ...fontStyles },
        month: { borderColor: borderGray, borderWidth: 1, borderRadius: 4 },
        year: { borderColor: borderGray, borderWidth: 1, borderRadius: 4 },
        year_selector: { borderColor: borderGray },
        time_selector: { borderColor: borderGray },
        day_selector: { borderColor: borderGray },
        outside: { borderColor: borderGray },
        month_selector_label: { ...fontStyles, fontSize: 20 },
        year_selector_label: { ...fontStyles, fontSize: 20 },
        time_selector_label: { ...fontStyles, fontSize: 20 },
        active_year: { backgroundColor: 'transparent', borderColor: borderGray },
        selected: { backgroundColor: theme.brand.green[500] },
        selected_label: { color: '#FFFFFF' },
        selected_month: { backgroundColor: theme.brand.green[500], borderColor: theme.brand.green[500] },
        selected_month_label: { color: '#FFFFFF' },
        selected_year: { backgroundColor: theme.brand.green[500], borderColor: theme.brand.green[500] },
        selected_year_label: { color: '#FFFFFF' },
        range_start_label: { color: '#FFFFFF' },
        range_end_label: { color: '#FFFFFF' },
        range_fill: { backgroundColor: 'transparent' },
        range_middle: { backgroundColor: theme.brand.green[100] },
        range_middle_label: { color: '#000000' },
        button_next: { backgroundColor: 'transparent', color: '#000000' },
        button_prev: { backgroundColor: 'transparent', color: '#000000' },
      },
      calendar: "gregory" as const,
    };

    const components = {
      IconPrev: <LeftChevronBlack />,
      IconNext: <RightChevronBlack />,
    }

    switch (mode) {
      case 'single':
        return (
          <RNDateTimePicker
            value={selectedDate instanceof Date ? selectedDate : new Date()}
            themeVariant="light"
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={(event, date) => {
              if (date && event.type !== 'dismissed') {
                setSelectedDate(date);
              }
            }}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
            style={{
              backgroundColor: 'white',
              width: '100%',
            }}
          />
        );
      case 'range':
        return (
          <DateTimePicker
            {...commonProps}
            startDate={tempDate}
            endDate={tempEndDate}
            onChange={({ startDate, endDate }: { startDate: DateType; endDate: DateType }) => {
              // If we have both dates, update them
              if (startDate && endDate) {
                setTempDate(new Date(startDate.toString()));
                setTempEndDate(new Date(endDate.toString()));
              }
              // If we only have start date, update it
              else if (startDate) {
                setTempDate(new Date(startDate.toString()));
              }
              // If we only have end date, update it
              else if (endDate) {
                setTempEndDate(new Date(endDate.toString()));
              }
            }}
            mode="range"
            components={components}
          />
        );
      case 'multiple':
        return (
          <DateTimePicker
            {...commonProps}
            dates={selectedDates}
            onChange={({ dates }: { dates: DateType[] }) => {
              if (dates && dates.length > 0 && dates[0]) {
                setSelectedDates(dates);
                setTempDate(new Date(dates[0].toString()));
              }
            }}
            mode="multiple"
            components={components}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} transparent animationType={Platform.OS === 'ios' ? 'fade' : 'none'}>
      <DatePickerModalOverlay onPress={handleDone} activeOpacity={1}>
        <DatePickerModalContent activeOpacity={1} onPress={(e: GestureResponderEvent) => {
          e.stopPropagation();
          e.preventDefault();
        }}>
          <DatePickerHeader>
            <TouchableOpacity onPress={onClose}>
              <DatePickerHeaderButton>Cancel</DatePickerHeaderButton>
            </TouchableOpacity>
            <DatePickerTitle>{title}</DatePickerTitle>
            <TouchableOpacity onPress={handleDone}>
              <DatePickerHeaderButton>
                Done
              </DatePickerHeaderButton>
            </TouchableOpacity>
          </DatePickerHeader>
          <DatePickerBody>
            {renderDateTimePicker()}
          </DatePickerBody>
        </DatePickerModalContent>
      </DatePickerModalOverlay>
    </Modal>
  );
}
