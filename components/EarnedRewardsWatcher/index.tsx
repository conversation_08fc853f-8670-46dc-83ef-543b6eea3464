import { useEarnedRewardsModal } from '@/context/EarnedRewardsContext';
import { EarnedReward } from '@/lib/collections/earnedRewards';
import Meteor, { Mongo, useTracker } from '@meteorrn/core';
import { FC, useEffect, useRef } from 'react';

export const EarnedRewardsWatcher: FC = () => {
  const { showRewardModal } = useEarnedRewardsModal();
  const previousRewardsRef = useRef<string[]>([]);

  const { earnedRewardsWithInfo } = useTracker(() => {
    // Define the client-side collection for EarnedRewards
    const EarnedRewards = new Mongo.Collection('earned-rewards');

    // Subscribe to unseen earned rewards for the current user
    const earnedRewardsHandle = Meteor.subscribe('earnedRewards-getUnseen');

    // Fetch unseen earned rewards, sorted by most recent
    const EarnedRewardsCursor = EarnedRewards.find(
      { seen: { $ne: true } },
      { sort: { createdAt: -1 } }
    );
    const earnedRewards = EarnedRewardsCursor.fetch() as EarnedReward[];

    // Extract unique reward codes from earned rewards
    const rewardCodes = Array.from(new Set(earnedRewards.map((e: any) => e.rewardCode)));

    // Define the client-side collection for Rewards
    const Rewards = new Mongo.Collection('rewards');

    // Subscribe to only the rewards related to those reward codes
    const rewardsHandle = Meteor.subscribe('rewards-byCodes', rewardCodes);

    // Fetch the matching reward documents
    const rewards = Rewards.find({ code: { $in: rewardCodes } }).fetch();

    // Create a quick lookup map of rewardCode → reward document
    const rewardMap = new Map(rewards.map((r: any) => [r.code, r]));

    // Merge each earnedReward with its corresponding reward info
    const earnedRewardsWithInfo = earnedRewards.map((er: any) => ({
      ...er,
      rewardInfo: rewardMap.get(er.rewardCode),
    }));

    // Optional loading flag (not currently used in this snippet)
    const loading = !earnedRewardsHandle.ready() || !rewardsHandle.ready();

    return { earnedRewardsWithInfo, loading };
  });

  useEffect(() => {
    // Only show modal if there are unseen rewards and they're different from previous ones
    if (earnedRewardsWithInfo.length > 0) {
      const currentRewardIds = earnedRewardsWithInfo.map((reward: EarnedReward) => reward._id);
      const previousRewardIds = previousRewardsRef.current;

      // Check if we have new rewards (different from what we've seen before)
      const hasNewRewards = currentRewardIds.some((id: string) => !previousRewardIds.includes(id));

      if (hasNewRewards) {
        console.log('🎉 New earned rewards detected, showing modal:', earnedRewardsWithInfo);
        showRewardModal(earnedRewardsWithInfo);
        // Update the ref to track these rewards as "seen" by the watcher
        previousRewardsRef.current = currentRewardIds;
      }
    } else {
      // Clear the ref when there are no unseen rewards
      previousRewardsRef.current = [];
    }
  }, [earnedRewardsWithInfo, showRewardModal]);

  // This component doesn't render anything visible
  // It just maintains the subscription in the background and watches for new rewards
  return null;
};

export default EarnedRewardsWatcher; 
