import UpChevronBlack from '@/assets/svg/up-chevron-black.svg';
import React, { FC, useEffect, useRef, useState } from 'react';
import { Animated, LayoutAnimation, View } from 'react-native';
import { useTheme } from 'styled-components/native';
import {
  AccordionInnerContainer,
  AccordionTitle,
  AccordionTitleContainer,
} from './styles';

interface AccordionProps {
  title?: string;
  children?: React.ReactNode;
  defaultExpanded?: boolean;
}

export const Accordion: FC<AccordionProps> = ({
  title,
  children,
  defaultExpanded = false,
}: AccordionProps) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const heightAnim = useRef(new Animated.Value(0)).current;
  const [containerHeight, setContainerHeight] = useState(0);

  const [expanded, setExpanded] = useState(defaultExpanded);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(rotateAnim, {
        toValue: expanded ? 0 : 1,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(heightAnim, {
        toValue: expanded ? containerHeight : 0,
        duration: 150,
        useNativeDriver: false,
      }),
    ]).start();

    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }, [expanded, containerHeight]);

  return (
    <View>
      {title && (
        <AccordionTitleContainer
          activeOpacity={1}
          onPress={() => {
            setExpanded((prev) => !prev);
          }}
        >
          <AccordionTitle>{title}</AccordionTitle>
          <Animated.View
            style={{
              transform: [
                {
                  rotate: rotateAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '180deg'],
                  }),
                },
              ],
            }}
          >
            <UpChevronBlack />
          </Animated.View>
        </AccordionTitleContainer>
      )}
      <AccordionInnerContainer
        as={Animated.View}
        style={{
          maxHeight: heightAnim,
          overflow: 'hidden',
        }}
      >
        <View
          onLayout={(event) => {
            const height = event.nativeEvent.layout.height;
            setContainerHeight(height);
          }}
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            gap: 8,
          }}
        >
          {children}
        </View>
      </AccordionInnerContainer>
    </View>
  );
};
