import { View, Text, TouchableOpacity } from 'react-native';
import styled , { DefaultTheme } from 'styled-components/native';
import Input from '../common/Input';

export const AddClothsModalContainer = styled(View)`
  height: 40%;
  background-color: white;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px;
`;

export const AddClothsModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const HeaderText = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaani;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;


export const AddClothsModalTitle = styled(Text)`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 20px;
`;

export const AddClothsModalInput = styled(Input)`
  width: 100%;
  height: 56px;
  border-radius: 10px;
  border-width: 1px;
  border-color: #000;
`;


export const ClothItemContainer = styled(View)<{ width: number }>`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #EBEBEB;
  border-radius: 10px;
  margin: 5px;
  height: ${({ width }: { width: number }) => (width / 3) - 10}px;
  width: ${({ width }: { width: number }) => (width / 3) - 10}px;
`;

export const AddClothItemContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #EBEBEB;
  border-radius: 10px;
  margin: 5px;
  height: ${({ width }: { width: number }) => (width / 3) - 10}px;
  width: ${({ width }: { width: number }) => (width / 3) - 10}px;
`;


export const ClothItemImage = styled.Image`
  width: 50px;
  height: 50px;
  border-radius: 10px;
`;

export const ClothItemName = styled(Text)`
  font-size: 16px;
  font-family: MuktaVaani;
`;

export const ClothesContainer = styled(View)`
  flex-direction: row;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
`;

export const CheckBox = styled(View)`
  width: 20px;
  height: 20px;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  border-width: 1px;
  border-color: #000;
  position: absolute;
  right: 10px;
  bottom: 10px;
  background-color: #FFF;
`;

export const CategoryItem = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 10px;
  margin: 5px;
  paddingHorizontal: 16px;
  paddingVertical: 8px;
  background-color: ${({ isSelected, theme }: { isSelected: boolean, theme: DefaultTheme }) => isSelected ? theme.brand.green[500] : '#F5F5F5'};
`;

export const CategoryItemText = styled(Text)<{ isSelected: boolean }>`
  font-size: 16px;
  font-family: MuktaVaani;
  color: ${({ isSelected }: { isSelected: boolean }) => isSelected ? '#FFF' : '#000'};
`;

export const HeaderTitle = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaaniSemiBold;
  color: #000;
`;
