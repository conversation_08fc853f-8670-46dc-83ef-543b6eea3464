import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator, BackHandler, Platform } from 'react-native';
import Modal from 'react-native-modal';
import styled from 'styled-components/native';

// Styled components
const ModalContainer = styled.View`
  background-color: white;
  border-radius: 16px;
  padding: 24px;
  width: 90%;
  align-self: center;
`;

const ModalTitle = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 20px;
  color: #333333;
  margin-bottom: 16px;
  text-align: center;
`;

const ModalMessage = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
  color: #333333;
  margin-bottom: 24px;
  text-align: center;
`;

const ButtonsContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
`;

const Button = styled.TouchableOpacity<{ isDelete?: boolean }>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  background-color: ${(props: { isDelete?: boolean }) => props.isDelete ? '#FF3B30' : '#F0F0F0'};
  margin: 0 8px;
  align-items: center;
  justify-content: center;
`;

const ButtonText = styled.Text<{ isDelete?: boolean }>`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
  color: ${(props: { isDelete?: boolean }) => props.isDelete ? 'white' : '#333333'};
`;

interface DeleteItemModalProps {
  isVisible: boolean;
  onClose: () => void;
  onDelete: () => void;
  itemName: string;
  isLoading: boolean;
}

export default function DeleteItemModal({
  isVisible,
  onClose,
  onDelete,
  itemName,
  isLoading
}: DeleteItemModalProps) {
  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        if (isVisible) {
          onClose();
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      backdropOpacity={0.5}
      animationIn="fadeIn"
      animationOut="fadeOut"
      useNativeDriver={true}
      onModalHide={onClose}
    >
      <ModalContainer>
        <ModalTitle>Delete Item</ModalTitle>
        <ModalMessage>
          Are you sure you want to delete "{itemName}"? This action cannot be undone.
        </ModalMessage>
        <ButtonsContainer>
          <Button onPress={onClose} disabled={isLoading}>
            <ButtonText>Cancel</ButtonText>
          </Button>
          <Button isDelete onPress={onDelete} disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <ButtonText isDelete>Delete</ButtonText>
            )}
          </Button>
        </ButtonsContainer>
      </ModalContainer>
    </Modal>
  );
}
