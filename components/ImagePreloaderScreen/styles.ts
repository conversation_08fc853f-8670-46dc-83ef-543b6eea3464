import { Text, TouchableOpacity } from "react-native";
import styled, { DefaultTheme } from "styled-components/native";

export const Container = styled.View`
  flex: 1;
  background-color: #fff;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const LoadingContainer = styled.View`
  align-items: center;
  justify-content: center;
  max-width: 300px;
`;

export const Title = styled(Text)`
  font-family: CormorantGaramondSemiBold;
  font-size: 24px;
  line-height: 32px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  text-align: center;
  margin-bottom: 16px;
`;

export const Subtitle = styled(Text)`
  font-family: MuktaVaaniLight;
  font-size: 14px;
  line-height: 24px;
  color: #000;
  text-align: center;
  margin-bottom: 32px;
`;

export const ProgressContainer = styled.View`
  width: 100%;
  margin-bottom: 24px;
`;

export const ProgressBar = styled.View`
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
`;

export const ProgressFill = styled.View<{ percentage: number }>`
  width: ${({ percentage }: { percentage: number }) => percentage}%;
  height: 100%;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 4px;
`;

export const ProgressText = styled(Text)`
  font-family: MuktaVaaniLight;
  font-size: 14px;
  line-height: 20px;
  color: #000;
  text-align: center;
`;

export const ErrorContainer = styled.View`
  margin-top: 16px;
  padding: 12px;
  background-color: #FEF2F2;
  border-radius: 8px;
  border: 1px solid #FECACA;
`;

export const ErrorText = styled(Text)`
  font-family: MuktaVaaniLight;
  font-size: 12px;
  line-height: 16px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.red};
  text-align: center;
`;

export const RetryButton = styled(TouchableOpacity)`
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  padding: 8px 16px;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 20px;
`;

export const RetryButtonText = styled(Text)`
  font-family: MuktaVaaniSemiBold;
  font-size: 12px;
  color: #FFFFFF;
  text-align: center;
`;