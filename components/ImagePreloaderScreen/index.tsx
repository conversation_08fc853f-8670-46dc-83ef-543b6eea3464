import { FC } from 'react';
import { ActivityIndicator } from 'react-native';
import {
  <PERSON><PERSON>er,
  ErrorContainer,
  ErrorText,
  LoadingContainer,
  ProgressBar,
  ProgressContainer,
  ProgressFill,
  ProgressText,
  RetryButton,
  RetryButtonText,
  Subtitle,
  Title
} from './styles';
import { useTheme } from 'styled-components/native';

interface ImagePreloaderScreenProps {
  progress: {
    loaded: number;
    total: number;
    percentage: number;
    isComplete: boolean;
    errors: string[];
    failedUrls: string[];
  };
  isPreloading: boolean;
  onRetry?: () => void;
}

export const ImagePreloaderScreen: FC<ImagePreloaderScreenProps> = ({
  progress,
  isPreloading,
  onRetry,
}) => {
  const theme = useTheme();

  if (!isPreloading && progress.isComplete) {
    return null;
  }

  const hasFailedImages = progress.failedUrls.length > 0;

  return (
    <Container>
      <LoadingContainer>
        <Title>Preparing Your Style Quiz</Title>
        <Subtitle>
          Loading images to ensure a smooth experience...
        </Subtitle>

        <ProgressContainer>
          <ProgressBar>
            <ProgressFill percentage={progress.percentage} />
          </ProgressBar>
          <ProgressText>
            {progress.loaded} of {progress.total} images loaded ({progress.percentage}%)
          </ProgressText>
        </ProgressContainer>

        {hasFailedImages && (
          <ErrorContainer>
            <ErrorText>
              {progress.failedUrls.length} image{progress.failedUrls.length !== 1 ? 's' : ''} failed to load.
              {onRetry && ' You can retry or continue with available images.'}
            </ErrorText>
            {onRetry && (
              <RetryButton onPress={onRetry}>
                <RetryButtonText>Retry Failed Images</RetryButtonText>
              </RetryButton>
            )}
          </ErrorContainer>
        )}

        <ActivityIndicator size="large" color={theme.brand.green[500]} />
      </LoadingContainer>
    </Container>
  );
}; 