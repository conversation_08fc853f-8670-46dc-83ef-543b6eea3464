import styled from 'styled-components/native';

interface DropdownContainerProps {
  width?: string | number;
  height?: number;
}

export const DropdownContainer = styled.TouchableOpacity<DropdownContainerProps>`
  width: ${(props: DropdownContainerProps) => props.width || '60%'};
  height: ${(props: DropdownContainerProps) => props.height || 30}px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  background-color: transparent;
`;

export const SelectedText = styled.Text`
  font-family: 'Mukta<PERSON>aani';
  font-size: 16px;
  color: #333;
  text-align: right;
  flex: 1;
`;

export const DropdownItem = styled.TouchableOpacity`
  position: relative;
  min-height: 44px;
  padding: 10px 16px 10px 30px;
  background-color: #fff;
  border-bottom-width: 1px;
  border-bottom-color: #f0f0f0;
  justify-content: center;
`;

export const DropdownText = styled.Text`
  font-family: '<PERSON>kta<PERSON>aan<PERSON>';
  font-size: 16px;
  color: #333;
`;

export const CheckView = styled.View`
  position: absolute;
  left: 0;
  top: 0;
  align-items: center;
  justify-content: center;
  height: 44px;
  width: 30px;
`;

export const DropdownOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
`; 