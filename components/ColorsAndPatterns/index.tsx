import { packingList as packingListString } from '@/constants/strings';
import { Check, ChevronLeft } from 'lucide-react-native';
import { useMemo, useState } from 'react';
import { FlatList, Pressable, Text, TouchableOpacity, View } from 'react-native';
import { useTheme } from 'styled-components';
import { HeaderText } from '../AddItems/styles';
import {
  ColorPatternName,
  ColorsContainer,
  ColorsView,
  FlatListColorContainer,
  FlatListContainer,
  FlatListItem,
  FlatListName,
  FlatListSeparator,
  ImageContainer,
  ModalContainer,
  ModalContent,
  ModalHeader,
  ModalTextTitle,
  ModalTouchableOpacity,
  ViewMoreButton,
  ViewMoreText
} from './style';
interface selectedColor {
  _id: string,
  name: string,
  hexCode: string,
  type: string,
  createdAt: string
}

interface ColorsAndPatternsListProps {
  colorPatternData: any[]
  numColumns?: number
  lengthToDisplay?: number
  isColor?: boolean
  isPattern?: boolean
  showViewMoreButton?: boolean
  secondaryColor?: any[]
  setPrimarySelectedColor?: (item: selectedColor | null) => void
  setSecondarySelectedColor?: (item: selectedColor | null) => void
  primarySelectedColor?: selectedColor | null
  secondarySelectedColor?: selectedColor | null
  setPrintPattern?: (item: selectedColor | null) => void
  selectedPrint?: selectedColor | null
}

export default function ColorsAndPatternsList({
  colorPatternData,
  numColumns = 6,
  lengthToDisplay = 12,
  isColor = false,
  isPattern = false,
  showViewMoreButton = true,
  secondaryColor = [],
  setPrimarySelectedColor = () => { },
  setSecondarySelectedColor = () => { },
  primarySelectedColor = {} as selectedColor,
  secondarySelectedColor = {} as selectedColor,
  setPrintPattern = () => { },
  selectedPrint = {} as selectedColor
}: ColorsAndPatternsListProps) {

  const theme = useTheme()
  const [isModalVisible, setIsModalVisible] = useState(false);

  const renderItem = ({ item, index }: any) => {
    if (index < lengthToDisplay) {
      const isSelected = selectedPrint?._id === item._id || primarySelectedColor?._id === item._id || secondarySelectedColor?._id === item._id;
      const borderColor = isSelected ? theme.brand.green[500] : theme.brand.gray[500];
      const borderWidth = isSelected ? 3 : 1;
      return (
        <Pressable style={{ flex: 1 }}
          onPress={() => {
            if (!isColor) {
              return setPrintPattern(selectedPrint?._id === item._id ? null : item)
            }
            const isSameSelected = primarySelectedColor?._id === item._id;
            const isSameSecondary = secondarySelectedColor?._id === item._id;
            if (isSameSelected) {
              setPrimarySelectedColor(secondarySelectedColor);
              setSecondarySelectedColor(null);
              return
            }
            if (isSameSecondary) return setSecondarySelectedColor(null)
            if (primarySelectedColor) return setSecondarySelectedColor(item)
            return setPrimarySelectedColor(item)
          }}>
          <ColorsContainer>
            {isColor && <ColorsView style={{ backgroundColor: item.hexCode, borderColor, borderWidth }} />}
            {isPattern && <ImageContainer source={{ uri: item.imageURL }} style={{ borderColor, borderWidth }} />}
            <ColorPatternName numberOfLines={2}>{item.name}</ColorPatternName>
          </ColorsContainer>
        </Pressable>
      )
    }
    return null;
  }

  const sortedData = useMemo(() => {
    const combined = [...colorPatternData, ...secondaryColor];
    const selectedIds = [
      primarySelectedColor?._id,
      secondarySelectedColor?._id,
      selectedPrint?._id
    ]

    if (!primarySelectedColor?._id && !selectedPrint?._id) return combined;
    const selectedIndex = combined.filter(item => selectedIds.includes(item._id));
    if (selectedIndex.length === 0) return combined;
    const rest = combined.filter(item => !selectedIds.includes(item._id));
    return [...selectedIndex, ...rest];
  }, [colorPatternData, secondaryColor, primarySelectedColor, secondarySelectedColor, selectedPrint]);

  const removeDuplicates = (array: any[]) => {
    const newData: Array<any> = [];
    array.forEach(item => {
      if (!newData.some((t: any) => t.name === item.name)) {
        newData.push(item);
      }
    });
    return newData;
  }
  return (
    <>
      <FlatList
        keyExtractor={(item, index) => `${item._id}-${index}`}
        data={sortedData}
        numColumns={numColumns}
        renderItem={renderItem}
      />
      {showViewMoreButton && (
        <ViewMoreButton onPress={() => setIsModalVisible(true)}>
          <ViewMoreText>{packingListString.viewMore}</ViewMoreText>
        </ViewMoreButton>
      )}
      <ModalContainer isVisible={isModalVisible} onBackdropPress={() => setIsModalVisible(false)}>
        <ModalContent>
          <ModalHeader>
            <TouchableOpacity onPress={() => setIsModalVisible(false)}>
              <ChevronLeft size={24} color={theme.brand.green[500]} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => {
              setIsModalVisible(false);
              if (!isColor) return setPrintPattern(selectedPrint)
              if (isColor && primarySelectedColor) return setPrimarySelectedColor(primarySelectedColor)
              if (isColor && secondarySelectedColor) return setSecondarySelectedColor(secondarySelectedColor)
            }}>
              <HeaderText style={{ textAlign: 'center' }}>{packingListString.save}</HeaderText>
            </TouchableOpacity>
          </ModalHeader>
          <View style={{ paddingBottom: 20 }}>
            <ModalTextTitle>
              {isColor ? packingListString.colorList : packingListString.patternList}
            </ModalTextTitle>
          </View>
          <FlatList
            keyExtractor={(item, index) => `${item._id}-${index}`}
            data={removeDuplicates([...colorPatternData, ...secondaryColor])}
            showsVerticalScrollIndicator={false}
            renderItem={({ item }) => {
              return (
                <FlatListContainer>
                  <FlatListItem>
                    <ModalTouchableOpacity
                      isSelected={primarySelectedColor?._id === item._id || selectedPrint?._id === item._id || secondarySelectedColor?._id === item._id}
                      onPress={() => {
                        if (!isColor) return setPrintPattern(selectedPrint?._id === item._id ? null : item)
                        const isSecondary = secondarySelectedColor !== null;
                        const selected = isSecondary ? secondarySelectedColor : primarySelectedColor;
                        const setSelected = isSecondary ? setSecondarySelectedColor : setPrimarySelectedColor;
                        setSelected(selected?._id === item._id ? null : item);
                      }}
                    >
                      {primarySelectedColor?._id === item._id || selectedPrint?._id === item._id && <Check size={14} color={theme.brand.gray[100]} />}
                      {secondarySelectedColor?._id === item._id && <Check size={14} color={theme.brand.gray[100]} />}
                    </ModalTouchableOpacity>
                    <FlatListItem>
                      <FlatListColorContainer>
                        {isColor && <ColorsView style={{ backgroundColor: item.hexCode }} />}
                        {isPattern && <ImageContainer source={{ uri: item.imageURL }} />}
                      </FlatListColorContainer>
                    </FlatListItem>
                    <FlatListName>{item.name}</FlatListName>
                  </FlatListItem>
                  <FlatListSeparator />
                </FlatListContainer>
              )
            }}
          />
        </ModalContent>
      </ModalContainer>
    </>
  );
}
