import { css, DefaultTheme } from 'styled-components';
import styled from 'styled-components/native';
import Modal from 'react-native-modal';
import styledModal from 'styled-components';
const sharedBoxStyles = css`
  flex: 1;
  height: 38px;
  width: 50.333335876464844px;
  border: 1px solid ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[500]};
`;
export const ColorsContainer = styled.View`
flex-direction: column;
align-items: center;
margin-bottom: 8px;
margin-left: 4px;
margin-right: 4px;`;

export const ImageContainer = styled.Image`${sharedBoxStyles}`;

export const ColorsView = styled.View`${sharedBoxStyles}`;

export const ViewMoreButton = styled.TouchableOpacity`
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  padding: 10px;
  border-radius: 50px;
  justify-content: center;
  align-items: center;`;

export const ViewMoreText = styled.Text`
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[50]};
  font-size: 16px;
  font-family: 'MuktaVaaniMedium';`;

export const ModalContainer = styledModal(Modal)`
  margin: 0px;
  justify-content: flex-end;
  align-items: flex-end;
`;
export const ModalHeader = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: 20px;
  padding-right: 20px;
`;

export const ModalTextTitle = styled.Text`
  text-align: center;
  font-family: 'MuktaVaaniMedium';
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[900]};
  font-size: 18px;
`;

export const ModalTouchableOpacity = styled.TouchableOpacity`
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 50px;
  align-items: center;
  width: 20px;
  height: 20px;
  justify-content: center;
  background-color: ${({ isSelected, theme }: { isSelected: boolean, theme: DefaultTheme }) =>
    isSelected ? theme.brand.green[500] : theme.brand.gray[100]};
`;

export const FlatListContainer = styled.View`
  margin-bottom: 10px;
  padding-left: 20px;
  padding-right: 20px;
`;
export const FlatListItem = styled.View`
  flex-direction: row;
  align-items: center;
`;

export const FlatListColorContainer = styled.View`
  margin-bottom: 0px;
  padding: 0px 10px 0px 10px;
  width: 80px;
  height: 38px;
  flex-direction: row;
`;

export const FlatListName = styled.Text`
  font-family: 'MuktaVaaniLight';
  font-size: 16px;
`;
export const FlatListSeparator = styled.View`
  height: 1px;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[300]};
  margin: 10px 0px 10px 0px;
`;

export const ColorPatternName = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 12px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[700]};
  text-align: center;
`;

export const ModalContent = styled.SafeAreaView`
  flex: 1;
  border-radius: 20px;
  width: 70%;
  background: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[50]},
`;