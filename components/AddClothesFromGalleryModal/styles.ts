import { View, Text, TouchableOpacity } from 'react-native';
import styled , { DefaultTheme } from 'styled-components/native';
import Input from '../common/Input';
import { PADDING, BORDER_RADIUS, isTablet } from '@/constants/responsive';

export const AddClothsModalContainer = styled(View)`
  height: 80%;
  background-color: white;
  width: 100%;
  border-top-left-radius: ${BORDER_RADIUS.MODAL}px;
  border-top-right-radius: ${BORDER_RADIUS.MODAL}px;
  padding: ${PADDING.MODAL_CONTENT_HORIZONTAL}px;
  justify-content: space-between;
`;

export const AddClothsModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const HeaderText = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaani;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;

export const AddClothsModalTitle = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaaniSemiBold;
`;

export const AddClothsModalInput = styled(Input)`
  width: 100%;
  height: 56px;
  border-radius: 10px;
  border-width: 1px;
  border-color: #000;
`;

export const ClothItemContainer = styled(View)<{ width: number }>`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #EBEBEB;
  border-radius: 10px;
  margin: 5px;
  height: ${({ width }: { width: number }) => (width / 3) - 10}px;
  width: ${({ width }: { width: number }) => (width / 3) - 10}px;
`;

export const AddClothItemContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #EBEBEB;
  border-radius: 10px;
  margin: 5px;
  height: ${({ width }: { width: number }) => (width / 3) - 10}px;
  width: ${({ width }: { width: number }) => (width / 3) - 10}px;
`;

export const ClothItemImage = styled.Image`
  width: 50px;
  height: 50px;
  border-radius: 10px;
`;

export const ClothItemName = styled(Text)`
  font-size: 16px;
  font-family: MuktaVaani;
`;

export const ClothesContainer = styled(View)`
  flex-direction: row;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
`;

export const CheckBox = styled(View)`
  width: 20px;
  height: 20px;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  border-width: 1px;
  border-color: #000;
  position: absolute;
  right: 10px;
  bottom: 10px;
  background-color: #FFF;
`;

export const CategoryItem = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 10px;
  margin: 5px;
  padding: 8px 16px;
  background-color: ${({ isSelected, theme }: { isSelected: boolean, theme: DefaultTheme }) => isSelected ? theme.brand.green[500] : '#F5F5F5'};
`;

export const CategoryItemText = styled(Text)<{ isSelected: boolean }>`
  font-size: 16px;
  font-family: MuktaVaani;
  color: ${({ isSelected }: { isSelected: boolean }) => isSelected ? '#FFF' : '#000'};
`;

export const HeaderTitle = styled(Text)`
  font-size: 20px;
  font-family: MuktaVaaniSemiBold;
  color: #000;
  text-align: center;
`;

export const CategoryItemContainer = styled(View)`
  flex-direction: row;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  padding: 6px 12px;
  border-radius: 10px;
`;

export const CategoryItemTextButton = styled(Text)`
  font-size: 16px;
  font-family: MuktaVaani-Regular;
  color: #FFF;
`;

export const QuantityButton = styled(TouchableOpacity)`
  border-radius: 10px;
  padding: 6px 12px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
`;

export const ClothsImageContainer = styled(TouchableOpacity)<{ width: number }>`
  flex-direction: row;
  gap: 10px;
  justify-content: center;
  align-items: center;
  background-color: #0E7E61;
  border-radius: 10px;
  overflow: hidden;
  width: ${({ width }: { width: number }) => width}px;
  height: ${({ width }: { width: number }) => width}px;
`;
