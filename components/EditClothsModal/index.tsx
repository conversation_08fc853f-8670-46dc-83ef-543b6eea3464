import {
  ActivityIndicator,
  FlatList,
  KeyboardAvoidingView,
  LayoutChangeEvent,
  TouchableOpacity,
  View,
  Image,
  ScrollView,
} from 'react-native';
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalTitle,
  HeaderText,
  AddClothsModalInput,
  ClothItemContainer,
  ClothItemName,
  ClothesContainer,
  AddClothItemContainer,
  CheckBox,
  CategoryItem,
  CategoryItemText,
  HeaderTitle,
  CategoryItemContainer,
  CategoryItemTextButton,
  QuantityButton,
  ClothsImageContainer,
  CategoryListContainer,
  CategoryListItem,
  CategoryListItemText,
  CategoryListHeader,
  CategoryListHeaderText,
  ClothItemImage,
  ClothsImageContainerFull,
  ClothItemImageFull,
  TextValue,
} from './styles';
import Button from '@/components/common/Button';
import Modal from 'react-native-modal';
import { XIcon, PlusIcon, CheckIcon, MinusIcon } from 'lucide-react-native';
import Input from '../common/Input';
import { useState, useEffect } from 'react';
import { CLOTHES, CLOTHES_CATEGORIES } from '@/constants/Clothes';
import { SubtitleText, TitleSection } from '../common/Text';
import placeholderImage from '@/assets/images/placeholder-item.png';
import * as ImagePicker from 'expo-image-picker';
import { UploadImage, uploadImageToS3, updateItem } from '@/methods/cloths';

import { Box } from '@/components/ui/box';

interface AddClothsCategoryModalProps {
  isVisible: boolean;
  onClose: () => void;
  item: any;
  selectedItem: any;
  packingList: any[];
  saveEditCloths: (
    note: string,
    quantity: number,
    parentId: string,
    itemId: string,
    category: string,
    color: string,
    brand: string,
  ) => void;
  removeItem: (selectedItem: any, item: any) => void;
}

export default function EditClothsModal({
  isVisible,
  onClose,
  item,
  selectedItem,
  packingList = [],
  saveEditCloths,
  removeItem,
}: AddClothsCategoryModalProps) {
  const [width, setWidth] = useState(0);
  const [note, setNote] = useState('');
  const [quantity, setQuantity] = useState(item?.quantity || 1);
  const [showAlertDialog, setShowAlertDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState();
  const [color, setColor] = useState(item?.color || '');
  const [brand, setBrand] = useState(item?.brand || '');
  const [itemName, setItemName] = useState(item?.name || '');
  const [image, setImage] = useState('');
  const [displayImage, setDisplayImage] = useState('');
  const {
    mutate: uploadImageMutation,
    isPending: isUploadingImage,
    isSuccess: isImageUploaded,
    data: imageData,
  } = UploadImage();
  const {
    mutate: uploadImageToS3Mutation,
    isPending: isUploadingImageToS3,
    isSuccess: isImageUploadedToS3,
    data: imageDataToS3,
  } = uploadImageToS3();
  const {
    mutate: updateItemMutation,
    isPending: isUpdatingItem,
    isSuccess: isItemUpdated,
    data: itemData,
  } = updateItem();
  const handleClose = () => {
    setShowAlertDialog(false);
  };

  const handleOpen = () => {
    setShowAlertDialog(true);
    console.log('showAlertDialog', showAlertDialog);
  };

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  const pickImage = async () => {
    console.log('pickImage');
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        alert('Permission to access media library is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
        base64: true,
      });

      if (!result.canceled && result.assets?.[0]) {
        const selectedImage = result.assets[0];
        setImage(selectedImage.base64 || '');
        setDisplayImage(selectedImage.uri);

        uploadImageMutation({
          fileName: `cloth-${Date.now()}`,
          fileType: 'image/jpeg',
          folderPath: 'items',
          imageUrl: image,
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      alert('Failed to pick image');
    }
  };

  useEffect(() => {
    if (imageDataToS3) {
      console.log('imageDataToS3', imageData);

      updateItemMutation({
        itemId: itemData?.itemId,
        imageUrl: imageData?.fileURL,
        color: color,
        brand: brand,
        name: itemName,
        category: selectedCategory,
        note: note,
      });

      saveEditCloths(
        note,
        quantity,
        selectedItem._id,
        item._id,
        selectedCategory,
        color,
        brand,
        imageData?.fileURL,
        itemName,
      );
      onClose();
    }
  }, [isImageUploadedToS3]);

  // useEffect(() => {
  //   if (isImageUploaded) {
  //     uploadImageToS3Mutation({
  //       imageUrl: image,
  //       preSignedUrl: imageData?.preSignedURL,
  //     });
  //   }
  // }, [isImageUploaded]);

  useEffect(() => {
    setNote(item?.note || '');
    setQuantity(item?.quantity || 1);
    setSelectedCategory(null);
    setColor(item?.color || '');
    setBrand(item?.brand || '');
    setDisplayImage(item?.imageUrl || '');
    setItemName(item?.name || '');
    setImage('');
  }, [isVisible]);

  const _saveEditCloths = () => {
    if (image) {
      uploadImageToS3Mutation({
        imageUrl: image,
        preSignedUrl: imageData?.preSignedURL,
      });

      return;
    }
    //check if image is uploaded
    saveEditCloths(
      note,
      quantity,
      selectedItem._id,
      item._id,
      selectedCategory,
      color,
      brand,
      item?.imageUrl,
      itemName,
    );
    onClose();
  };

  return (
    <Modal
      key={item?.id}
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <KeyboardAvoidingView
        behavior="padding"
        style={{ flex: 1, justifyContent: 'flex-end' }}
      >
        <AddClothsModalContainer>
          <ScrollView
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <AddClothsModalHeader>
              <TouchableOpacity style={{ flex: 1 }} onPress={onClose}>
                <HeaderText>Cancel</HeaderText>
              </TouchableOpacity>
              <View style={{ flex: 3, alignItems: 'center' }}>
                <HeaderTitle>Edit</HeaderTitle>
              </View>

              <View style={{ flex: 1, alignItems: 'flex-end' }}>
                <TouchableOpacity
                  onPress={() => {
                    _saveEditCloths();
                  }}
                >
                  <HeaderText>Save</HeaderText>
                </TouchableOpacity>
              </View>
            </AddClothsModalHeader>
            <View style={{ marginTop: 20 }}>
              <TouchableOpacity onPress={pickImage}>
                <ClothsImageContainerFull width={width}>
                  <ClothItemImageFull
                    defaultSource={placeholderImage}
                    resizeMode="contain"
                    source={
                      displayImage
                        ? { uri: displayImage }
                        : item?.imageUrl
                        ? { uri: item?.imageUrl }
                        : placeholderImage
                    }
                  />
                  {isUploadingImage && (
                    <View
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: 'rgba(0,0,0,0.3)',
                      }}
                    >
                      <ActivityIndicator size="large" color="#FFF" />
                    </View>
                  )}
                </ClothsImageContainerFull>
              </TouchableOpacity>
            </View>
            <View style={{ marginTop: 20 }}>
              <TitleSection string={`Item Name`} />
              <View style={{ marginTop: 10 }}>
                <AddClothsModalInput
                  placeholder="Enter item name"
                  value={itemName}
                  onChangeText={setItemName}
                />
              </View>
            </View>
            <View style={{ marginTop: 20 }}>
              <TitleSection string={`Color`} />
              <View style={{ marginTop: 10 }}>
                <AddClothsModalInput
                  placeholder="Enter color"
                  value={color}
                  onChangeText={setColor}
                />
              </View>
            </View>
            <View style={{ marginTop: 20 }}>
              <TitleSection string={`Brand`} />
              <View style={{ marginTop: 10 }}>
                <AddClothsModalInput
                  placeholder="Enter brand"
                  value={brand}
                  onChangeText={setBrand}
                />
              </View>
            </View>
            <View style={{ marginTop: 20 }}>
              <TitleSection string={` Category`} />
              <View
                style={{
                  marginTop: 10,
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 10,
                }}
              >
                <CategoryItemContainer onPress={handleOpen}>
                  <CategoryItemTextButton>
                    {`${selectedCategory?.name || selectedItem?.name}`}
                  </CategoryItemTextButton>
                </CategoryItemContainer>
              </View>
            </View>
            <View style={{ marginTop: 20 }}>
              <TitleSection string={`Notes`} />
              <View style={{ marginTop: 10 }}>
                <AddClothsModalInput
                  placeholder="Item notes"
                  value={note}
                  onChangeText={setNote}
                />
              </View>
            </View>
            <View
              style={{
                justifyContent: 'space-between',
                marginTop: 20,
                flexDirection: 'row',
                alignItems: 'center',
                gap: 10,
              }}
            >
              <TitleSection string={`Quantity`} />
              <View
                style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}
              >
                <QuantityButton
                  style={{ backgroundColor: '#B4D7CE' }}
                  onPress={() => setQuantity(quantity - 1)}
                >
                  <MinusIcon color="#0E7E61" />
                </QuantityButton>
                <SubtitleText string={`${quantity}`} />
                <QuantityButton
                  style={{ backgroundColor: '#0E7E61' }}
                  onPress={() => setQuantity(quantity + 1)}
                >
                  <PlusIcon color="#FFF" />
                </QuantityButton>
              </View>
            </View>

            <ClothesContainer
              onLayout={handleLayout}
              style={{ marginTop: 20 }}
            ></ClothesContainer>
            <Button
              title="Remove"
              isLined
              buttonColor="red"
              onPress={() => {
                removeItem(selectedItem._id, item._id);
              }}
            />
          </ScrollView>
          <View style={{ marginTop: 20, gap: 10, marginBottom: 20 }}></View>
        </AddClothsModalContainer>
      </KeyboardAvoidingView>
      <Modal
        isVisible={showAlertDialog}
        onBackdropPress={handleClose}
        onBackButtonPress={handleClose}
      >
        <CategoryListContainer>
          <View
            style={{ justifyContent: 'flex-end', margin: 0, marginBottom: 20 }}
          >
            <CategoryListHeader>
              <CategoryListHeaderText>
                Select Category to move item
              </CategoryListHeaderText>
            </CategoryListHeader>
          </View>
          <FlatList
            data={packingList}
            horizontal={true}
            keyExtractor={(item) => item}
            contentContainerStyle={{ gap: 10 }}
            renderItem={({ item }) => (
              <CategoryListItem
                key={item}
                onPress={() => {
                  setSelectedCategory(item);
                  handleClose();
                }}
              >
                <CategoryListItemText>{item.name}</CategoryListItemText>
              </CategoryListItem>
            )}
          />
        </CategoryListContainer>
      </Modal>
    </Modal>
  );
}
