import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import Modal from 'react-native-modal';
import styled from 'styled-components/native';

interface CategoryFilterModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectCategory: (categoryId: string, categoryName: string) => void;
  selectedCategory: string;
  gender?: string; // Optional gender prop
}

// Define the main categories as specified with simplified names
export const MAIN_CATEGORIES = [
  { id: 'all', name: 'All' },
  { id: 'tops', name: 'Tops' },
  { id: 'bottoms', name: 'Bottoms' },
  { id: 'dresses', name: 'Dress<PERSON>' },
  { id: 'matching-sets', name: 'Matching Sets' },
  { id: 'sweaters', name: 'Sweaters' },
  { id: 'outerwear', name: 'Outerwear' },
  { id: 'swimwear', name: 'Swimwear' },
  { id: 'activewear', name: 'Activewear' },
  { id: 'loungewear', name: 'Loungewear' },
  { id: 'jumpsuits', name: 'Jumpsuits' },
  { id: 'shoes', name: 'Shoes' },
  { id: 'sandals', name: 'Sand<PERSON>' },
  { id: 'jewellery', name: 'Jewellery' },
  { id: 'bags', name: 'Bags' },
  { id: 'accessories', name: 'Accessories' },
  { id: 'toiletries', name: 'Toiletries' },
  { id: 'makeup', name: 'Makeup' },
  { id: 'tech', name: 'Tech' },
  { id: 'others', name: 'Others' },
];

export default function CategoryFilterModal({
  isVisible,
  onClose,
  onSelectCategory,
  selectedCategory,
}: CategoryFilterModalProps) {
  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{ margin: 0, justifyContent: 'flex-end' }}
      swipeDirection="down"
      onSwipeComplete={onClose}
    >
      <ModalContainer>
        <BarIndicatorContainer>
          <BarIndicator />
        </BarIndicatorContainer>
        <ModalTitle>Filter Clothes</ModalTitle>
        <ScrollView
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={{ paddingBottom: 24 }}
          style={{
            maxHeight: 400,
          }} /* Set a specific height for the ScrollView */
        >
          <ButtonContainer>
            {MAIN_CATEGORIES.map((category) => (
              <FilterButton
                key={category.id}
                active={selectedCategory === category.name}
                onPress={() => {
                  onSelectCategory(category.id, category.name);
                  onClose();
                }}
              >
                <FilterButtonText active={selectedCategory === category.name}>
                  {category.name}
                </FilterButtonText>
              </FilterButton>
            ))}
          </ButtonContainer>
        </ScrollView>
      </ModalContainer>
    </Modal>
  );
}

// Styled components
const ModalContainer = styled(View)`
  background-color: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 0 16px 24px 16px;
  width: 100%;
  max-height: 500px; /* Increased height to show more categories */
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.15);
`;

const BarIndicatorContainer = styled(View)`
  width: 100%;
  height: 24px;
  align-items: center;
  justify-content: center;
`;

const BarIndicator = styled(View)`
  width: 40px;
  height: 4px;
  border-radius: 2px;
  background-color: rgba(51, 51, 51, 0.2); /* Gray color instead of green */
`;

const ModalTitle = styled(Text)`
  font-family: 'CormorantGaramondSemiBold';
  font-size: 24px;
  color: #1c1c1c;
  margin-bottom: 16px;
  padding: 0 8px;
  text-align: left; /* Left-aligned like the Outfits filter */
`;

const ButtonContainer = styled(View)`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px; /* Reduced gap between buttons */
  padding: 0 8px;
`;

interface ActiveProps {
  active: boolean;
}

const FilterButton = styled(TouchableOpacity)<ActiveProps>`
  background-color: ${(props: ActiveProps) =>
    props.active ? '#0E7E61' : 'transparent'};
  padding: 8px 16px;
  border-radius: 8px; /* Match the Outfits filter border radius */
  min-width: 45px;
  height: auto; /* Allow height to adjust based on content */
  min-height: 36px; /* Minimum height to match the Outfits filter */
  align-items: center;
  justify-content: center;
  border-width: ${(props: ActiveProps) => (props.active ? '0' : '1px')};
  border-color: ${(props: ActiveProps) =>
    props.active
      ? 'transparent'
      : '#0E7E61'}; /* Green border only for inactive */
  margin: 2px; /* Reduced margin all around */
  flex-direction: row; /* Ensures better text alignment */
`;

const FilterButtonText = styled(Text)<ActiveProps>`
  color: ${(props: ActiveProps) => (props.active ? '#FFFFFF' : '#0E7E61')};
  font-family: 'MuktaVaaniSemiBold';
  font-size: 14px;
  line-height: 20px; /* Match the Outfits filter line height */
  text-align: center;
  margin-top: -1px; /* Fine-tuning for pixel-perfect centering */
`;
