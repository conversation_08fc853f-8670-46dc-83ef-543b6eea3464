import { DefaultTheme } from 'styled-components';
import styled from 'styled-components/native';

export const DeleteConfirmationContainer = styled.View`
  padding: 32px 16px;
`;

export const DeleteHeading = styled.Text`
  font-size: 20px;
  font-family: 'MuktaVaaniSemiBold';
  text-align: center;
  margin-bottom: 8px;
`;

export const DeleteSubheading = styled.Text`
  font-size: 16px;
  text-align: center;
  margin-bottom: 32px;
`;

export const FeatureItem = styled.View`
  flex: 1;
  padding: 16px 10px;
  background-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[100]};
  border-radius: 8px;
  align-items: center;
`;

export const FeatureIcon = styled.View`
  width: 24px;
  height: 24px;
  margin-bottom: 16px;
`;

export const FeatureText = styled.Text`
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  font-size: 14px;
  font-family: 'MuktaVaaniMedium';
  text-align: center;
`;
