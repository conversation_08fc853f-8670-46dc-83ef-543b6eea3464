import ClockSvg from '@/assets/svg/clock-green.svg';
import ListSvg from '@/assets/svg/list-green.svg';
import ShirtSvg from '@/assets/svg/shirt-green.svg';
import SpeechBubbleSvg from '@/assets/svg/speech-bubble-green.svg';
import { GridContainer } from '@/components/GridContainer';
import { deleteVerification } from '@/methods/users';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import Button from '../common/Button';
import {
  DeleteConfirmationContainer,
  DeleteHeading,
  DeleteSubheading,
  FeatureIcon,
  FeatureItem,
  FeatureText,
} from './styles';

const features = [
  { icon: <ShirtSvg />, text: 'Items in your closet' },
  { icon: <ClockSvg />, text: 'Scheduled outfits' },
  { icon: <SpeechBubbleSvg />, text: 'Suggested outfits' },
  { icon: <ListSvg />, text: 'Packing lists' },
];

interface DeleteConfirmationProps {
  onCancel?: () => void;
}

const DeleteConfirmation = ({ onCancel }: DeleteConfirmationProps) => {
  const deleteVerificationMutation = deleteVerification();

  const handleSendVerification = useCallback(() => {
    deleteVerificationMutation.mutate();
  }, []);

  useEffect(() => {
    if (deleteVerificationMutation.isSuccess) {
      router.push('/delete-verification');
    } else if (deleteVerificationMutation.isError) {
      alert('Something went wrong. Please try again.');
    }
  }, [deleteVerificationMutation.data]);

  return (
    <DeleteConfirmationContainer>
      <DeleteHeading>Delete your account</DeleteHeading>
      <DeleteSubheading>You will lose the following:</DeleteSubheading>
      <GridContainer
        itemsPerRow={2}
        gap={16}
        containerStyle={{ marginBottom: 32 }}
      >
        {features.map((feature, index) => (
          <FeatureItem key={index}>
            <FeatureIcon>{feature.icon}</FeatureIcon>
            <FeatureText>{feature.text}</FeatureText>
          </FeatureItem>
        ))}
      </GridContainer>
      <Button
        buttonColor="red"
        title="Delete my account"
        style={{ marginBottom: 16 }}
        onPress={handleSendVerification}
        isDisabled={deleteVerificationMutation.isPending}
        isLoading={deleteVerificationMutation.isPending}
      />
      <Button
        buttonColor="red"
        title="Cancel"
        isLined
        onPress={onCancel}
        isDisabled={deleteVerificationMutation.isPending}
      />
    </DeleteConfirmationContainer>
  );
};

export default DeleteConfirmation;
