import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import Modal from 'react-native-modal';
import styled from 'styled-components/native';
import { MAIN_CATEGORIES } from '@/components/CategoryFilterModal';
import Input from '@/components/common/Input';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';

interface AddNewItemModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddItem: (item: any) => void;
}

export default function AddNewItemModal({
  isVisible,
  onClose,
  onAddItem,
}: AddNewItemModalProps) {
  const [itemName, setItemName] = useState('');
  const [itemBrand, setItemBrand] = useState('');
  const [itemColor, setItemColor] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [displayCategories, setDisplayCategories] = useState<any[]>([]);

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('AddNewItemModal - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log('AddNewItemModal - Using gender from getCurrentUserGender:', gender);
        }

        if (!gender) {
          console.warn('AddNewItemModal - No gender available, using default categories');
          setDisplayCategories(MAIN_CATEGORIES.filter(cat => cat.id !== 'all'));
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        if (backendCategories && backendCategories.length > 0) {
          // Format categories for display (exclude 'All' category for item creation)
          const formattedCategories = backendCategories.map(category => ({
            id: category.id,
            name: category.name
          }));

          console.log(`AddNewItemModal - Loaded ${formattedCategories.length} categories from backend for gender: ${gender}`);
          setDisplayCategories(formattedCategories);
        } else {
          console.warn('AddNewItemModal - No categories returned from backend, using default categories');
          // Fallback to MAIN_CATEGORIES if no categories returned (exclude 'All')
          setDisplayCategories(MAIN_CATEGORIES.filter(cat => cat.id !== 'all'));
        }
      } catch (error) {
        console.error('Error loading categories from backend:', error);
        // Fallback to MAIN_CATEGORIES if there's an error (exclude 'All')
        setDisplayCategories(MAIN_CATEGORIES.filter(cat => cat.id !== 'all'));
      }
    };

    loadGenderCategories();
  }, [userProfile]);

  const resetForm = () => {
    setItemName('');
    setItemBrand('');
    setItemColor('');
    setSelectedCategory('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleAddItem = () => {
    // Validate form
    if (!itemName.trim()) {
      Alert.alert('Error', 'Please enter an item name');
      return;
    }

    if (!selectedCategory) {
      Alert.alert('Error', 'Please select a category');
      return;
    }

    // Create new item object
    const newItem = {
      _id: `new-item-${Date.now()}`,
      name: itemName.trim(),
      brand: itemBrand.trim(),
      color: itemColor.trim(),
      category: { name: selectedCategory },
      // Use a placeholder image
      imageUrl: '',
    };

    // Add the item
    onAddItem(newItem);

    // Reset form and close modal
    resetForm();
    onClose();
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={handleClose}
      style={{ margin: 0, justifyContent: 'flex-end' }}
      backdropOpacity={0.5}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      useNativeDriver={true}
    >
      <ModalContainer>
        <BarIndicatorContainer>
          <BarIndicator />
        </BarIndicatorContainer>

        <ModalHeader>
          <ModalTitle>Add New Item</ModalTitle>
          <CloseButton onPress={handleClose}>
            <CloseButtonText>Close</CloseButtonText>
          </CloseButton>
        </ModalHeader>

        <ScrollView
          style={{ maxHeight: 400 }}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <FormContainer>
            <FormLabel>Item Name*</FormLabel>
            <FormInput
              value={itemName}
              onChangeText={setItemName}
              placeholder="Enter item name"
            />

            <FormLabel>Brand</FormLabel>
            <FormInput
              value={itemBrand}
              onChangeText={setItemBrand}
              placeholder="Enter brand name"
            />

            <FormLabel>Color</FormLabel>
            <FormInput
              value={itemColor}
              onChangeText={setItemColor}
              placeholder="Enter color"
            />

            <FormLabel>Category*</FormLabel>
            <CategoriesContainer>
              {displayCategories.map((category) => (
                <CategoryButton
                  key={category.id}
                  active={selectedCategory === category.name}
                  onPress={() => setSelectedCategory(category.name)}
                >
                  <CategoryButtonText
                    active={selectedCategory === category.name}
                  >
                    {category.name}
                  </CategoryButtonText>
                </CategoryButton>
              ))}
            </CategoriesContainer>

            <AddButton onPress={handleAddItem}>
              <AddButtonText>Add Item</AddButtonText>
            </AddButton>
          </FormContainer>
        </ScrollView>
      </ModalContainer>
    </Modal>
  );
}

// Styled components
const ModalContainer = styled(View)`
  background-color: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 0 16px 24px 16px;
  width: 100%;
  max-height: 80%;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.15);
`;

const BarIndicatorContainer = styled(View)`
  width: 100%;
  height: 24px;
  align-items: center;
  justify-content: center;
`;

const BarIndicator = styled(View)`
  width: 40px;
  height: 4px;
  border-radius: 2px;
  background-color: rgba(51, 51, 51, 0.2);
`;

const ModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom-width: 1px;
  border-bottom-color: #eeeeee;
`;

const ModalTitle = styled(Text)`
  font-family: 'CormorantGaramondSemiBold';
  font-size: 24px;
  color: #1c1c1c;
  flex: 1;
`;

const CloseButton = styled(TouchableOpacity)`
  padding: 8px;
`;

const CloseButtonText = styled(Text)`
  font-family: 'MuktaVaani';
  font-size: 16px;
  color: #0e7e61;
`;

const FormContainer = styled(View)`
  padding: 8px;
`;

const FormLabel = styled(Text)`
  font-family: 'MuktaVaani';
  font-size: 16px;
  color: #333333;
  margin-bottom: 8px;
  margin-top: 16px;
`;

const FormInput = styled(Input)`
  width: 100%;
  height: 40px;
  border-radius: 8px;
  border-width: 1px;
  border-color: #cccccc;
  padding: 0 12px;
  font-family: 'MuktaVaani';
  font-size: 14px;
`;

const CategoriesContainer = styled(View)`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 24px;
`;

interface ActiveProps {
  active: boolean;
}

const CategoryButton = styled(TouchableOpacity)<ActiveProps>`
  background-color: ${(props: ActiveProps) => (props.active ? '#0E7E61' : 'transparent')};
  padding: 8px 12px;
  border-radius: 8px;
  border-width: ${(props: ActiveProps) => (props.active ? '0' : '1px')};
  border-color: ${(props: ActiveProps) => (props.active ? 'transparent' : '#0E7E61')};
`;

const CategoryButtonText = styled(Text)<ActiveProps>`
  color: ${(props: ActiveProps) => (props.active ? '#FFFFFF' : '#0E7E61')};
  font-family: 'MuktaVaaniSemiBold';
  font-size: 14px;
`;

const AddButton = styled(TouchableOpacity)`
  background-color: #0e7e61;
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
`;

const AddButtonText = styled(Text)`
  color: #ffffff;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
`;
