import styled, { DefaultTheme } from 'styled-components/native';

export const RewardsHeader = styled.View`
  flex-direction: row;
  width: 100%;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 16px;
`;

export const RewardsHeading = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
  line-height: 24px;
`;

export const RewardsSeeAll = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  gap: 8px;
`;

export const RewardsSeeAllText = styled.Text`
  font-family: 'Mukta<PERSON>aani';
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  line-height: 24px;
`;

export const PointsText = styled.Text`
  font-family: 'MuktaVaani';
  margin-bottom: 16px;
`;
