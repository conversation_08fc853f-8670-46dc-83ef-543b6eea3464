import RightChevron from '@/assets/svg/right-chevron.svg';
import { getUserProfile } from '@/methods/users';
import { router, useFocusEffect } from 'expo-router';
import { useCallback, useRef } from 'react';
import { View } from 'react-native';
import { RewardsGrid } from '../RewardsGrid';
import {
  PointsText,
  RewardsHeader,
  RewardsHeading,
  RewardsSeeAll,
  RewardsSeeAllText,
} from './styles';

export const RewardsSection = () => {
  const isNavigating = useRef(false);
  const { data: userProfile, isLoading } = getUserProfile();

  useFocusEffect(useCallback(() => {
    isNavigating.current = false;
  }, []));

  const handlePress = useCallback(() => {
    if (isNavigating.current) return;
    isNavigating.current = true;
    router.push('/rewards');
  }, []);

  const { points } = userProfile?.data?.profile || {};

  return (
    <View>
      <RewardsHeader>
        <RewardsHeading>Rewards</RewardsHeading>
        <RewardsSeeAll onPress={handlePress}>
          <RewardsSeeAllText>See all</RewardsSeeAllText>
          <RightChevron />
        </RewardsSeeAll>
      </RewardsHeader>
      <PointsText>Points Earned: {isLoading ? '...' : points}</PointsText>
      <RewardsGrid itemsToShow={2} />
    </View>
  );
};
