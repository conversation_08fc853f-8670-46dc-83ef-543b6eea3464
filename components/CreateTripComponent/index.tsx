import DatePickerModal from '@/components/DatePickerModal';
import LocationAutocomplete from '@/components/LocationAutocomplete';
import TripCard from '@/components/TripCard';
import { Field, FieldGroup } from '@/components/common/Field';
import { createNewTrip } from '@/methods/trips';
import { TripDetails } from '@/types/trip';
import {
  cleanTripDetails,
  formatDate,
  getStartDateText,
} from '@/utils/tripHelpers';
import { router } from 'expo-router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Alert, Text, TouchableOpacity, View } from 'react-native';
import { useTheme } from 'styled-components/native';
import {
  CreateTripContainer,
  CreateTripHeader,
  CreateTripHeading,
  CreateTripTouchable,
  CreateTripTouchableText,
  DateTouchable,
  TripNameTextInput,
} from './styles';

const defaultTripDetails: TripDetails = {
  name: '',
  destinations: [
    {
      id: Date.now().toString(), // Unique ID
      name: '',
      longitude: 0,
      latitude: 0,
      startDate: undefined,
      endDate: undefined,
    },
  ],
};

export default function CreateTripComponent() {
  const [tripDetails, setTripDetails] =
    useState<TripDetails>(defaultTripDetails);

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDestinationIndex, setSelectedDestinationIndex] =
    useState<number>(0);

  const tripNameInputRef = useRef<any>(null);
  const locationAutocompleteRefs = useRef<(any | null)[]>([]);

  const {
    mutate: createTrip,
    data: createTripData,
  } = createNewTrip();

  const theme = useTheme();

  const handleClose = useCallback(() => {
    setTripDetails(defaultTripDetails);
    router.back();
  }, []);

  const validateTripDetails = useCallback(
    (details: TripDetails): boolean => {
      if (!details.name.trim()) {
        Alert.alert('Error', 'Please enter a trip name');
        return false;
      }

      // Remove last destination if it's empty and proceed with trip creation
      if (details.destinations.length > 1) {
        const lastDestination =
          details.destinations[details.destinations.length - 1];
        if (!lastDestination.name.trim()) {
          const updatedDetails = {
            ...details,
            destinations: details.destinations.slice(0, -1),
          };
          setTripDetails(updatedDetails);
          createTrip(cleanTripDetails(updatedDetails));
          return false;
        }
      }

      for (let i = 0; i < details.destinations.length; i++) {
        const destination = details.destinations[i];
        if (!destination.name.trim()) {
          Alert.alert('Error', `Please enter a name for destination ${i + 1}`);
          return false;
        }
        if (!destination.startDate) {
          Alert.alert(
            'Error',
            `Please select a start date for ${destination.name}`,
          );
          return false;
        }
      }

      return true;
    },
    [createTrip],
  );

  useEffect(() => {
    if (createTripData?.success) {
      router.push(`/trip-overview/${createTripData?.data?.eventId}`);
    }
  }, [createTripData]);

  const handleCreateTrip = useCallback(() => {
    if (validateTripDetails(tripDetails)) {
      const cleanedTripDetails = cleanTripDetails(tripDetails);
      createTrip(cleanedTripDetails);
    }
  }, [tripDetails, createTrip, validateTripDetails]);

  const handleDestinationSelect = useCallback(
    (
      destination: {
        name: string;
        latitude: number;
        longitude: number;
        countryCode?: string;
      },
      index: number,
    ) => {
      setTripDetails((prev) => ({
        ...prev,
        destinations: prev.destinations.map((loc, i) =>
          i === index
            ? {
              ...loc,
              name: destination.name,
              latitude: destination.latitude,
              longitude: destination.longitude,
              countryCode: destination.countryCode,
            }
            : loc,
        ),
      }));
    },
    [],
  );

  const handleDateChange = useCallback(
    (startDate?: Date | undefined, endDate?: Date | undefined) => {
      if (!endDate) return; // Don't update if end date is not selected

      setTripDetails((prev) => {
        const updatedDestinations = [...prev.destinations];
        // Update current destination's start and end dates
        startDate && (updatedDestinations[selectedDestinationIndex].startDate = startDate);
        endDate && (updatedDestinations[selectedDestinationIndex].endDate = endDate);

        // If not first destination, sync with previous destination's end date
        if (selectedDestinationIndex > 0) {
          startDate && (updatedDestinations[selectedDestinationIndex - 1].endDate = startDate);
        }

        // If there's a next destination, sync its start date
        if (selectedDestinationIndex < updatedDestinations.length - 1) {
          endDate && (updatedDestinations[selectedDestinationIndex + 1].startDate = endDate);
        }

        return {
          ...prev,
          destinations: updatedDestinations,
        };
      });
      setShowDatePicker(false);
    },
    [selectedDestinationIndex],
  );

  const openDatePicker = useCallback((index: number) => {
    setSelectedDestinationIndex(index);
    setShowDatePicker(true);
  }, []);

  const handleAddDestination = useCallback(() => {
    const lastDestination =
      tripDetails.destinations[tripDetails.destinations.length - 1];
    if (!lastDestination.endDate) {
      Alert.alert(
        'Error',
        'Please set an end date for the last destination before adding a new one',
      );
      return;
    }
    setTripDetails((prev) => ({
      ...prev,
      destinations: [
        ...prev.destinations,
        {
          id: Date.now().toString(),
          name: '',
          longitude: 0,
          latitude: 0,
          startDate: lastDestination.endDate || new Date(),
        },
      ],
    }));
  }, [tripDetails.destinations]);

  const handleDestinationDelete = useCallback(
    (index: number) => {
      if (tripDetails.destinations.length === 1) {
        Alert.alert('Error', 'Cannot delete the only destination');
        return;
      }

      setTripDetails((prev) => {
        const updatedDestinations = [...prev.destinations];
        updatedDestinations.splice(index, 1);
        return {
          ...prev,
          destinations: updatedDestinations,
        };
      });
    },
    [tripDetails.destinations],
  );

  return (
    <>
      <View style={{ gap: 16, paddingBottom: 16 }}>
        <CreateTripHeader>
          <CreateTripTouchable activeOpacity={0.8} onPress={handleClose}>
            <CreateTripTouchableText>Cancel</CreateTripTouchableText>
          </CreateTripTouchable>
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
              pointerEvents: 'none',
            }}
          >
            <CreateTripHeading>Create a Trip</CreateTripHeading>
          </View>
          <CreateTripTouchable activeOpacity={0.8} onPress={handleCreateTrip}>
            <CreateTripTouchableText>Create</CreateTripTouchableText>
          </CreateTripTouchable>
        </CreateTripHeader>
        <View style={{ paddingHorizontal: 16 }}>
          <TripCard
            showFooter={false}
            trip={{
              _id: '',
              name: tripDetails.name || 'New Trip',
              destinations: tripDetails.destinations,
              cardColor: theme?.brand.magenta,
            }}
            notClickable
          />
        </View>
      </View>
      <CreateTripContainer
        horizontal={false}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View style={{ paddingBottom: 32 }}>
          <FieldGroup>
            <Field label="Trip Name" onPress={() => tripNameInputRef.current?.focus()}>
              <TripNameTextInput
                ref={tripNameInputRef}
                value={tripDetails.name}
                onChangeText={(text: string) =>
                  setTripDetails((prev) => ({ ...prev, name: text }))
                }
                placeholder="Enter trip name"
                textAlign="right"
                numberOfLines={1}
              />
            </Field>
          </FieldGroup>
          {tripDetails.destinations.map((loc, index) => {
            return (
              <FieldGroup
                key={loc.id}
                containerStyle={{
                  marginTop: loc.id === tripDetails.destinations[0].id ? 0 : 4,
                }}
                isDeletable={loc.id !== tripDetails.destinations[0].id}
                onDelete={() =>
                  handleDestinationDelete(tripDetails.destinations.indexOf(loc))
                }
              >
                <Field label="Destination" onPress={() => locationAutocompleteRefs.current[index]?.focus()} hasDropdown>
                  <View
                    style={{
                      width: '50%',
                      position: 'relative',
                      height: 30,
                    }}
                  >
                    <LocationAutocomplete
                      ref={(ref) => (locationAutocompleteRefs.current[index] = ref)}
                      value={loc.name}
                      onLocationSelect={(location) =>
                        handleDestinationSelect(
                          location,
                          tripDetails.destinations.indexOf(loc),
                        )
                      }
                    />
                  </View>
                </Field>
                <Field label="Start Date" onPress={() => openDatePicker(tripDetails.destinations.indexOf(loc))}>
                  <DateTouchable activeOpacity={0.8}
                    onPress={() => openDatePicker(tripDetails.destinations.indexOf(loc))}
                  >
                    <Text
                      style={{
                        color: '#fff',
                        fontFamily: 'MuktaVaani',
                        fontSize: 16,
                        textAlign: 'center',
                      }}
                    >
                      {getStartDateText(tripDetails.destinations, loc)}
                    </Text>
                  </DateTouchable>
                </Field>
                <Field label="End Date" onPress={() => openDatePicker(tripDetails.destinations.indexOf(loc))}>
                  <DateTouchable activeOpacity={0.8}
                    onPress={() => openDatePicker(tripDetails.destinations.indexOf(loc))}
                  >
                    <Text
                      style={{
                        color: '#fff',
                        fontFamily: 'MuktaVaani',
                        fontSize: 16,
                        textAlign: 'center',
                      }}
                    >
                      {loc.endDate
                        ? formatDate(loc.endDate)
                        : 'Select End Date'}
                    </Text>
                  </DateTouchable>
                </Field>
              </FieldGroup>
            );
          })}

          <TouchableOpacity
            onPress={handleAddDestination}
            style={{
              paddingVertical: 12,
              marginTop: 16,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
            }}
            activeOpacity={0.5}
          >
            <Text
              style={{
                color: theme.brand.green[500],
                fontSize: 16,
                fontFamily: 'MuktaVaaniMedium',
              }}
            >
              + Add Another Destination
            </Text>
          </TouchableOpacity>
        </View>
        <DatePickerModal
          key={`date-picker-${selectedDestinationIndex}`}
          visible={showDatePicker}
          onClose={() => setShowDatePicker(false)}
          onDateChange={handleDateChange}
          mode="range"
          startDate={tripDetails.destinations[selectedDestinationIndex]?.startDate}
          endDate={tripDetails.destinations[selectedDestinationIndex]?.endDate}
          title="Select Date Range"
          minimumDate={selectedDestinationIndex > 0 ? tripDetails.destinations[selectedDestinationIndex - 1]?.endDate : new Date()}
        />
      </CreateTripContainer>
    </>
  );
}
