import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components/native';

export const StyleProfileCardView = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 10px;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
`;

export const StyleProfileCardHeading = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 4px;
`;

export const StyleProfileCardDescription = styled.Text`
  font-family: 'MuktaVaani';
  line-height: 20px;
  margin-bottom: 8px;
`;

export const StyleProfileCardLink = styled.Text`
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  font-family: 'MuktaVaaniSemiBold';
  line-height: 20px;
  text-decoration: underline;
  text-decoration-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[500]};
`;
