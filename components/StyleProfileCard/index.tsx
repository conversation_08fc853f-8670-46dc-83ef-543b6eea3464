import { fetchStyleQuiz } from "@/methods/style-quiz";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useRef, useState } from "react";
import { TouchableOpacity, View, Image, Dimensions } from "react-native";
import { StyleProfileCardDescription, StyleProfileCardHeading, StyleProfileCardLink, StyleProfileCardView } from "./styles";

// Custom collage component for React Native
const ImageCollage = ({ images, width, height }: { images: string[], width: number, height: number }) => {
  if (images.length === 0) return null;

  const renderScrapbookCollage = () => {
    const imageCount = images.length;
    const baseImageWidth = width * 0.42; // Slightly larger base width
    const baseImageHeight = baseImageWidth * 1.33; // 4:3 aspect ratio as default
    const spacing = width * 0.01; // Minimal spacing for slight overlap
    
    // Calculate positions with moderate overlapping to ensure visibility
    const basePositions = [
      { left: 0, top: 0 }, // Top left
      { left: baseImageWidth * 0.55, top: baseImageHeight * 0.08 }, // Top center
      { left: baseImageWidth * 1.1, top: 0 }, // Top right
      { left: baseImageWidth * 0.08, top: baseImageHeight * 0.45 }, // Bottom left
      { left: baseImageWidth * 0.63, top: baseImageHeight * 0.38 }, // Bottom center
      { left: baseImageWidth * 1.18, top: baseImageHeight * 0.45 }, // Bottom right
    ];

    // Generate additional positions for more images with moderate overlap
    const generatePositions = () => {
      const positions = [...basePositions];
      
      for (let i = 6; i < imageCount; i++) {
        const row = Math.floor(i / 3);
        const col = i % 3;
        const left = (baseImageWidth * 0.55) * col + (Math.random() * 0.15 - 0.075) * baseImageWidth;
        const top = (baseImageHeight * 0.45) * row + (Math.random() * 0.15 - 0.075) * baseImageHeight;
        positions.push({ left, top });
      }
      
      return positions;
    };

    const positions = generatePositions();

    return (
      <View style={{ width, height, position: 'relative' }}>
        {images.map((image, index) => {
          const position = positions[index % positions.length];
          const imageWidth = baseImageWidth * (0.85 + Math.random() * 0.25); // More size variation
          const imageHeight = imageWidth * 1.33; // Maintain aspect ratio
          
          return (
            <View
              key={index}
              style={{
                position: 'absolute',
                left: position.left,
                top: position.top,
                zIndex: index, // Ensure proper layering
              }}
            >
              <Image
                source={{ uri: image }}
                style={{
                  width: imageWidth,
                  height: imageHeight,
                }}
                resizeMode="cover"
              />
            </View>
          );
        })}
      </View>
    );
  };

  return (
    <View style={{ width, height, borderRadius: 8, overflow: 'hidden', backgroundColor: '#e8e8e8' }}>
      {renderScrapbookCollage()}
    </View>
  );
};

export const StyleProfileCard = () => {
  const { data: styleQuizData, isPending } = fetchStyleQuiz();
  const screenWidth = Dimensions.get('window').width;
  const cardPadding = 32; // 16px on each side
  const cardGap = 10;
  const availableWidth = screenWidth - cardPadding - cardGap;
  const collageHeight = (availableWidth * 2) / 3; // 3:2 aspect ratio

  const isNavigating = useRef(false);

  useFocusEffect(useCallback(() => {
    isNavigating.current = false;
  }, []));

  const handlePress = useCallback(() => {
    if (isNavigating.current) return;
    isNavigating.current = true;

    router.push('/style-profile');
  }, []);

  const quizAnswers = styleQuizData?.data?.quiz?.filter((quiz: any) => quiz.answer);
  const images = quizAnswers?.map((quiz: any) => quiz.image).filter(Boolean) || [];

  if (isPending) {
    return null;
  }

  return (
    <StyleProfileCardView>
      <TouchableOpacity onPress={handlePress} style={{ flex: 1 }} activeOpacity={1}>
        <View style={{ flex: 1 }}>
          {quizAnswers?.length > 0 ? (
            <>
              <StyleProfileCardHeading>Your Style Profile</StyleProfileCardHeading>
              <StyleProfileCardDescription>
                {images.length > 0 && (
                  <ImageCollage 
                    images={images} 
                    width={availableWidth} 
                    height={collageHeight} 
                  />
                )}
              </StyleProfileCardDescription>
            </>
          ) : (
            <>
              <StyleProfileCardHeading>Set your Style Profile</StyleProfileCardHeading>
              <StyleProfileCardDescription>
                Enhance your design choices by setting your preferences!
              </StyleProfileCardDescription>
              <StyleProfileCardLink>Visit style profile &gt;</StyleProfileCardLink>
            </>
          )}
        </View>
      </TouchableOpacity>
    </StyleProfileCardView>
  );
};