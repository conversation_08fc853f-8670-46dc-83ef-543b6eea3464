import { getUserProfile } from '@/methods/users';
import { Fragment, useEffect, useState } from 'react';
import { SettingsItem, SettingsItemProps } from '../SettingsItem';
import { Divider, SettingsGroupContainer } from './styles';

type SettingsGroupProps = {
  buttons?: SettingsItemProps[];
  updateData?: (data: any) => void;
};

export const SettingsGroup = ({ buttons }: SettingsGroupProps) => {
  return (
    <SettingsGroupContainer>
      {buttons?.map((button, index) => {
        return (
          <Fragment key={index}>
            <SettingsItem
              {...button}
            />
            {index !== buttons.length - 1 && <Divider />}
          </Fragment>
        );
      })}
    </SettingsGroupContainer>
  );
};
