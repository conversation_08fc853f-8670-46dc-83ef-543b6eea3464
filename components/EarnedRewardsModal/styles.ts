import styled, { DefaultTheme } from 'styled-components/native';

export const ModalContainer = styled.TouchableOpacity`
  flex: 1;
  justify-content: center;
  align-items: center;
  position: relative;
   background-color: rgba(0, 0, 0, 0.6);
`;

export const Backdrop = styled.View`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
`;

export const ModalContent = styled.View`
  background-color: white;
  border-radius: 20px;
  padding: 32px 24px;
  margin: 20px;
  max-width: 320px;
  width: 100%;
  shadow-color: #000;
  shadow-offset: 0px 8px;
  shadow-opacity: 0.3;
  shadow-radius: 20px;
  elevation: 15;
`;

export const CelebrationEmoji = styled.Text`
  font-size: 48px;
  text-align: center;
  margin-bottom: 16px;
`;

export const ModalTitle = styled.Text`
  font-family: 'MuktaVaaniBold';
  font-size: 24px;
  line-height: 32px;
  text-align: center;
  margin-bottom: 12px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;

export const ModalMessage = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  margin-bottom: 24px;
`;

export const PointsContainer = styled.View`
  background-color: #f0fdf4;
  border-radius: 12px;
  padding: 16px 24px;
  margin-bottom: 24px;
  border: 2px solid #bbf7d0;
`;

export const PointsText = styled.Text`
  font-family: 'MuktaVaaniBold';
  font-size: 28px;
  text-align: center;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;
