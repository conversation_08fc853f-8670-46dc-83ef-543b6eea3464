import { useEarnedRewardsModal } from '@/context/EarnedRewardsContext';
import { markEarnedRewardAsSeen } from '@/methods/rewards';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef } from 'react';
import { Animated, Modal, TouchableOpacity, View } from 'react-native';
import Button from '../common/Button';
import {
  CelebrationEmoji,
  ModalContainer,
  ModalContent,
  ModalMessage,
  ModalTitle,
  PointsContainer,
  PointsText
} from './styles';

export const EarnedRewardsModal = () => {
  const { isModalVisible, currentRewards, hideRewardModal } = useEarnedRewardsModal();
  const queryClient = useQueryClient();
  const markSeenMutation = markEarnedRewardAsSeen();

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.3)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    if (isModalVisible) {
      // Mark all current rewards as seen when modal loads
      const markRewardsAsSeen = async () => {
        try {
          const markSeenPromises = currentRewards.map(reward =>
            markSeenMutation.mutateAsync({ _id: reward._id })
          );

          await Promise.all(markSeenPromises);

          // Invalidate queries to refetch data
          queryClient.invalidateQueries({ queryKey: ['earnedRewards', 'unseen'] });
        } catch (error) {
          console.error('Error marking rewards as seen:', error);
        }
      };

      markRewardsAsSeen();

      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset animations
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.3);
      slideAnim.setValue(50);
    }
  }, [isModalVisible]);

  const handleClose = () => {
    // Animate out
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.3,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      hideRewardModal();
    });
  };

  if (!isModalVisible) {
    return null;
  }

  const rewardsInfo = currentRewards[0]?.rewardInfo;

  return (
    <Modal
      transparent
      visible={isModalVisible}
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <ModalContainer
        activeOpacity={1}
        onPress={handleClose}
      >
        <Animated.View
          style={{
            position: 'absolute',
            justifyContent: 'center',
            alignItems: 'center',
            transform: [
              { scale: scaleAnim },
              { translateY: slideAnim },
            ],
          }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            <ModalContent>
              <CelebrationEmoji>🎉</CelebrationEmoji>

              <ModalTitle>{rewardsInfo?.congratsTitle}</ModalTitle>

              <ModalMessage>
                {rewardsInfo?.congratsDescription}
              </ModalMessage>

              <PointsContainer>
                <PointsText>+{rewardsInfo?.points} points</PointsText>
              </PointsContainer>

              <View style={{ flex: 1, alignSelf: 'stretch' }}>
                <Button onPress={handleClose} buttonColor="green" title="Continue" />
              </View>
            </ModalContent>
          </TouchableOpacity>
        </Animated.View>
      </ModalContainer>
    </Modal>
  );
}; 
