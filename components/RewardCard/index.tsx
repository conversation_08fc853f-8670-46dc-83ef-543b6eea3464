import Lock from '@/assets/svg/lock.svg';
import RewardBadge from '@/assets/svg/reward-badge.svg';
import {
  LockIcon,
  RewardCardText,
  RewardCardTextContainer,
  RewardCardView,
  RewardImageContainer,
  RewardUnlockText,
} from './styles';
import { Image, View } from 'react-native';

type Reward = {
  _id: string;
  name: string;
  points: number;
  description: string;
  imageUrl: string;
  congratsTitle: string;
  congratsDescription: string;
  createdAt: string;
  isEarned: boolean;
  code: string;
};

type RewardCardProps = {
  reward: Reward;
};

export const RewardCard = ({ reward }: RewardCardProps) => {
  const {
    imageUrl,
    name,
    points,
    isEarned,
  } = reward;

  const isLocked = !isEarned;
  return (
    <RewardCardView>
      {isLocked && (
        <LockIcon>
          <Lock />
        </LockIcon>
      )}
      <RewardImageContainer>
        <Image source={{ uri: imageUrl }} style={{ width: '100%', height: '100%' }} />
      </RewardImageContainer>
      <RewardCardTextContainer>
        <RewardCardText style={{ textAlign: 'center' }}>{name}</RewardCardText>
      </RewardCardTextContainer>
      {isLocked ? (
        <RewardUnlockText>
          Unlock to earn badge and {points} points!
        </RewardUnlockText>
      ) : (
        <RewardBadge />
      )}
    </RewardCardView>
  );
};
