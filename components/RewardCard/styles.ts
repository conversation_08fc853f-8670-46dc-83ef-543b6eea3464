import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components/native';

export const RewardCardView = styled.View`
  flex: 1;
  position: relative;
  align-items: center;
  justify-content: stretch;
  gap: 8px;
  background-color: #fff;
  width: 100%;
  padding: 20px 16px;
  border-radius: 8px;
`;

export const RewardImageContainer = styled.View`
  width: 80px;
  height: 80px;
`;

export const LockIcon = styled.View`
  position: absolute;
  top: 8px;
  right: 8px;
`;

export const RewardCardTextContainer = styled.View`
  flex: 1;
  justify-content: center;
  margin-bottom: 6px;
`;

export const RewardCardText = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  align-self: center;
`;

export const RewardUnlockText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 12px;
  line-height: 16px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  text-align: center;
  margin-top: auto;
`;
