import React from 'react';
import { TouchableOpacity, View, ActivityIndicator, Text } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import Modal from 'react-native-modal';
import {
  BarIndicatorContainer,
  BarIndicator,
  Container,
  ContentScrollView,
  HeaderContainer,
  BackButton,
  HeaderTitle,
  ActionButton,
  ActionButtonText,
  ImageContainer,
  ItemImage,
  DetailsContainer,
  SectionTitle,
  DetailsList,
  DetailRow,
  DetailLabel,
  DetailValue,
  DateBadge,
  DateText,
} from './styles';

interface Field {
  label: string;
  key: string;
  type?: 'text' | 'date' | 'badge' | 'action';
  action?: () => void;
}

interface DynamicDetailsScreenProps {
  isVisible: boolean;
  onClose: () => void;
  title: string;
  item: any;
  fields: Field[];
  actionButtonText?: string;
  onAction?: () => void;
  sectionTitle?: string;
}

export default function DynamicDetailsScreen({
  isVisible,
  onClose,
  title,
  item,
  fields,
  actionButtonText,
  onAction,
  sectionTitle = 'Item Details'
}: DynamicDetailsScreenProps) {
  // Function to get the appropriate image source
  const getImageSource = () => {
    if (!item?.imageUrl) {
      return require('@/assets/images/placeholder-item.png');
    }

    // Check if the image URL is a local asset path
    if (typeof item.imageUrl === 'string' && item.imageUrl.startsWith('@/')) {
      // For local assets (using require)
      if (item.imageUrl === '@/assets/images/closet/closet-outfit-main1.png') {
        return require('@/assets/images/closet/closet-outfit-main1.png');
      } else if (item.imageUrl === '@/assets/images/closet/closet-outfit-main2.png') {
        return require('@/assets/images/closet/closet-outfit-main2.png');
      } else {
        return require('@/assets/images/placeholder-item.png');
      }
    }

    // For remote URLs, ensure they have the correct format
    let imageUrl = item.imageUrl;

    // If the URL doesn't start with http or https, assume it's a relative path
    if (typeof imageUrl === 'string' && !imageUrl.startsWith('http')) {
      // Add the base URL if it's a relative path
      imageUrl = `https://myuse.s3.ap-southeast-1.amazonaws.com/${imageUrl}`;
    }

    return { uri: imageUrl };
  };

  // Function to render a field based on its type
  const renderFieldValue = (field: Field) => {
    // Check if item is null or undefined
    if (!item) {
      return <DetailValue>Not available</DetailValue>;
    }

    // Safely access the value
    let value = item[field.key];

    // Special handling for category field
    if (field.key === 'category' && typeof value === 'object') {
      // If category is an object, use its name property
      value = value?.name || value;
    }

    if (field.type === 'date' && value) {
      return (
        <DateBadge>
          <DateText>{value}</DateText>
        </DateBadge>
      );
    } else if (field.type === 'action' && field.action) {
      return (
        <TouchableOpacity onPress={field.action}>
          <DetailValue style={{ color: '#0E7E61' }}>{value || 'Set'}</DetailValue>
        </TouchableOpacity>
      );
    } else {
      return <DetailValue>{value || 'Not specified'}</DetailValue>;
    }
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      style={{ margin: 0, justifyContent: 'flex-end', flex: 1 }}
      swipeDirection="down"
      onSwipeComplete={onClose}
      swipeThreshold={15}
      backdropOpacity={0.5}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={250}
      animationOutTiming={200}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={200}
      useNativeDriver={true}
      useNativeDriverForBackdrop={true}
      hideModalContentWhileAnimating={true}
      propagateSwipe={true}
    >
      <Container>
        <BarIndicatorContainer>
          <BarIndicator />
        </BarIndicatorContainer>
        <HeaderContainer>
          <BackButton onPress={onClose}>
            <ArrowLeft size={24} color="#0E7E61" />
          </BackButton>
          <HeaderTitle>{title}</HeaderTitle>
          {actionButtonText && onAction && (
            <ActionButton onPress={onAction}>
              <ActionButtonText>{actionButtonText}</ActionButtonText>
            </ActionButton>
          )}
        </HeaderContainer>

        <ContentScrollView
          showsVerticalScrollIndicator={true}
          contentContainerStyle={{ paddingBottom: 40 }}
          bounces={true}
          alwaysBounceVertical={true}
          scrollEventThrottle={16}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
          overScrollMode="always"
        >
          <ImageContainer>
            {item ? (
              <ItemImage
                source={getImageSource()}
                resizeMode="cover"
                defaultSource={require('@/assets/images/placeholder-item.png')}
              />
            ) : (
              <View style={{ justifyContent: 'center', alignItems: 'center', height: 240 }}>
                <ActivityIndicator size="large" color="#0E7E61" />
              </View>
            )}
          </ImageContainer>

          <DetailsContainer>
            <SectionTitle>{sectionTitle}</SectionTitle>
            {item ? (
              <DetailsList>
                {fields.map((field, index) => (
                  <DetailRow key={`field-${index}`}>
                    <DetailLabel>{field.label}</DetailLabel>
                    {renderFieldValue(field)}
                  </DetailRow>
                ))}
              </DetailsList>
            ) : (
              <View style={{ alignItems: 'center', marginTop: 20 }}>
                <ActivityIndicator size="large" color="#0E7E61" />
                <Text style={{ marginTop: 10, fontFamily: 'Mukta Vaani', color: '#333333' }}>
                  Loading item details...
                </Text>
              </View>
            )}
          </DetailsContainer>
        </ContentScrollView>
      </Container>
    </Modal>
  );
}
