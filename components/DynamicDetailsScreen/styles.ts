import styled from 'styled-components/native';
import { ScrollView } from 'react-native';

// Bar indicator at the top of the modal - helps with swipe gesture
export const BarIndicatorContainer = styled.View`
  width: 100%;
  height: 34px;
  align-items: center;
  justify-content: center;
`;

export const BarIndicator = styled.View`
  width: 56px;
  height: 5px;
  border-radius: 5px;
  background-color: rgba(51, 51, 51, 0.2);
`;

// Main container
export const Container = styled.View`
  background-color: #FFFFFF;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  overflow: hidden;
  max-height: 90%;
  min-height: 70%;
  flex: 0; /* This ensures the container doesn't try to take up all available space */
`;

// Content scroll view
export const ContentScrollView = styled(ScrollView)`
  flex: 1;
  background-color: #FFFFFF;
  padding-bottom: 40px; /* Add padding at the bottom for better scrolling */
`;

// Header container
export const HeaderContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0px;
  width: 100%;
  height: 56px;
  background-color: #FFFFFF;
  border-bottom-width: 1px;
  border-bottom-color: #F0F0F0;
`;

// Back button
export const BackButton = styled.TouchableOpacity`
  height: 40px;
  width: 72px;
  justify-content: center;
`;

// Header title
export const HeaderTitle = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 20px;
  line-height: 28px;
  color: #1C1C1C;
  text-align: center;
`;

// Action button (e.g., "Wear It")
export const ActionButton = styled.TouchableOpacity`
  height: 40px;
  width: 72px;
  justify-content: center;
  align-items: center;
`;

// Action button text
export const ActionButtonText = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
  line-height: 24px;
  color: #0E7E61;
  text-align: center;
`;

// Image container
export const ImageContainer = styled.View`
  width: 240px;
  height: 240px;
  background-color: #EBEBEB;
  border-radius: 8px;
  margin: 20px auto 30px;
  overflow: hidden;
  align-self: center;
`;

// Item image
export const ItemImage = styled.Image`
  width: 100%;
  height: 100%;
`;

// Details container
export const DetailsContainer = styled.View`
  width: 343px;
  margin: 0 auto;
  padding-bottom: 80px; /* Extra padding at the bottom for better scrolling */
  flex-shrink: 1; /* Allow container to shrink if needed */
`;

// Section title
export const SectionTitle = styled.Text`
  font-family: 'CormorantGaramondSemiBold';
  font-size: 24px;
  line-height: 32px;
  color: #333333;
  margin-bottom: 16px;
`;

// Details list
export const DetailsList = styled.View`
  gap: 16px;
`;

// Detail row
export const DetailRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 4px 16px;
  height: 56px;
  background-color: #F0F0F0;
  border: 1px solid #A1A1A1;
  border-radius: 16px;
`;

// Detail label
export const DetailLabel = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.5px;
  color: #333333;
`;

// Detail value
export const DetailValue = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.5px;
  color: #333333;
  max-width: 60%;
`;

// Date badge
export const DateBadge = styled.View`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  height: 32px;
  background-color: #0E7E61;
  border-radius: 4px;
`;

// Date text
export const DateText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.5px;
  color: #FFFFFF;
`;
