import { <PERSON><PERSON>, FlatList, Text, View } from "react-native";
import Modal from "react-native-modal";
import { HeaderText, TemplateModalHeader, TemplateModalTitle } from '@/components/TemplateListModal/styles';
import { TouchableOpacity } from "react-native";
import { Checkbox } from '@/components/common/Checkbox';
import { InputGhost } from '@/components/common/Input';
import { SearchIcon } from 'lucide-react-native';
import { StyleSheet } from "react-native";
import Camera from '@/assets/svg/camera.svg';
import Gallery from '@/assets/svg/gallery.svg';
import OnlineStore from '@/assets/svg/onlineStore.svg';
import SocialMedia from '@/assets/svg/socialMedia.svg';
import { CategoryItem, ClothItemImage } from '@/components/AddClothsCategoryModal/styles';
import * as ImagePicker from "expo-image-picker";
import { styleDiary } from '@/constants/strings';
import { useEffect, useState } from 'react';
import { getCategories, getClothes } from '@/methods/cloths';
import { getUserProfile } from '@/methods/users';
import { getCurrentUserGender } from '@/data/categories';
import { formatCategoriesForDisplay, getStandardizedCategories } from '@/utils/standardCategories';

interface AddNewItemStyleModalProps {
  isAddNewItemVisible: boolean;
  setAddNewItemVisible: (visible: boolean) => void;
  search: string;
  setSearch: (search: string) => void;
  tripName: string;
  setPreSelectedImage: (image: { uri: string; base64?: string } | undefined) => void;
  setImage: (image: string) => void;
}

export default function AddNewItemStyleModal(
  {
    isAddNewItemVisible,
    setAddNewItemVisible,
    search,
    setSearch,
    tripName,
    setPreSelectedImage,
    setImage,
  }: AddNewItemStyleModalProps
) {
  const [displayCategories, setDisplayCategories] = useState<any[]>([{ _id: 'all', name: 'All', category: 'All' }]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ _id: 'all', name: 'All', category: 'All' });
  const [selectedClothes, setSelectedClothes] = useState<any[]>([]);
  const { data: categories, isLoading: isCategoriesLoading, refetch: refetchCategories } = getCategories();
  const { data: clothesList, isLoading: isClothesLoading, refetch: refetchClothes } = getClothes();
  const { data: userProfile } = getUserProfile();

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
        }

        if (!gender) {
          // Fallback to backend categories if there's an error
          if (categories?.itemCategories && categories.itemCategories.length > 0) {
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...categories.itemCategories
            ]);
          }
          return;
        }
        try {
          // Get standardized categories that include both backend and standard categories
          const standardizedCategories = await getStandardizedCategories(gender);

          // Format categories for display in the AddClothsCategoryModal
          // We need to use _id instead of id and include the category property
          const formattedCategories = formatCategoriesForDisplay(
            standardizedCategories,
            true,  // Include "All" category
            '_id', // Use _id property instead of id
            true   // Include category property
          );

          // Log the categories that will be displayed
          formattedCategories.forEach((cat, index) => {
            console.log(`${index + 1}. ${cat.name} (ID: ${cat._id})`);
          });

          setDisplayCategories(formattedCategories);
        } catch (error) {
          console.error('Error getting standardized categories:', error);

          // Fallback to backend categories if there's an error
          if (categories?.itemCategories && categories.itemCategories.length > 0) {
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...categories.itemCategories
            ]);
          }
        }
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to backend categories if there's an error
        if (categories?.itemCategories && categories.itemCategories.length > 0) {
          setDisplayCategories([
            { _id: 'all', name: 'All', category: 'All' },
            ...categories.itemCategories
          ]);
        }
      }
    };

    loadGenderCategories();
  }, [userProfile, categories]);

  const filteredPackingListClothes = clothesList?.items?.filter((item) => {
    // Skip items that are already added
    // if (addedCloths.includes(item._id)) {
    //   return false;
    // }

    // Apply search filter
    const matchesSearch = item?.name?.toLowerCase().includes(search.toLowerCase());
    if (!matchesSearch) {
      return false;
    }

    // If "All" category is selected, show all items that match the search
    if (selectedCategory.name === 'All') {
      return true;
    }

    // For specific categories, use exact matching (case-insensitive)
    const itemCategory = item?.category?.name || '';
    const filterCategory = selectedCategory.name;

    if (!itemCategory) {
      return false;
    }

    // Exact match (case-insensitive)
    return itemCategory.toLowerCase().trim() === filterCategory.toLowerCase().trim();
  });
  const addSelectedCloth = (cloth: any) => {
    cloth.quantity = 1;
    setSelectedClothes([...selectedClothes, cloth]);
  }
  const removeSelectedCloth = (cloth: any) => {
    setSelectedClothes(selectedClothes.filter((item) => item._id !== cloth._id));
  }
  // Function to access camera and take photos
  const accessCamera = async () => {
    try {
      // Request camera permissions first
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow camera access to take photos.');
        return;
      }
      // Launch camera directly
      let result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        base64: true,
        aspect: [4, 3],
        quality: 1,
      });
      if (!result.canceled) {
        setPreSelectedImage({
          uri: result.assets[0].uri,
          base64: result.assets[0].base64 || undefined
        });
      } else {
        console.log('📷 Camera was canceled');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to access camera. Please try again.');
    }
  };
  const pickImage = async () => {
    // No permissions request is necessary for launching the image library
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: false,
      base64: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].base64 || "");
      // setDisplayImage(result.assets[0].uri);
    }
  };

  const addOptions = [
    {
      title: styleDiary.useCamera,
      icon: <Camera color="#0E7E61" />,
      onPress: accessCamera
    },
    {
      title: styleDiary.photoGallery,
      icon: <Gallery color="#0E7E61" />,
      onPress: pickImage
    },
    {
      title: styleDiary.onlineStore,
      icon: <OnlineStore color="#0E7E61" />,
      onPress: () => { }
    },
    {
      title: styleDiary.socialMedia,
      icon: <SocialMedia color="#0E7E61" />,
      onPress: () => { }
    }
  ]
  return (
    <Modal
      style={styles.modal}
      isVisible={isAddNewItemVisible}
      onBackButtonPress={() => setAddNewItemVisible(false)}
      onBackdropPress={() => setAddNewItemVisible(false)}
    >
      <View style={styles.modalContent}>
        <>
          <TemplateModalHeader>
            <TouchableOpacity onPress={() => setAddNewItemVisible(false)}>
              <HeaderText>{styleDiary.close}</HeaderText>
            </TouchableOpacity>
            <TemplateModalTitle>{tripName} Outfit</TemplateModalTitle>
            <TouchableOpacity onPress={() => setAddNewItemVisible(false)}>
              <HeaderText>{styleDiary.add}</HeaderText>
            </TouchableOpacity>
          </TemplateModalHeader>

          <View style={styles.addNewItem}>
            <Text style={styles.addNewItemText}>{styleDiary.addNewItem}</Text>

          </View>
          <View style={styles.addOptionsContainer}>
            {addOptions.map((option, index) => (
              <TouchableOpacity key={index} onPress={option.onPress}>
                <View style={{ width: 81, height: 81, borderRadius: 8, backgroundColor: '#B4D7CE', alignItems: 'center', justifyContent: 'center' }}>
                  {option.icon}
                  <Text style={styles.addOptionText}>{option.title}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
          <View style={styles.addNewItem}>
            <Text style={styles.fromMyuseCloset}>{styleDiary.fromMyuseCloset}</Text>
          </View>

          <View style={styles.searchContainer}>
            <SearchIcon size={12} style={{ marginHorizontal: 10 }} />
            <InputGhost
              placeholder={styleDiary.search}
              onChangeText={(text) => setSearch(text)}
              value={search}
              style={{ width: '100%' }}
            />
          </View>
          <View style={{ marginTop: 20 }}>
            <FlatList
              data={displayCategories}
              horizontal
              keyExtractor={(item) => item._id || item.name}
              showsHorizontalScrollIndicator={false}
              renderItem={({ item }) => {
                return <CategoryItem
                  key={item._id || `category-${item.name}`}
                  onPress={() => setSelectedCategory(item)}
                  isSelected={selectedCategory.name === item.name}
                >
                  <Text style={{ ...styles.categoryItem, color: selectedCategory.name === item.name ? '#FFF' : '#0E7E61' }}>{item.name}</Text>
                </CategoryItem>
              }}
            />
          </View>

          <View style={{ height: '40%' }}>
            <FlatList
              keyExtractor={(item) => item._id}
              contentContainerStyle={{ paddingBottom: 200 }}
              data={[{ addItem: true }, ...filteredPackingListClothes || []]}
              numColumns={3}
              showsVerticalScrollIndicator={false}
              renderItem={({ item }) => {
                return (
                  <TouchableOpacity
                    style={styles.clothItem}
                    key={item._id || `item-${item.name}-${Math.random()}`}
                    onPress={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}>
                    <ClothItemImage source={item.imageUrl ? { uri: item.imageUrl } : require('@/assets/images/pants.png')} />
                    <Checkbox
                      checked={!!selectedClothes.includes(item)}
                      onToggle={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}
                      size={20}
                      containerStyle={styles.checkboxContainer}
                    />
                    <Text style={styles.clothName}>{item.name}</Text>
                  </TouchableOpacity>
                )
              }}
            />
          </View>
        </>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalContent: {
    padding: 20,
    backgroundColor: '#F0F0F0',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  addNewItem: {
    marginVertical: 20,
    alignItems: 'center',
  },
  addNewItemText: {
    fontFamily: 'MuktaVaani-SemiBold',
    fontSize: 18,
  },
  addOptionsContainer: {
    marginVertical: 20,
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'space-around',
  },
  addOptionText: {
    textAlign: 'center',
    fontFamily: 'MuktaVaani-Medium',
    fontSize: 14,
    color: '#0E7E61',
  },
  fromMyuseCloset: {
    fontFamily: 'MuktaVaani-SemiBold',
    fontSize: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 5,
    borderWidth: 0.3,
    borderRadius: 50,
  },
  categoryItem: {
    fontFamily: 'MuktaVaani-Medium',
    fontSize: 14,
  },
  checkboxContainer: {
    position: 'absolute',
    bottom: 8,
    right: 8,
  },
  clothName: {
    fontFamily: 'MuktaVaani-SemiBold',
    fontSize: 12,
    color: '#000',
  },
  clothItem: {
    width: 109,
    margin: 10,
    height: 112,
    backgroundColor: '#EBEBEB',
  },
});
