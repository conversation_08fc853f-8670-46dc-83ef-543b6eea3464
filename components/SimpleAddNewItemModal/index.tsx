import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import Modal from 'react-native-modal';
import styled from 'styled-components/native';
// Import API-based categories
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';

interface SimpleAddNewItemModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddItem: (category: string) => void;
}

export default function SimpleAddNewItemModal({
  isVisible,
  onClose,
  onAddItem,
}: SimpleAddNewItemModalProps) {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [displayCategories, setDisplayCategories] = useState<string[]>([]);

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Reset selection when modal opens
  React.useEffect(() => {
    if (isVisible) {
      setSelectedCategory('');
    }
  }, [isVisible]);

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log(
            'SimpleAddNewItemModal - Using gender from userProfile:',
            gender,
          );
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log(
            'SimpleAddNewItemModal - Using gender from getCurrentUserGender:',
            gender,
          );
        }

        if (!gender) {
          console.warn(
            'SimpleAddNewItemModal - No gender available, using default categories',
          );
          setDisplayCategories([
            'Tops',
            'Bottoms',
            'Dresses',
            'Shoes',
            'Accessories',
          ]);
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        if (!backendCategories || backendCategories.length === 0) {
          console.warn(
            'SimpleAddNewItemModal - No categories returned from backend',
          );
          setDisplayCategories([
            'Tops',
            'Bottoms',
            'Dresses',
            'Shoes',
            'Accessories',
          ]);
          return;
        }

        // Extract category names
        const categoryNames = backendCategories.map(
          (category) => category.name,
        );

        // Make sure we have all the basic categories at minimum
        const basicCategories = [
          'Tops',
          'Bottoms',
          'Dresses',
          'Shoes',
          'Accessories',
        ];

        // Add any missing basic categories
        basicCategories.forEach((basicCat) => {
          if (!categoryNames.includes(basicCat)) {
            categoryNames.push(basicCat);
          }
        });

        console.log(
          `SimpleAddNewItemModal - Loaded ${
            categoryNames.length
          } categories for gender: ${gender || 'unknown'}`,
        );
        setDisplayCategories(categoryNames);
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to a comprehensive set of categories if there's an error
        setDisplayCategories([
          'Tops',
          'Bottoms',
          'Dresses',
          'Matching Sets',
          'Outerwear',
          'Swimwear',
          'Activewear',
          'Shoes',
          'Jewellery',
          'Bags',
          'Accessories',
          'Tech',
          'Others',
        ]);
      }
    };

    loadGenderCategories();
  }, [userProfile]);

  const handleAddItem = () => {
    if (!selectedCategory) {
      Alert.alert('Error', 'Please select a category');
      return;
    }

    onAddItem(selectedCategory);
    onClose();
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{ margin: 0, justifyContent: 'flex-end' }}
      backdropOpacity={0.5}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      useNativeDriver={true}
    >
      <ModalContainer>
        <ModalHeader>
          <ModalTitle>Add New Item</ModalTitle>
          <CloseButton onPress={onClose}>
            <CloseButtonText>Close</CloseButtonText>
          </CloseButton>
        </ModalHeader>

        <ScrollView
          style={{ maxHeight: 400 }}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          <CategoriesContainer>
            {displayCategories.map((category) => (
              <CategoryButton
                key={category}
                active={selectedCategory === category}
                onPress={() => setSelectedCategory(category)}
              >
                <CategoryButtonText active={selectedCategory === category}>
                  {category}
                </CategoryButtonText>
              </CategoryButton>
            ))}
          </CategoriesContainer>
        </ScrollView>

        <AddButton onPress={handleAddItem}>
          <AddButtonText>Add Item</AddButtonText>
        </AddButton>
      </ModalContainer>
    </Modal>
  );
}

// Styled components
const ModalContainer = styled(View)`
  background-color: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 16px;
  width: 100%;
`;

const ModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const ModalTitle = styled(Text)`
  font-family: 'CormorantGaramondSemiBold';
  font-size: 24px;
  color: #1c1c1c;
`;

const CloseButton = styled(TouchableOpacity)`
  padding: 8px;
`;

const CloseButtonText = styled(Text)`
  font-family: 'MuktaVaani';
  font-size: 16px;
  color: #0e7e61;
`;

const CategoriesContainer = styled(View)`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 8px;
`;

interface ActiveProps {
  active: boolean;
}

const CategoryButton = styled(TouchableOpacity)<ActiveProps>`
  background-color: ${(props: ActiveProps) =>
    props.active ? '#0E7E61' : 'transparent'};
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 4px;
  margin-right: 4px;
  border-width: ${(props: ActiveProps) => (props.active ? '0' : '1px')};
  border-color: ${(props: ActiveProps) =>
    props.active ? 'transparent' : '#0E7E61'};
`;

const CategoryButtonText = styled(Text)<ActiveProps>`
  color: ${(props: ActiveProps) => (props.active ? '#FFFFFF' : '#0E7E61')};
  font-family: 'MuktaVaaniSemiBold';
  font-size: 14px;
`;

const AddButton = styled(TouchableOpacity)`
  background-color: #0e7e61;
  padding: 16px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
`;

const AddButtonText = styled(Text)`
  color: #ffffff;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
`;
