import styled from 'styled-components/native';

export const UpcomingTripsContainer = styled.View`
  margin-top: 16px;
  background-color: ;
  width: 100%;
`;

export const UpcomingTripsHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const UpcomingTripsTitle = styled.Text`
  font-size: 24px;
  color: #000;
  font-family: 'CormorantGaramondSemiBold';
`;

export const SeeAllTouchable = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
`;

export const SeeAllText = styled.Text`
  font-size: 14px;
  color: ${({ theme }: { theme: any }) => theme.brand.green[500]};
  font-family: 'MuktaVaani';
`;

export const UpcomingTripCard = styled.TouchableOpacity`
  width: 100%;
  background-color: #a92a73;
  border-radius: 16px;
  padding: 16px;
  height: 200px;
`;

export const CardHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const CardDate = styled.Text`
  font-size: 24px;
  color: #fff;
  font-family: 'MuktaVaaniMedium';
`;

export const CardTitle = styled.Text`
  font-size: 24px;
  color: #fff;
  font-family: 'MuktaVaaniBold';
`;

export const CardLocation = styled.Text`
  font-size: 14px;
  color: #fff;
  font-family: 'MuktaVaani';
`;

export const IconContainer = styled.View`
  background-color: #fff;
  padding: 8px;
  border-radius: 20px;
`;

export const FooterInfoContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  gap: 8px;
  flex-shrink: 1;
  flex-grow: 1;
`;

export const FooterInfoText = styled.Text`
  font-size: 14px;
  color: #fff;
  font-family: 'MuktaVaani';
`;

export const Separator = styled.View`
  width: 2px;
  height: 20px;
  background-color: #fff;
`;
