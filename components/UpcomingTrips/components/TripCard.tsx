import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from 'styled-components';
import { LucideLuggage } from 'lucide-react-native';
import { CardHeader, IconContainer, CardDate, CardTitle, CardLocation, FooterInfoContainer, FooterInfoText, Separator } from '../styles';
import { UpcomingTripCard } from '../styles';
import moment from 'moment';
import { useRouter } from 'expo-router';
export default function TripCard({ trip }: { trip: any }) {
    const theme = useTheme();
    const router = useRouter();
    return (<UpcomingTripCard
      onPress={() => router.push(`/trip-overview/${trip?._id}`)}
    >
        <CardHeader>
          <CardDate>{moment(trip?.startDate).format('DD MMMM YYYY')} - {moment(trip?.endDate).format('DD MMMM YYYY')} </CardDate>
          <IconContainer>
            <LucideLuggage size={20} color={theme.brand.green[500]} />
          </IconContainer>
        </CardHeader>
        <CardTitle>{trip?.name}</CardTitle>
        <CardLocation>
          {trip?.location}
        </CardLocation>
        <FooterInfoContainer>
          <FooterInfoText>
            Trip starts in {moment(trip?.startDate).fromNow()}
          </FooterInfoText>
          <Separator />
          <FooterInfoText>
            Items Packed: {trip?.items?.length}
          </FooterInfoText>
          <Separator />
          <FooterInfoText>
            Total Weight: {trip?.totalWeight}kg
          </FooterInfoText>
        </FooterInfoContainer>
      </UpcomingTripCard>
    );
}