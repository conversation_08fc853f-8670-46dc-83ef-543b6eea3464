import React from 'react';

interface UpcomingTripCardProps {
    // Define the props that UpcomingTripCard will accept
    // For example:
    // title: string;
    // date: string;
}

const UpcomingTripCard: React.FC<UpcomingTripCardProps> = (props) => {
    return (
        <div>
            {/* Render the card content using props */}
            {/* Example: */}
            {/* <h2>{props.title}</h2> */}
            {/* <p>{props.date}</p> */}
        </div>
    );
};

export default UpcomingTripCard; 