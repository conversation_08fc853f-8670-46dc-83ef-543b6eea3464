import styled from 'styled-components/native';

export const ProfileHeaderContainer = styled.View`
  position: relative;
  align-items: center;
  padding-top: 20px;
`;

export const ProfilePicContainer = styled.View`
  position: relative;
`;

export const ProfilePic = styled.Image`
  display: block;
  width: 104px;
  height: 104px;
  border-radius: 52px;
  background-color: white;
`;

export const EditBtnContainer = styled.View`
  position: absolute;
  bottom: -4px;
  right: 0;
  left: 0;
  z-index: 1;
  align-items: center;
`;

export const EditBtn = styled.TouchableOpacity`
  gap: 4px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  padding: 6px 8px;
  border-radius: 20px;
  z-index: 1;
`;

export const EditText = styled.Text`
  font-size: 12px;
  font-family: 'MuktaVaaniMedium';
`;

export const SettingsIcon = styled.TouchableOpacity`
  position: absolute;
  right: 0;
  top: 20px;
  z-index: 1;
`;

export const NameText = styled.Text`
  font-family: CormorantGaramondSemiBold;
  font-size: 32px;
  color: white;
  margin-top: 16px;
`;

export const UserNameTouchable = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 0 20px;
`;

export const UserNameText = styled.Text`
  height: 34px;
  font-family: CormorantGaramondSemiBold;
  font-size: 20px;
  color: white;
  margin-top: 8px;
`;
