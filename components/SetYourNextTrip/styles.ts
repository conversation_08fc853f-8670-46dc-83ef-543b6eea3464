import styled from 'styled-components/native';

export const Container = styled.View`
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
`;

export const Title = styled.Text`
  font-size: 24px;
  font-family: 'CormorantGaramondBold';
  text-align: center;
  margin-bottom: 20px;
`;

export const IllustrationContainer = styled.View`
  align-items: center;
  margin-bottom: 24px;
`;

export const FeaturesContainer = styled.View`
  margin-bottom: 10px;
`;

export const FeatureItem = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
`;

export const FeatureText = styled.Text`
  font-family: 'MuktaVaaniLight';
  font-size: 16px;
  line-height: 20px;
  margin-left: 12px;
  flex: 1;
  text-align: center;
`;
