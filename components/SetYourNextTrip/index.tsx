import { SetYourNextTrip as SetYourNextTripImage } from '@/constants/images';
import { router } from 'expo-router';
import { FC, useCallback } from 'react';
import { Image, View } from 'react-native';
import Button from '../common/Button';
import {
  Container,
  FeatureItem,
  FeaturesContainer,
  FeatureText,
  IllustrationContainer,
  Title,
} from './styles';

type SetYourNextTripProps = {
  numDaysSinceLastTrip?: number;
}

const SetYourNextTrip: FC<SetYourNextTripProps> = ({ numDaysSinceLastTrip = 0 }) => {
  const features = [
    {
      text: '📋 Organize your packing list'
    },
    {
      text: '🌦️ See the weather forecast for your trip'
    },
    {
      text: '👜 ⁠Plan outfits and activities according to the weather'
    },
    {
      text: '🛍️ Connect your past purchases to pack smarter'
    }
  ];

  const handleAddPress = useCallback(() => {
    router.push('/create-trip');
  }, []);

  const hasLastTrip = numDaysSinceLastTrip > 0;
  const buttonTitle = hasLastTrip ? 'Plan your Next Adventure' : 'Plan your Trip';

  return (
    <Container>
      <Title>Set Your Next Trip</Title>

      <IllustrationContainer>
        <Image source={SetYourNextTripImage} style={{ width: 130, height: 130 }} />
      </IllustrationContainer>

      <FeaturesContainer>
        {hasLastTrip ? (
          <>
            <FeatureItem>
              <FeatureText>It's been {numDaysSinceLastTrip} days since your last trip.</FeatureText>
            </FeatureItem>
            <FeatureItem>
              <FeatureText>Where are you going next?</FeatureText>
            </FeatureItem>
          </>
        ) : features.map((feature, index) => (
          <FeatureItem key={index}>
            <FeatureText>{feature.text}</FeatureText>
          </FeatureItem>
        ))}
      </FeaturesContainer>

      <Button title={buttonTitle} onPress={handleAddPress} activeOpacity={0.8} />
    </Container>
  );
};

export default SetYourNextTrip;
