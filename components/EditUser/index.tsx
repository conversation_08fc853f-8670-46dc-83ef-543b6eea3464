import ImageIcon from '@/assets/svg/image-icon.svg';
import UserCardIcon from '@/assets/svg/user-card.svg';
import React from 'react';
import { EditUserButton, EditUserButtonText, EditUserView } from './styles';

interface EditUserProps {
  onChangeProfilePicture?: () => void;
  onChangeUsername?: () => void;
}

export const EditUser = ({
  onChangeProfilePicture,
  onChangeUsername,
}: EditUserProps) => {
  return (
    <EditUserView edges={['bottom']}>
      <EditUserButton activeOpacity={0.8} onPress={onChangeProfilePicture}>
        <ImageIcon />
        <EditUserButtonText>Change profile picture</EditUserButtonText>
      </EditUserButton>
      <EditUserButton activeOpacity={0.8} onPress={onChangeUsername}>
        <UserCardIcon />
        <EditUserButtonText>Change username</EditUserButtonText>
      </EditUserButton>
    </EditUserView>
  );
};
