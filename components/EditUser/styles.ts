import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components/native';

export const EditUserView = styled.View`
  padding: 24px 16px 60px;
  gap: 20px;
`;

export const EditUserButton = styled.TouchableOpacity`
  flex-direction: row;
  gap: 16px;
  background-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[100]};
  padding: 16px;
  border-radius: 8px;
`;

export const EditUserButtonText = styled.Text`
  font-size: 14px;
  font-family: 'MuktaVaaniMedium';
  line-height: 20px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  margin-top: 2px;
`;
