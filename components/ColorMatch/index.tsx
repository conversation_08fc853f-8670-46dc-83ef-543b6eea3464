import React, { FC, useState } from 'react';
import { Text } from 'react-native';
import { useTheme } from 'styled-components/native';
import {
  ButtonCircle,
  ColorMatchContainer,
  ColorView,
  ColorsContainer,
  PreferenceSliderContainer,
  SliderButton,
  SliderLine,
} from './styles';

type ColorMatchProps = {
  colors: string[];
};

export const ColorMatch: FC<ColorMatchProps> = ({
  colors = [],
}: ColorMatchProps) => {
  const [value, setValue] = useState(1);

  return (
    <ColorMatchContainer>
      <ColorsContainer>
        {colors.map((color, index) => (
          <ColorView key={index} style={{ backgroundColor: color }} />
        ))}
      </ColorsContainer>
      <PreferenceSlider value={value} onValueChange={setValue} />
    </ColorMatchContainer>
  );
};

type PreferenceSliderProps = {
  value: number;
  onValueChange: (value: number) => void;
};

const PreferenceSlider: FC<PreferenceSliderProps> = ({
  value,
  onValueChange,
}: PreferenceSliderProps) => {
  const theme = useTheme();

  return (
    <PreferenceSliderContainer>
      <SliderLine />
      <SliderButton
        style={{ alignItems: 'flex-start' }}
        onPress={() => onValueChange(0)}
        activeOpacity={1}
      >
        <ButtonCircle
          style={{
            backgroundColor: value === 0 ? theme.brand.green[500] : '#ffffff',
            borderColor: value === 0 ? theme.brand.green[500] : '#333333',
          }}
        />
        <Text
          style={{
            fontFamily: 'MuktaVaani',
            color: value === 0 ? theme.brand.green[500] : undefined,
          }}
        >
          No
        </Text>
      </SliderButton>
      <SliderButton
        style={{ alignItems: 'center' }}
        onPress={() => onValueChange(1)}
        activeOpacity={1}
      >
        <ButtonCircle
          style={{
            backgroundColor: value === 1 ? theme.brand.green[500] : '#ffffff',
            borderColor: value === 1 ? theme.brand.green[500] : '#333333',
          }}
        />
        <Text
          style={{
            fontFamily: 'MuktaVaani',
            color: value === 1 ? theme.brand.green[500] : undefined,
          }}
        >
          Maybe
        </Text>
      </SliderButton>
      <SliderButton
        style={{ alignItems: 'flex-end' }}
        onPress={() => onValueChange(2)}
        activeOpacity={1}
      >
        <ButtonCircle
          style={{
            backgroundColor: value === 2 ? theme.brand.green[500] : '#ffffff',
            borderColor: value === 2 ? theme.brand.green[500] : '#333333',
          }}
        />
        <Text
          style={{
            fontFamily: 'MuktaVaani',
            color: value === 2 ? theme.brand.green[500] : undefined,
          }}
        >
          Yes
        </Text>
      </SliderButton>
    </PreferenceSliderContainer>
  );
};
