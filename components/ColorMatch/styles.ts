import styled from 'styled-components/native';

export const ColorMatchContainer = styled.View`
  flex-direction: column;
  gap: 8px;
  width: 100%;
  min-height: 50px;
`;

export const ColorsContainer = styled.View`
  flex-direction: row;
  gap: 8px;
  width: 100%;
  margin-bottom: 8px;
`;

export const ColorView = styled.View`
  flex: 1;
  height: 36px;
  border: 1px solid #5c5c5c;
`;

export const PreferenceSliderContainer = styled.View`
  position: relative;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const SliderLine = styled.View`
  position: absolute;
  top: 5px;
  left: 0;
  flex: 1;
  width: 100%;
  height: 1px;
  background-color: #5c5c5c;
`;

export const SliderButton = styled.TouchableOpacity`
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  width: 50px;
`;

export const ButtonCircle = styled.View`
  width: 11px;
  height: 11px;
  border-radius: 11px;
  background-color: #ffffff;
  border: 1px solid #333333;
`;
