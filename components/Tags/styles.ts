import styled, { DefaultTheme } from 'styled-components/native';

export const TagTouchableOpacity = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  gap: 7px;
  height: 40px;
  border-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[500]};
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
  padding: 0 24px;
  max-width: 100%;
`;

export const TagsText = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
`;

export const PlusText = styled.Text`
  font-size: 20px;
  line-height: 20px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;
