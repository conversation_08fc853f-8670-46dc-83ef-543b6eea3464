import CheckWhite from '@/assets/svg/check-white.svg';
import React, { FC, useCallback, useState } from 'react';
import { useTheme } from 'styled-components/native';
import { PlusText, TagTouchableOpacity, TagsText } from './styles';

type TagsConfig = {
  text: string;
  selected: boolean;
};

interface TagsProps {
  initialTags?: TagsConfig[];
  onToggleTag?: (tags: string[]) => void;
}

/**
 * This component is used for the Style Hardpasses screen
 */

export const Tags: FC<TagsProps> = ({
  initialTags = [],
  onToggleTag,
}: TagsProps) => {
  const theme = useTheme();
  const [tags, setTags] = useState<TagsConfig[]>(initialTags);

  const handleToggleTag = useCallback(
    (indexToToggle: number) => {
      const newTags = tags.map((tag, index) => {
        if (index === indexToToggle) {
          return { ...tag, selected: !tag.selected };
        }
        return tag;
      });
      setTags(newTags);

      if (onToggleTag) {
        const selectedTags = newTags
          .filter((tag) => tag.selected)
          .map((tag) => tag.text);
        onToggleTag(selectedTags);
      }
    },
    [tags, onToggleTag],
  );

  return (
    <>
      {tags.map((tag, index) => (
        <TagTouchableOpacity
          key={index}
          onPress={() => handleToggleTag(index)}
          activeOpacity={1}
          style={{
            backgroundColor: tag.selected
              ? theme.brand.green[500]
              : 'transparent',
          }}
        >
          {tag.selected ? <CheckWhite /> : <PlusText>+</PlusText>}
          <TagsText
            numberOfLines={1}
            style={{
              color: tag.selected ? '#fff' : theme.brand.green[500],
            }}
          >
            {tag.text}
          </TagsText>
        </TagTouchableOpacity>
      ))}
    </>
  );
};
