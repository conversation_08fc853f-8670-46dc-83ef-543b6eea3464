import { Canvas } from '@shopify/react-native-skia';
import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components/native';

export const CalendarListContainer = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  justify-content: space-between;
  margin-top: 90px;
`;

export const DayItem = styled(Canvas) <{ currentDay: boolean, selected: boolean }>`
  height: 88px;
  width: 42px;
  border-radius: 46px;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  ${({ currentDay, selected, theme }: { currentDay: boolean, selected: boolean, theme: DefaultTheme }) =>{

    if(currentDay && selected){
      return `border: 5px solid ${theme.brand.green[500]}`;
    }
    
    if(currentDay){
      return `border: 2px solid ${theme.brand.green[500]}`;
    }

    if(selected){
      return `border: 5px solid #fff;
      `;
    }
  }};
`;

export const DayItemContent = styled.View`
  position: absolute;
  height: 88px;
  width: 42px;
  justify-content: center;
  align-items: center;
  gap: 3px;
`;

export const DayItemText = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: #fff;
`;

export const DayItemTextSmall = styled.Text`
  font-size: 12px;
  font-weight: 600;
  color: #fff;
`;

export const WhiteBackDrop = styled.View`
  background-color: red;
  height: 88px;
  width: 50px;
  position: absolute;
  top: 50px;
  right: 0px;
  z-index: -2;
`;

export const CalendarContent = styled.View`
  background-color: #fff;
  width: 100%;
  height: 200px;
  margin-top: 60px;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.05);
`;

export const CalendarContentTitle = styled.Text`
  font-family: CormorantGaramond;
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
`;

export const ComingSoonText = styled.Text`
  font-family: CormorantGaramond;
  font-size: 28px;
  font-weight: 500;
  color: #888;
  text-align: center;
  margin-top: 32px;
  letter-spacing: 0.5px;
`;

export const ClothsItemContainer = styled.View`
    flex-wrap: wrap;
    flex-direction: row;
    gap: 16px;
    width: 100%;
    margin-top: 16px;
    justify-content: space-between;
`;

export const ClothsItem = styled.View<{ containerWidth: number }>`
  height: 100px;
  width: ${({ containerWidth }: { containerWidth: number }) => (containerWidth / 3) - 16}px;
  background-color: grey;
  border-radius: 4px;
  flex-grow: 0;
`;

export const PaginationContainer = styled.View`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 8px 0;
  gap: 8px;
`;

interface PaginationDotProps {
  active: boolean;
}

export const PaginationDot = styled.View<PaginationDotProps>`
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: ${(props: PaginationDotProps) => props.active ? '#000' : '#ccc'};
  opacity: ${(props: PaginationDotProps) => props.active ? 1 : 0.5};
`;

export const BoxConnector = styled.View`
  height: 100px;
  width: 42px;
  background-color: white;
  position: absolute;
  top: 40px;
  left: 0px;
  z-index: -1;
`;
