import { ActivityIndicator, FlatList, LayoutChangeEvent, TouchableOpacity, View } from 'react-native';
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalTitle,
  HeaderText,
  AddClothsModalInput,
  ClothItemContainer,
  ClothItemName,
  ClothesContainer,
  AddClothItemContainer,
  CategoryItem,
  CategoryItemText,
  ClothItemImage,
  ClothNameContainer,
  ItemCategoryText
} from './styles';
import Modal from 'react-native-modal';
import { XIcon, PlusIcon } from 'lucide-react-native';
import Input from '../common/Input';
import { Checkbox } from '@/components/common/Checkbox';
import { useState, useEffect } from 'react';
import { CLOTHES, CLOTHES_CATEGORIES } from '@/constants/Clothes';
import { getCategories, getClothes, UploadImage } from '@/methods/cloths';
// Import API-based categories
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';
import { getStandardizedCategories, formatCategoriesForDisplay } from '@/utils/standardCategories';



interface AddClothsCategoryModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
  item: any;
  packingList: any[];
  addedCloths: any[];
  setIsAddItemsModalVisible: (visible: boolean) => void;
}

export default function AddClothsCategoryModal({ isVisible, onClose, onAdd, item, packingList = [], addedCloths = [], setIsAddItemsModalVisible }: AddClothsCategoryModalProps) {

  const { mutate: uploadImage } = UploadImage();

  const [search, setSearch] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [clothes, setClothes] = useState<any[]>([]);
  const [width, setWidth] = useState(0);
  const [image, setImage] = useState<string | null>(null);
  const [selectedClothes, setSelectedClothes] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ _id: 'all', name: 'All', category: 'All' });
  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<any[]>([{ _id: 'all', name: 'All', category: 'All' }]);

  const { data: categories, isLoading: isCategoriesLoading, refetch: refetchCategories } = getCategories();
  const { data: clothesList, isLoading: isClothesLoading, refetch: refetchClothes } = getClothes();
  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  const addSelectedCloth = (cloth: any) => {
    cloth.quantity = 1;
    setSelectedClothes([...selectedClothes, cloth]);
  }

  const removeSelectedCloth = (cloth: any) => {
    setSelectedClothes(selectedClothes.filter((item) => item._id !== cloth._id));
  }

  useEffect(() => {
    refetchClothes();
  }, [isVisible]);

  // Helper function to extract unique categories from items (similar to SideFilterPanel)
  const getUsedCategories = (items: any[]): Set<string> => {
    console.log('\n🔍 ADD CLOTHS CATEGORY MODAL - ANALYZING USED CATEGORIES 🔍');
    const usedCategories = new Set<string>();

    if (!items || !Array.isArray(items)) {
      console.log('❌ No items or items is not an array');
      return usedCategories;
    }

    console.log('📊 Total items to analyze:', items.length);

    items.forEach((item, index) => {
      console.log(`Item ${index + 1}:`, {
        id: item._id,
        name: item.name,
        category: item.category,
        categoryName: item.categoryName,
        itemCategoryId: item.itemCategoryId
      });

      // Get the display category name using our comprehensive mapping
      const displayCategoryName = getCategoryDisplayName(item);
      const categoryNameLower = displayCategoryName.toLowerCase().trim();

      usedCategories.add(categoryNameLower);
      console.log(`✅ Added category: "${categoryNameLower}" (display: "${displayCategoryName}")`);
    });

    console.log('📋 Final used categories:', Array.from(usedCategories));
    return usedCategories;
  };

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('AddClothsCategoryModal - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log('AddClothsCategoryModal - Using gender from getCurrentUserGender:', gender);
        }

        if (!gender) {
          console.warn('AddClothsCategoryModal - No gender available, using default categories');
          // Fallback to backend categories if there's an error
          if (categories?.itemCategories && categories.itemCategories.length > 0) {
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...categories.itemCategories
            ]);
          }
          return;
        }

        console.log('\n🔍 ADD CLOTHS CATEGORY MODAL - LOADING STANDARDIZED CATEGORIES 🔍');

        try {
          // Get standardized categories that include both backend and standard categories
          const standardizedCategories = await getStandardizedCategories(gender);

          console.log(`Loaded ${standardizedCategories.length} standardized categories`);

          // Format categories for display in the AddClothsCategoryModal
          // We need to use _id instead of id and include the category property
          const formattedCategories = formatCategoriesForDisplay(
            standardizedCategories,
            false,  // Don't include "All" category here, we'll add it manually
            '_id', // Use _id property instead of id
            true   // Include category property
          );

          // Get categories that have items in them
          console.log('🔍 Raw allItems:', allItems);
          console.log('🔍 Items count:', allItems?.length);

          // Get used categories from available items (excluding already added items)
          const availableItems = allItems?.filter((item: any) => !addedCloths.includes(item._id)) || [];
          const usedCategories = getUsedCategories(availableItems);

          // Filter categories to show only those that have items
          const categoriesWithItems = formattedCategories.filter((category) => {
            // Check if this category has items
            const categoryNameLower = category.name.toLowerCase().trim();
            const hasItems = usedCategories.has(categoryNameLower);
            console.log(`${hasItems ? '✅' : '❌'} Category "${category.name}" (${categoryNameLower}) ${hasItems ? 'has' : 'has no'} items`);
            return hasItems;
          });

          // Remove duplicates by name (case-insensitive)
          const uniqueCategories = categoriesWithItems.filter((category, index, array) => {
            return array.findIndex(c => c.name.toLowerCase() === category.name.toLowerCase()) === index;
          });

          console.log('📋 Categories after deduplication:', uniqueCategories.map(c => ({ id: c._id, name: c.name })));

          // Sort categories alphabetically by name
          uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));

          // Add "All" category as the first item
          const allCategory = { _id: 'all', name: 'All', category: 'All' };
          uniqueCategories.unshift(allCategory);

          // Log the categories that will be displayed
          console.log('=== CATEGORIES DISPLAYED IN ADD CLOTHS CATEGORY MODAL ===');
          uniqueCategories.forEach((cat, index) => {
            console.log(`${index + 1}. ${cat.name} (ID: ${cat._id})`);
          });
          console.log('=== END DISPLAYED CATEGORIES ===');

          setDisplayCategories(uniqueCategories);
        } catch (error) {
          console.error('Error getting standardized categories:', error);

          // Fallback to backend categories if there's an error
          if (categories?.itemCategories && categories.itemCategories.length > 0) {
            // Sort fallback categories alphabetically
            const sortedBackendCategories = [...categories.itemCategories].sort((a, b) => a.name.localeCompare(b.name));
            setDisplayCategories([
              { _id: 'all', name: 'All', category: 'All' },
              ...sortedBackendCategories
            ]);
          }
        }
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to backend categories if there's an error
        if (categories?.itemCategories && categories.itemCategories.length > 0) {
          // Sort fallback categories alphabetically
          const sortedBackendCategories = [...categories.itemCategories].sort((a, b) => a.name.localeCompare(b.name));
          setDisplayCategories([
            { _id: 'all', name: 'All', category: 'All' },
            ...sortedBackendCategories
          ]);
        }
      }
    };

    loadGenderCategories();
  }, [userProfile, categories, allItems, addedCloths]); // Added allItems and addedCloths as dependencies




  const allItems = clothesList?.items || [];

  // Helper function to get the display category name (same as Closet implementation)
  const getCategoryDisplayName = (item: any): string => {
    // Use category.mainCategory field (current backend schema)
    if (item.category?.mainCategory) {
      return item.category.mainCategory;
    }

    // Fallback: Try legacy category.name field
    if (item.category?.name) {
      return item.category.name;
    }

    // Try mainCategory field directly on item (if it exists)
    if (item.mainCategory) {
      return item.mainCategory;
    }

    // If we have an itemCategoryId, try to map it using a comprehensive mapping approach
    if (item.itemCategoryId) {
      // Enhanced category mapping that covers more possibilities
      const comprehensiveCategoryMap: {[key: string]: string} = {
        // Direct matches
        'tops': 'Tops',
        'bottoms': 'Bottoms',
        'shoes': 'Shoes',
        'dresses': 'Dresses',
        'accessories': 'Accessories',
        'outerwear': 'Outerwear',
        'loungewear': 'Loungewear',
        'activewear': 'Activewear',
        'swimwear': 'Swimwear',
        'underwear': 'Underwear',
        'sleepwear': 'Sleepwear',
        'jewelry': 'Jewelry',
        'bags': 'Bags',
        'tech': 'Tech',
        'others': 'Others',
      };

      const itemCategoryLower = item.itemCategoryId.toLowerCase();

      // First try exact match
      if (comprehensiveCategoryMap[itemCategoryLower]) {
        return comprehensiveCategoryMap[itemCategoryLower];
      }

      // Then try partial matches
      for (const [key, value] of Object.entries(comprehensiveCategoryMap)) {
        if (itemCategoryLower.includes(key) || key.includes(itemCategoryLower)) {
          return value;
        }
      }

      // If no match found, return the itemCategoryId with proper capitalization
      return item.itemCategoryId.charAt(0).toUpperCase() + item.itemCategoryId.slice(1).toLowerCase();
    }

    // Fallback to 'Uncategorized' if no category information is available
    return 'Uncategorized';
  };

  const filteredPackingListClothes = allItems?.filter((item: any) => {
    // Skip items that are already added
    if (addedCloths.includes(item._id)) {
      return false;
    }

    // Apply search filter
    const matchesSearch = item?.name?.toLowerCase().includes(search.toLowerCase());
    if (!matchesSearch) {
      return false;
    }

    // If "All" category is selected, show all items that match the search
    if (selectedCategory.name === 'All') {
      return true;
    }

    // For specific categories, use exact matching (case-insensitive)
    // Use mainCategory from the backend API structure
    const itemCategory = item?.category?.mainCategory || item?.category?.name || '';
    const filterCategory = selectedCategory.name;

    if (!itemCategory) {
      return false;
    }

    // Exact match (case-insensitive)
    return itemCategory.toLowerCase().trim() === filterCategory.toLowerCase().trim();
  });




  return <Modal
    key={item?.id}
    style={{ justifyContent: 'flex-end', margin: 0 }}
    isVisible={isVisible} onBackButtonPress={onClose} onBackdropPress={onClose}>

    <AddClothsModalContainer>
      <AddClothsModalHeader>
        <TouchableOpacity onPress={onClose}>
          <HeaderText>Close</HeaderText>
        </TouchableOpacity>
        <AddClothsModalTitle>
          Clothes
        </AddClothsModalTitle>
        <TouchableOpacity onPress={() => {
          onAdd(selectedClothes);
          setSelectedClothes([]);
          onClose();
        }}>
          <HeaderText>Add</HeaderText>
        </TouchableOpacity>
      </AddClothsModalHeader>
      <View style={{ marginTop: 20 }}>
        <AddClothsModalInput placeholder="Search"
          value={search}
          onChangeText={setSearch}
        />
      </View>
      <View style={{ marginTop: 20 }}>
        <FlatList
          data={displayCategories}
          horizontal
          keyExtractor={(item) => item._id || item.name}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 0 }}
          renderItem={({ item }) => {
            return <CategoryItem
              key={item._id || `category-${item.name}`}
              isSelected={selectedCategory.name === item.name}
              onPress={() => setSelectedCategory(item)}>
              <CategoryItemText isSelected={selectedCategory.name === item.name}>{item.name}</CategoryItemText>
            </CategoryItem>
          }}
        />
      </View>
      <ClothesContainer
        onLayout={handleLayout}
        style={{ marginTop: 20 }}>
        <FlatList
          keyExtractor={(item) => item._id || 'add-item'}
          contentContainerStyle={{ paddingBottom: 200 }}
          data={[{ addItem: true }, ...filteredPackingListClothes || []]}
          numColumns={2}
          columnWrapperStyle={{ justifyContent: 'space-between' }}
          ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => {
            if (item.addItem) {
              return (
                <AddClothItemContainer
                  key="add-item-container"
                  width={width}
                  onPress={() => {
                    onClose();
                    setTimeout(() => {
                      setIsAddItemsModalVisible(true);
                      setSelectedClothes([]);
                    }, 500);
                  }}>
                  <PlusIcon size={25} color="#767676" />
                </AddClothItemContainer>
              )
            }
            return (
              <TouchableOpacity
                key={item._id || `item-${item.name}-${Math.random()}`}
                onPress={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}>
                <ClothItemContainer width={width} isSelected={selectedClothes.includes(item) || false}>
                  <ClothItemImage source={item.imageUrl ? { uri: item.imageUrl } : require('@/assets/images/placeholder-item.png')} />

                  <Checkbox
                    checked={!!selectedClothes.includes(item)}
                    onToggle={() => selectedClothes.includes(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}
                    size={20}
                    containerStyle={{ position: 'absolute', bottom: 8, right: 8 }}
                  />
                </ClothItemContainer>
                <ClothNameContainer>
                  <ClothItemName>{item.name}</ClothItemName>
                  <ItemCategoryText numberOfLines={1}>
                    {getCategoryDisplayName(item)}
                  </ItemCategoryText>
                </ClothNameContainer>
              </TouchableOpacity>
            )
          }}
        />
      </ClothesContainer>

    </AddClothsModalContainer>

  </Modal>;
}