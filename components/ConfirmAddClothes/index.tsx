import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView, Alert, ActivityIndicator, StatusBar, Platform, Dimensions } from 'react-native';
import Modal from 'react-native-modal';
import styled from 'styled-components/native';
import { addItem } from '@/methods/cloths';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'expo-router';

// Get screen dimensions
const { width: screenWidth } = Dimensions.get('window');

// Styled components
const Container = styled(View)`
  flex: 1;
  background-color: #fff;
  padding-top: ${Platform.OS === 'ios' ? '40px' : '0px'};
  margin-top: 0;
  margin-bottom: 0;
`;

const Header = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 8px;
  width: ${screenWidth}px;
  height: 56px;
  background-color: #FFFFFF;
  border-bottom-width: 1px;
  border-bottom-color: #F5F5F5;
`;

const HeaderButton = styled(TouchableOpacity)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 8px;
  width: 72px;
  height: 40px;
  border-radius: 100px;
`;

const HeaderButtonText = styled(Text)`
  width: ${(props: { color?: string }) => props.color === '#FF3B30' ? '45px' : '32px'};
  height: 24px;
  font-family: 'MuktaVaaniMedium';
  font-style: normal;
  font-size: 16px;
  line-height: 24px;
  display: flex;
  align-items: center;
  text-align: center;
  color: ${(props: { color?: string }) => props.color || '#0E7E61'};
`;

const HeaderTitle = styled(Text)`
  width: 150px;
  height: 28px;
  font-family: 'MuktaVaaniSemiBold';
  font-style: normal;
  font-size: 20px;
  line-height: 28px;
  color: #1C1C1C;
  text-align: center;
`;

const ContentContainer = styled(View)`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px 0px 40px;
  gap: 40px;
  width: ${screenWidth - 32}px;
  margin: 12px 16px;
  overflow-y: scroll;
`;

const ImageContainer = styled(View)`
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0px;
  width: 240px;
  height: 240px;
  background-color: #EBEBEB;
  border-radius: 8px;
`;

const ItemImage = styled(Image)`
  width: 240px;
  height: 240px;
  border-radius: 8px;
`;

const ImageLoadingContainer = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  justify-content: center;
  align-items: center;
  background-color: rgba(240, 240, 240, 0.7);
  border-radius: 8px;
`;

const TableContainer = styled(View)`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px;
  gap: 16px;
  width: 100%;
  margin-bottom: 20px;
`;

const TableTitle = styled(Text)`
  width: auto;
  min-height: 32px;
  margin-bottom: 8px;
  font-family: 'CormorantGaramondSemiBold';
  font-size: 24px;
  line-height: 32px;
  color: #333333;
`;

const StatsTable = styled(View)`
  width: 100%;
  background-color: #F0F0F0;
  border: 1px solid #C0C0C0;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 8px;
  border-collapse: collapse;
`;

interface TableRowProps {
  height?: number;
}

const TableRow = styled(View)<TableRowProps>`
  width: 100%;
  flex-direction: row;
  align-items: stretch;
  background-color: rgba(255, 255, 255, 0.0001);
  min-height: ${(props: TableRowProps) => props.height || 48}px;
  margin-top: -1px;
  flex-wrap: nowrap;
`;

const TableCell = styled(View)`
  width: 50%;
  border-width: 1px;
  border-style: solid;
  border-color: #C0C0C0;
  background-color: rgba(255, 255, 255, 0.5);
  justify-content: center;
  padding: 14px 16px 16px 16px;
  margin-left: -1px;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
`;

// Removed unused DetailRow component

const CellTitle = styled(Text)`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 14px;
  line-height: 20px;
  color: #333333;
  flex-wrap: wrap;
  flex-shrink: 1;
  width: 100%;
`;

const CellText = styled(Text)`
  font-family: 'MuktaVaani';
  font-size: 14px;
  line-height: 22px;
  color: #333333;
  flex-wrap: wrap;
  flex-shrink: 1;
`;

const DateBadge = styled(View)`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  width: 106px;
  height: 32px;
  background: #0E7E61;
  border-radius: 4px;
`;

const DateText = styled(Text)`
  width: 90px;
  height: 24px;
  font-family: 'MuktaVaani';
  font-style: normal;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.5px;
  color: #FFFFFF;
`;

const LoadingContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
`;

// Define the fields for the details screen
const detailsFields = [
  { label: 'Name', key: 'name', type: 'text' },
  { label: 'Color', key: 'color', type: 'text' },
  { label: 'Size', key: 'size', type: 'text' },
  { label: 'Material', key: 'material', type: 'text' },
  { label: 'Category', key: 'category', type: 'text' },
  { label: 'Brand', key: 'brand', type: 'text' },
  { label: 'Price', key: 'price', type: 'text' },
  { label: 'Tags', key: 'tags', type: 'text' },
  { label: 'Purchase Date', key: 'purchaseDate', type: 'date' },
  { label: 'Last Worn Date', key: 'lastWornDate', type: 'date' },
];

interface ConfirmAddClothesProps {
  isVisible: boolean;
  onClose: () => void;
  item: any;
  onSave: () => void;
}

export default function ConfirmAddClothes({ isVisible, onClose, item, onSave }: ConfirmAddClothesProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Get the addItem mutation hook at the component level
  const addItemMutation = addItem();

  // Log when the modal visibility changes
  React.useEffect(() => {
    console.log('ConfirmAddClothes isVisible:', isVisible);
  }, [isVisible]);

  // Function to get the appropriate image source
  const getImageSource = () => {
    try {
      if (!item) {
        console.log('No item, using placeholder');
        return require('@/assets/images/placeholder-item.png');
      }

      if (!item.imageUrl) {
        console.log('No imageUrl, using placeholder');
        return require('@/assets/images/placeholder-item.png');
      }

      console.log('Image URL:', item.imageUrl);

      // Check if the image URL is a local asset path
      if (typeof item.imageUrl === 'string' && item.imageUrl.startsWith('@/')) {
        console.log('Local asset path detected');
        // For local assets (using require)
        if (item.imageUrl === '@/assets/images/closet/closet-outfit-main2.png') {
          return require('@/assets/images/closet/closet-outfit-main2.png');
        } else if (item.imageUrl === '@/assets/images/closet/closet-outfit-main3.png') {
          return require('@/assets/images/closet/closet-outfit-main3.png');
        } else {
          console.log('Unknown local asset, using placeholder');
          return require('@/assets/images/placeholder-item.png');
        }
      }

      // For remote URLs, ensure they have the correct format
      let imageUrl = item.imageUrl;

      // If the URL doesn't start with http or https, assume it's a relative path
      if (typeof imageUrl === 'string' && !imageUrl.startsWith('http')) {
        // Add the base URL if it's a relative path
        imageUrl = `https://myuse.s3.ap-southeast-1.amazonaws.com/${imageUrl}`;
        console.log('Formatted URL:', imageUrl);
      }

      return { uri: imageUrl };
    } catch (error) {
      console.error('Error getting image source:', error);
      return require('@/assets/images/placeholder-item.png');
    }
  };

  // Function to render a field based on its type
  const renderFieldValue = (field: any) => {
    try {
      // Check if item is null or undefined
      if (!item) {
        return <CellText>Not available</CellText>;
      }

      // Safely access the value
      let value = item[field.key];

      // Special handling for category field
      if (field.key === 'category') {
        if (typeof value === 'object' && value !== null) {
          // If category is an object, use its name property
          value = value.name || JSON.stringify(value);
        } else if (typeof value === 'string') {
          // If it's already a string, use it as is
          value = value;
        } else if (item.categoryId) {
          // If there's a categoryId, use that
          value = item.categoryId;
        } else {
          value = 'Not specified';
        }
      }

      // Format date fields
      if (field.type === 'date' && value) {
        // Try to format the date if it's a valid date string
        try {
          // Check if it's already a formatted date string
          if (typeof value === 'string' && !value.includes('T')) {
            return (
              <DateBadge>
                <DateText>{value}</DateText>
              </DateBadge>
            );
          }

          // Try to parse and format the date
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            const formattedDate = date.toLocaleDateString('en-US', {
              day: 'numeric',
              month: 'long',
              year: 'numeric'
            });
            return (
              <DateBadge>
                <DateText>{formattedDate}</DateText>
              </DateBadge>
            );
          }
        } catch (e) {
          console.error('Error formatting date:', e);
        }
      }

      // Handle arrays
      if (Array.isArray(value)) {
        return <CellText>{value.join(', ') || 'Not specified'}</CellText>;
      }

      // Handle objects
      if (typeof value === 'object' && value !== null) {
        return <CellText>{'Object data'}</CellText>;
      }

      // Default case for text
      return <CellText numberOfLines={2}>{value || 'Not specified'}</CellText>;
    } catch (error) {
      console.error('Error rendering field value:', error);
      return <CellText>Error displaying value</CellText>;
    }
  };

  // Handle saving the item
  const handleSave = async () => {
    if (!item) {
      Alert.alert('Error', 'No item data available');
      return;
    }

    if (!item.name) {
      Alert.alert('Error', 'Item name is required');
      return;
    }

    setIsLoading(true);
    try {
      // Prepare the item data with proper fallbacks
      const itemData = {
        name: item.name,
        itemCategoryId: item.category?._id || item.categoryId || '',
        color: item.color || '',
        brand: item.brand || '',
        imageUrl: item.imageUrl || ''
      };

      console.log('Saving item with data:', itemData);

      // Save the item and get the response
      const response = await addItemMutation.mutateAsync(itemData);

      // Log the full response to see its structure
      console.log('Full response from addItem mutation:', JSON.stringify(response, null, 2));

      // Try to extract the item ID from the response
      let newItemId = null;

      if (response) {
        // The response might be the data object directly or it might contain an _id
        if (typeof response === 'string') {
          // If the response is a string, it might be the ID directly
          newItemId = response;
        } else if (response._id) {
          // If the response has an _id property
          newItemId = response._id;
        } else if (response.id) {
          // If the response has an id property
          newItemId = response.id;
        } else if (response.itemId) {
          // If the response has an itemId property
          newItemId = response.itemId;
        } else {
          // If we can't find an ID, log the response type
          console.log('Response type:', typeof response);
          console.log('Response keys:', Object.keys(response));

          // As a last resort, try to use the item's name to find it later
          console.log('Using item name as fallback:', item.name);
          // We'll handle this case in the navigation section
        }
      }

      console.log('Extracted item ID:', newItemId);

      // Log the new item ID
      if (newItemId) {
        console.log('New item ID saved:', newItemId);
      }

      // Refresh the clothes list
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Close the modal
      onClose();

      // Call the onSave callback
      onSave();

      // Navigate directly to the ClothingStats screen
      if (newItemId) {
        console.log('Navigating to ClothingStats with ID:', newItemId);
        router.push(`/(tabs)/clothing-stats/${newItemId}`);
      } else {
        // If we don't have an ID, show an error message
        console.error('Cannot navigate: No item ID available');
        Alert.alert(
          'Error',
          'Could not view item details. The item has been added to your closet.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error adding item:', error);
      Alert.alert(
        'Error',
        'Failed to add item to your closet. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      isVisible={isVisible}
      style={{ margin: 0 }}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      backdropOpacity={0.5}
      useNativeDriver={true}
      statusBarTranslucent={true}
      hideModalContentWhileAnimating={true}
      animationInTiming={300}
      animationOutTiming={300}
      backdropTransitionInTiming={300}
      backdropTransitionOutTiming={300}
    >
      <Container>
        {/* Set status bar to transparent */}
        <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />

        <Header>
          <HeaderButton onPress={onClose}>
            <HeaderButtonText color="#FF3B30">Cancel</HeaderButtonText>
          </HeaderButton>
          <HeaderTitle>Add New Clothes</HeaderTitle>
          <HeaderButton onPress={handleSave} disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator size="small" color="#0E7E61" />
            ) : (
              <HeaderButtonText>Save</HeaderButtonText>
            )}
          </HeaderButton>
        </Header>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 40 }}
          keyboardShouldPersistTaps="handled"
          bounces={true}
        >
          {/* Content starts here */}

          <ContentContainer>
            {/* Item Image */}
            <ImageContainer>
              {item ? (
                <React.Fragment>
                  {!imageError ? (
                    <React.Fragment>
                      <ItemImage
                        source={getImageSource()}
                        resizeMode="contain"
                        onLoadStart={() => {
                          setImageLoading(true);
                          setImageError(false);
                        }}
                        onLoadEnd={() => setImageLoading(false)}
                        onError={() => {
                          console.log('Image failed to load');
                          setImageLoading(false);
                          setImageError(true);
                        }}
                        defaultSource={require('@/assets/images/placeholder-item.png')}
                        style={{ width: 240, height: 240 }}
                      />
                      {imageLoading && (
                        <ImageLoadingContainer>
                          <ActivityIndicator size="large" color="#0E7E61" />
                        </ImageLoadingContainer>
                      )}
                    </React.Fragment>
                  ) : (
                    <React.Fragment>
                      <ItemImage source={require('@/assets/images/placeholder-item.png')} resizeMode="cover" />
                      <View style={{ position: 'absolute', bottom: 10, left: 0, right: 0, alignItems: 'center' }}>
                        <Text style={{ color: '#FF3B30', fontFamily: 'Mukta Vaani', fontSize: 12 }}>
                          Failed to load image
                        </Text>
                      </View>
                    </React.Fragment>
                  )}
                </React.Fragment>
              ) : (
                <React.Fragment>
                  <ItemImage source={require('@/assets/images/placeholder-item.png')} resizeMode="cover" />
                  <ImageLoadingContainer>
                    <ActivityIndicator size="large" color="#0E7E61" />
                    <Text style={{ marginTop: 10, fontFamily: 'Mukta Vaani', color: '#333333', fontSize: 12 }}>
                      Loading image...
                    </Text>
                  </ImageLoadingContainer>
                </React.Fragment>
              )}
            </ImageContainer>

            {/* Details Table */}
            <TableContainer>
              <TableTitle>Clothing Details</TableTitle>
              {item ? (
                <StatsTable>
                  {detailsFields.map((field, index) => (
                    <TableRow height={60} key={`field-${index}`} style={{ marginTop: index === 0 ? 0 : undefined }}>
                      <TableCell style={{ marginLeft: 0, width: 180 }}>
                        <CellTitle numberOfLines={0} style={{ flexWrap: 'wrap' }}>{field.label}</CellTitle>
                      </TableCell>
                      <TableCell>
                        {renderFieldValue(field)}
                      </TableCell>
                    </TableRow>
                  ))}
                </StatsTable>
              ) : (
                <LoadingContainer>
                  <ActivityIndicator size="large" color="#0E7E61" />
                  <Text style={{ marginTop: 10, fontFamily: 'Mukta Vaani', color: '#333333' }}>
                    Loading item details...
                  </Text>
                </LoadingContainer>
              )}
            </TableContainer>
          </ContentContainer>
        </ScrollView>
      </Container>
    </Modal>
  );
}
