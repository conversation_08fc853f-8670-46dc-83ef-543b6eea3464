import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import { XIcon } from 'lucide-react-native';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { TextContent, TextFooter, TextItem, TextTitle } from './style';
interface ContentItem {
  title: string;
  description: string;
  items?: Array<{ title: string; description: string }>;
  footer?: string;
}
interface LegalDocumentScreenProps {
  title: string;
  content: Array<{ title: string; description: string; items?: Array<{ title: string; description: string; }>; footer?: string }>;
  firstSection?: string;
  secondSection?: string;
  onPress?: (title: string) => void;
  modal?: boolean;
}

const isLinkTitle = (title: string | undefined) => ['privacyPolicy', 'plans'].includes(title || '');

const LegalDocumentScreen: React.FC<LegalDocumentScreenProps> = ({ title, content, firstSection, secondSection, onPress = () => { }, modal = false }) => {
  const renderPlansItem = (item: ContentItem) => {
    return (
      <TouchableOpacity onPress={() => onPress(item.title)}>
        <TextTitle style={{ color: 'blue', textDecorationLine: 'underline' }}>
          {item.description}
        </TextTitle>
      </TouchableOpacity>
    )
  }
  const renderContentItem = (item: ContentItem, index: number) => {
    return (
      <View key={`content-${index}`} style={{ marginTop: 24 }}>
        {isLinkTitle(item.title) ? (
          renderPlansItem(item)
        ) : (
          <>
            {item.title && <TextTitle>{item.title}</TextTitle>}
            {item.description && <TextItem>{item.description}</TextItem>}
          </>
        )}
        {item.items?.map((subItem, subIndex) => (
          <Text key={`sub-${index}-${subIndex}`} style={{ marginTop: 8, marginLeft: 20 }}>
            {subItem.title && <TextTitle>{subItem.title + ' '}</TextTitle>}
            {subItem.description && <TextItem>{subItem.description}</TextItem>}
          </Text>
        ))}
        {item.footer && <TextFooter>{item.footer}</TextFooter>}
      </View>
    )
  }
  return (
    <View style={{ height: '100%' }}>
      {modal ? (
        <HeaderPage noLogo disableBack rightComponent={(<XIcon size={24} onPress={() => onPress('plans')} />)} />
      ) : <HeaderPage noLogo />}
      <ScrollView showsHorizontalScrollIndicator={false} showsVerticalScrollIndicator={false}>
        <View>
          <SectionTitle title={title} />
          {firstSection && <TextContent>{firstSection}</TextContent>}
          {secondSection && <TextContent>{secondSection}</TextContent>}
          {content.map(renderContentItem)}
        </View>
      </ScrollView>
    </View>
  );
};

export default LegalDocumentScreen;
