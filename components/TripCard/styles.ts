import styled, { DefaultTheme } from 'styled-components/native';

export const TripCardContainer = styled.TouchableOpacity<{ bgColor: string }>`
  background-color: ${({
  theme,
  bgColor,
}: {
  theme: DefaultTheme;
  bgColor: string;
}) => bgColor || theme.brand.magenta};
  padding: 20px 16px;
  border-radius: 8px;
`;

export const TripCardHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
`;

export const TripCardDate = styled.Text`
  flex: 1;
  color: #fff;
  font-family: 'MuktaVaaniMedium';
  font-size: 24px;
  line-height: 32px;
`;

export const IconView = styled.View`
  background-color: #fff;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
`;

export const TripCardTitleView = styled.View`
  flex-direction: row;
  gap: 8px;
  flex-wrap: nowrap;
  width: 100%;
  align-items: center;
`;

export const TripCardTitleText = styled.Text`
  font-family: 'MuktaVaaniBold';
  flex-shrink: 1;
  min-width: 0;
  color: #fff;
  font-size: 24px;
`;

export const TripCardDuration = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  color: #fff;
  font-size: 16px;
`;

export const TripCardLocationView = styled.View`
  flex-direction: row;
  gap: 6px;
  align-items: center;
  margin-top: 6px;
`;

export const TripCardLocationText = styled.Text`
  color: #fff;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 14px;
`;

export const TripCardFooter = styled.View`
  flex: 1;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  margin-top: 8px;
`;

export const FooterText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 12px;
  line-height: 16px;
`;

export const FooterTextBold = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 12px;
  line-height: 16px;
`;