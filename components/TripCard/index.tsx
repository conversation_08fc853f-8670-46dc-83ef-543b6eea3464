import GlobeIcon from '@/assets/svg/globe-icon.svg';
import LuggageSvg from '@/assets/svg/luggage.svg';
import TrashSvg from '@/assets/svg/trash.svg';
import { deleteTrip } from '@/methods/trips';
import { Destination } from '@/types/trip';
import {
  formatDestinations,
  formatDuration,
  formatTripDates,
  getTripDates,
} from '@/utils/tripHelpers';
import { router, useFocusEffect } from 'expo-router';
import { useCallback, useRef } from 'react';
import { ActivityIndicator, Alert, Image, TouchableOpacity, View } from 'react-native';
import { useTheme } from 'styled-components';
import {
  FooterText,
  FooterTextBold,
  IconView,
  TripCardContainer,
  TripCardDate,
  TripCardDuration,
  TripCardFooter,
  TripCardHeader,
  TripCardLocationText,
  TripCardLocationView,
  TripCardTitleText,
  TripCardTitleView,
} from './styles';

type Trip = {
  _id: string;
  name: string;
  destinations: Destination[];
  cardColor: string;
}

interface TripCardProps {
  trip?: Trip;
  showFooter?: boolean;
  notClickable?: boolean;
  packingListItems?: any[];
  isDeletable?: boolean;
  onDelete?: () => void;
  isLoading?: boolean;
}

const FLAGS_API = 'https://flagcdn.com/32x24/';

const getFlagUrl = (countryCode: string) => {
  return `${FLAGS_API}${countryCode.toLowerCase()}.png`;
};

export default function TripCard({
  trip,
  packingListItems = [],
  showFooter = true,
  notClickable = false,
  isDeletable = false,
  onDelete,
  isLoading = false,
}: TripCardProps) {
  const theme = useTheme();

  const { mutate: deleteCurrentTrip } = deleteTrip();
  const isNavigating = useRef(false);

  useFocusEffect(useCallback(() => {
    isNavigating.current = false;
  }, []));

  const countryCodes = Array.from(
    new Set(trip?.destinations?.map((destination: any) => destination.countryCode)),
  ).filter(Boolean);

  const renderFlagOrGlobe = () => {
    if (!countryCodes || countryCodes.length === 0) return;
    if (!countryCodes[0]) return;
    const flagUrl = getFlagUrl(countryCodes[0]);

    if (countryCodes.length > 1) {
      return <GlobeIcon width={20} height={20} />;
    }

    return (
      <Image
        source={{ uri: flagUrl }}
        style={{ width: 16, height: 16 }}
        resizeMode="contain"
      />
    );
  };

  let textColor = '#fff';
  // Use dark textColor for every third card
  if (trip?.cardColor === theme?.brand.skyBlue) {
    textColor = '#242424';
  }

  const date = formatTripDates(trip?.destinations || []);

  const tripDates = getTripDates(trip?.destinations || []);
  const duration = tripDates.startDate && tripDates.endDate
    ? formatDuration(tripDates.startDate, tripDates.endDate)
    : '';

  const destination = formatDestinations(trip?.destinations || []);

  const handleTripOverview = useCallback(() => {
    if (notClickable) return;
    if (!trip?._id) return;
    if (isNavigating.current) return;
    isNavigating.current = true;
    router.push(`/trip-overview/${trip?._id}`);
  }, [notClickable, trip?._id, isNavigating]);

  const extractCountOfItemsPacked = useCallback(() => {
    console.log(packingListItems, 'packingListItems');

    let itemCount = 0;

    packingListItems.forEach((item) => {
      console.log(JSON.stringify(item.items, null, 2), item.items.length);
      itemCount += item.items.length;
    });

    return itemCount;
  }, [packingListItems]);

  //get the count days before the trip starts
  const countDaysBeforeTripStarts = useCallback(() => {
    const startDate = trip?.destinations?.[0]?.startDate
      ? new Date(trip?.destinations?.[0].startDate)
      : new Date();
    const today = new Date();
    const diffTime = Math.abs(startDate.getTime() - today.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }, [trip?.destinations]);

  const handleDeleteTrip = useCallback(() => {
    if (!trip?._id) return;

    Alert.alert('Delete Trip', 'Are you sure you want to delete this trip?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: () => {
          if (!trip?._id) return;
          deleteCurrentTrip(trip?._id, {
            onSuccess: () => {
              onDelete?.();
            },
          });
        },
      },
    ]);
  }, [deleteCurrentTrip, trip?._id, onDelete]);

  return (
    <TripCardContainer
      bgColor={trip?.cardColor}
      activeOpacity={!notClickable ? 0.8 : 1}
      onPress={handleTripOverview}
    >
      {isLoading ? (
        <View style={{ height: 109, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      ) : (
        <>
          <TripCardHeader>
            <TripCardDate style={{ color: textColor }}>{date}</TripCardDate>
            <View style={{ flexDirection: 'row', gap: 8, paddingLeft: 10 }}>
              <IconView>
                <LuggageSvg width={20} height={20} color={packingListItems.length > 0 ? theme?.brand.green[500] : "#808080"} />
              </IconView>
              {isDeletable && (
                <TouchableOpacity onPress={handleDeleteTrip} activeOpacity={0.8}>
                  <IconView>
                    <TrashSvg height={18} color={theme?.brand.red} />
                  </IconView>
                </TouchableOpacity>
              )}
            </View>
          </TripCardHeader>
          <TripCardTitleView>
            <TripCardTitleText style={{ color: textColor }} numberOfLines={1}>
              {trip?.name}
            </TripCardTitleText>
            {duration && (
              <View>
                <TripCardDuration style={{ color: textColor }}>
                  ({duration})
                </TripCardDuration>
              </View>
            )}
          </TripCardTitleView>
          <TripCardLocationView>
            {renderFlagOrGlobe()}
            <TripCardLocationText style={{ color: textColor }}>
              {destination}
            </TripCardLocationText>
          </TripCardLocationView>
          {showFooter && (
            <TripCardFooter>
              {countDaysBeforeTripStarts() > 0 && (
                <View style={{ flex: 1 }}>
                  <FooterText style={{ color: textColor }}>
                    Trip starts in{'\n'}
                    <FooterTextBold>
                      {countDaysBeforeTripStarts()} days
                    </FooterTextBold>
                  </FooterText>
                </View>
              )}
              <View
                style={{
                  width: 1,
                  backgroundColor: textColor,
                  marginHorizontal: 10,
                }}
              />
              <View style={{ flex: 1 }}>
                <FooterText style={{ color: textColor }}>
                  Items Packed:{'\n'}
                  <FooterTextBold>{extractCountOfItemsPacked()}</FooterTextBold>
                </FooterText>
              </View>
              <View
                style={{
                  width: 1,
                  backgroundColor: textColor,
                  marginHorizontal: 10,
                }}
              />
              <View style={{ flex: 1 }}>
                <FooterText style={{ color: textColor }}>
                  Total Weight:{'\n'}
                  <FooterTextBold>20 kg</FooterTextBold>
                </FooterText>
              </View>
            </TripCardFooter>
          )}
        </>
      )}
    </TripCardContainer>
  );
}
