import { DefaultTheme } from 'styled-components';
import styled from 'styled-components/native';

export const OverviewItemContainer = styled.TouchableOpacity<{ isItem: boolean }>`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: ${({ isItem }: { isItem: boolean }) => isItem ? '40px' : '30px'};
  margin-bottom: 10px;
  ${({ isItem }: { isItem: boolean }) => !isItem && 'margin-bottom: 0px;'}
`;

export const OverviewItemText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
`;

export const OverviewItemTextBold = styled.Text`
  font-family: 'MuktaVaani-Bold';
  font-size: 16px;
`;

export const ItemCountText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 16px;
`;

export const QuantityText = styled.Text`
  font-family: 'MuktaVaani-Regular';
  font-size: 16px;
`;

export const CircleIconContainer = styled.TouchableOpacity`
  width: 20px;
  height: 20px;
  border-radius: 10px;
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  align-items: center;
  justify-content: center;
`;

export const BrandColorText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 13px;
  font-weight: 400;
  color: gray;
`;

export const AddItemContainer = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 10px;
`;

export const ContentItemButton = styled.TouchableOpacity`
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 50px;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  padding: 4px 8px;
`;

export const ContentItemButtonText = styled.Text`
  font-family: 'MuktaVaani';
  font-size: 12px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[50]};
`;

export const RecommendationsModal = styled.View`
margin-left: 5%;
margin-right: 5%;
background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[50]};
padding: 20px;
border-radius: 12px;
`;

export const RecommendationsModalCloseButton = styled.TouchableOpacity`
background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
border-radius: 50px;
align-items: center;
justify-content: center;
width: 30px;
height: 30px;
align-self: flex-end;
`;

export const ModalCloseButtonText = styled.Text`
font-family: 'MuktaVaaniSemiBold';
font-size: 16px;
color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[50]};
`;

export const ModalViewText = styled.View`
align-items: center;
justify-content: center;
`;

export const ModalViewTextTitle = styled.Text`
font-size: 24px;
font-family: 'MuktaVaaniSemiBold';
color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[900]};
`;

export const ModalViewTextDescription = styled.Text`
font-size: 14px;
font-family: 'MuktaVaaniLight';
color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[900]};
text-align: center;
`;

export const ModalViewTextButton = styled.TouchableOpacity`
background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
border-radius: 50px;
align-items: center;
justify-content: center;
width: 100%;
padding: 10px;
margin-top: 10px;
`;

export const ModalViewTextButtonText = styled.Text`
font-size: 16px;
font-family: 'MuktaVaaniMedium';
color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[50]};
`;
