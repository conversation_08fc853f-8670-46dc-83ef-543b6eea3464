import React, { ReactNode } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.5; // Aspect ratio of 2:3

interface CanvasProps {
  children: ReactNode;
  showGrid?: boolean;
}

export const Canvas = ({ children, showGrid = true }: CanvasProps) => {
  return (
    <View style={styles.canvasContainer}>
      <View style={styles.canvas}>
        {showGrid && (
          <>
            {/* Vertical grid lines */}
            <View style={[styles.gridLine, styles.verticalCenter]} />
            <View style={[styles.gridLine, styles.verticalLeft]} />
            <View style={[styles.gridLine, styles.verticalRight]} />

            {/* Horizontal grid lines */}
            <View style={[styles.gridLine, styles.horizontalCenter]} />
            <View style={[styles.gridLine, styles.horizontalTop]} />
            <View style={[styles.gridLine, styles.horizontalBottom]} />
          </>
        )}
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  canvasContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  canvas: {
    width: CANVAS_WIDTH,
    height: CANVAS_HEIGHT - 100,
    backgroundColor: '#f5f5f5',
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 0, 0.5)', // Yellow with opacity
  },
  verticalCenter: {
    width: 1,
    height: '100%',
    left: '50%',
  },
  verticalLeft: {
    width: 1,
    height: '100%',
    left: '33%',
    borderStyle: 'dashed',
  },
  verticalRight: {
    width: 1,
    height: '100%',
    left: '67%',
    borderStyle: 'dashed',
  },
  horizontalCenter: {
    width: '100%',
    height: 1,
    top: '50%',
  },
  horizontalTop: {
    width: '100%',
    height: 1,
    top: '33%',
    borderStyle: 'dashed',
  },
  horizontalBottom: {
    width: '100%',
    height: 1,
    top: '67%',
    borderStyle: 'dashed',
  },
});