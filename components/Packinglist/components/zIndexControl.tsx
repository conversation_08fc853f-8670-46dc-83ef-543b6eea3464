import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
const ZIndexControls = ({ onBringForward, onSendBackward, onDelete, disabled }) => {
  return (
    <View style={styles.zIndexControls}>
      <TouchableOpacity 
        style={[styles.zIndexButton, disabled && styles.disabledButton]} 
        onPress={onBringForward}
        disabled={disabled}
      >
        <Text style={styles.zIndexButtonText}>Bring Forward</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        style={[styles.zIndexButton, disabled && styles.disabledButton]} 
        onPress={onSendBackward}
        disabled={disabled}
      >
        <Text style={styles.zIndexButtonText}>Send Backward</Text>
      </TouchableOpacity>
      {/* add delete button */}
      <TouchableOpacity 
        style={[styles.zIndexButton, disabled && styles.disabledButton]} 
        onPress={onDelete}
        disabled={disabled}
      >
        <Text style={styles.zIndexButtonText}>Delete</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ZIndexControls;



const styles = StyleSheet.create({
  zIndexControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: 10,
    marginBottom: 10,
  },
  zIndexButton: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 20,
  },
  zIndexButtonText: {
    color: '#000',
    fontWeight: 'light',
  },
  disabledButton: {
    opacity: 0.5,
  },
  disabledButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});


