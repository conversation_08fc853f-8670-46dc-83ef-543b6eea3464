import { PlusIcon } from 'lucide-react-native';
import { useEffect, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';
import {
  HeaderText,
  TemplateModalHeader,
  TemplateModalTitle
} from '../TemplateListModal/styles';
import Button from '../common/Button';
import { InputGhost } from '../common/Input';
import { styleDiary } from '@/constants/strings';

export const StyledDiaryPlanOutfitModal = ({
  isVisible,
  onClose,
  eventId,
  handlePlanOutfit,
  activitiesEventsList,
  setActivitiesEventsList,
  selectedEvent,
  activeStyleDiary
}: {
  isVisible: boolean;
  onClose: () => void;
  eventId: string;
  handlePlanOutfit: (event: any) => void;
  activitiesEventsList: any[];
  setActivitiesEventsList: (activitiesEvents: any[]) => void;
  selectedEvent: any | null;
  activeStyleDiary?: any;
}) => {

  const suggestions = [
    {
      id: 1,
      name: 'Workout'
    },
    {
      id: 2,
      name: 'Dinner Anniversary'
    },
    {
      id: 3,
      name: "Friend's Wedding"
    },
    {
      id: 4,
      name: 'Beach Picnic'
    },
    {
      id: 5,
      name: 'Pool Party'
    },
  ]
  const [newEvent, setNewEvent] = useState('');
  useEffect(() => {
    if (selectedEvent) {
      setNewEvent(selectedEvent.name);
    }
  }, [selectedEvent]);

  const handleSaveEvent = (type: string) => {
    if (!newEvent) return onClose();
    if (activitiesEventsList?.find((event) => event.name === selectedEvent?.name)) {
      const currentEvent = activitiesEventsList?.find((event) => event.name === selectedEvent?.name);
      const updatedEvent = {
        id: currentEvent?.id,
        name: newEvent,
        eventId: currentEvent?.eventId,
        image: currentEvent?.image,
      }
      setActivitiesEventsList(activitiesEventsList.map((event) => event.id === currentEvent?.id ? updatedEvent : event));
      setNewEvent('');
      onClose();
      return;
    }
    const event = {
      id: activeStyleDiary?._id,
      name: newEvent,
      eventId,
      image: null,
    }
    setActivitiesEventsList([...activitiesEventsList, event]);
    if (type === 'save') {
      setNewEvent('');
      onClose();
      return;
    }
    else {
      handlePlanOutfit(event);
      return;
    }
  }


  return (
    <Modal
      style={{ flex: 1, justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <View style={styles.container}>
        <TemplateModalHeader>
          <TouchableOpacity onPress={onClose}>
            <HeaderText>{styleDiary.cancel}</HeaderText>
          </TouchableOpacity>
          <TemplateModalTitle>{styleDiary.planAnOutfit}</TemplateModalTitle>
          {suggestions.length > 0 ? <TouchableOpacity disabled={newEvent === ''} onPress={() => handleSaveEvent('save')}>
            <HeaderText>{styleDiary.save}</HeaderText>
          </TouchableOpacity> : <View style={{ width: 50 }} />}
        </TemplateModalHeader>

        <View style={styles.content}>
          {suggestions.map((suggestion: any) => (
            <TouchableOpacity key={suggestion.id} style={{
              ...styles.suggestionItem,
              backgroundColor: newEvent === suggestion.name ? '#0E7E61' : 'transparent',
            }} onPress={() => setNewEvent(suggestion.name)}>
              <PlusIcon size={20} color={newEvent === suggestion.name ? '#FFFFFF' : '#0E7E61'} />
              <Text style={{
                color: newEvent === suggestion.name ? '#FFFFFF' : '#0E7E61',
                fontFamily: 'MuktaVaani-Medium',
              }}>
                {suggestion.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>{styleDiary.activityEvent}</Text>
          <InputGhost
            placeholder={styleDiary.imPlanningForA}
            onChangeText={(text) => setNewEvent(text)}
            value={newEvent}
            style={{ width: '100%' }}
          />
        </View>
        <View style={{ justifyContent: 'flex-end', alignItems: 'center', marginTop: 20 }}>
          <Button title={newEvent === '' ? styleDiary.skipAndPlanOutfit : styleDiary.planOutfit} style={{ marginBottom: 10 }} onPress={() => handleSaveEvent('plan')} />
          <Button isDisabled={newEvent === ''} title={styleDiary.saveEvent} isLined onPress={() => handleSaveEvent('save')} />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#F0F0F0',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  suggestionItem: {
    flexDirection: 'row',
    marginTop: 10,
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderWidth: 0.3,
    borderRadius: 50,
    borderColor: '#0E7E61',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderWidth: 0.3,
    borderRadius: 13
  },
  inputLabel: {
    marginRight: 10,
    fontFamily: 'MuktaVaani-Medium',
  }
});