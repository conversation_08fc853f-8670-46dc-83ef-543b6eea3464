import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import { makeImageFromView } from '@shopify/react-native-skia';
import Tshirt from '@/assets/images/tshirt.png';
import Pants from '@/assets/images/pants.png';
import Shoes from '@/assets/images/shoes.png';
import { getPackingList } from '@/methods/trips';

import { Canvas } from './components/canvas';
import { DraggableResizeableItem } from './components/dragableItem';
import ZIndexControls from './components/zIndexControl';

const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.5; // Aspect ratio of 2:3

/**
 * Combined DraggableResizeableItem component that handles both dragging and resizing
 */

/**
 * ItemSelector component for selecting items to add to the canvas
 */
const ItemSelector = ({ items, onSelectItem }) => {
  return (
    <View style={styles.itemSelectorContainer}>
      <Text style={styles.sectionTitle}>Add Items</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        style={styles.itemScroll}
      >
        {items.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.itemOption}
            onPress={() => onSelectItem(item)}
          >
            <Image
              source={{ uri: item.source }}
              style={styles.itemThumbnail}
              resizeMode="contain"
            />
            <Text style={styles.itemLabel}>{item.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

export const StyledDiary = ({ eventId }: { eventId: string }) => {
  const { data: packingList } = getPackingList(eventId);
  const [availableItems, setAvailableItems] = useState([]);

  console.log('packingList', packingList?.data);

  useEffect(() => {
    if (packingList?.data?.packingList?.length > 0) {
      const Items = [];

      packingList?.data?.packingList.forEach((item) => {
        item.items.forEach((item) => {
          console.log('item-----', item);
          Items.push({
            id: item._id,
            name: item.name,
            source: item.imageUrl,
            type: item.type,
          });
        });
      });

      setAvailableItems([...Items]);
    }
  }, [packingList]);

  // State for items on the canvas
  const [canvasItems, setCanvasItems] = useState([]);
  // State for selected item
  const [selectedItemIndex, setSelectedItemIndex] = useState(null);
  // Reference to the canvas for capturing as image
  const canvasRef = useRef(null);

  const captureView = async (canvas) => {
    try {
      // Use Skia's makeImageFromView to capture the view
      console.log('canvasRef.current', canvasRef.current);
      const image = await makeImageFromView(canvas);
      console.log('image', image?.encodeToBase64());

      // console.log('image', image);
      // Save the image to a temporary file
      // const filePath = `${FileSystem.cacheDirectory}canvas_${Date.now()}.png`;
      // await image.save(filePath, { format: 'png' });

      // return filePath;
    } catch (error) {
      console.error('Error capturing view with Skia:', error);
      throw error;
    }
  };

  const highestZIndex = canvasItems.reduce(
    (max, item) => Math.max(max, item.zIndex || 1),
    0,
  );

  // Add item to canvas
  const handleAddItem = (item) => {
    const newItem = {
      id: Date.now().toString(),
      source: item.source,
      x: 50, // Initial position
      y: 50,
      width: 100, // Initial size
      height: 100,
      type: item.type,
      zIndex: highestZIndex + 1, // Place on top
    };

    setCanvasItems([...canvasItems, newItem]);
    // Select the newly added item
    setSelectedItemIndex(canvasItems.length);
  };

  // Update item position and size
  const handleItemChange = (index, changes) => {
    const updatedItems = [...canvasItems];
    updatedItems[index] = {
      ...updatedItems[index],
      x: changes.x !== undefined ? changes.x : updatedItems[index].x,
      y: changes.y !== undefined ? changes.y : updatedItems[index].y,
      width:
        changes.width !== undefined ? changes.width : updatedItems[index].width,
      height:
        changes.height !== undefined
          ? changes.height
          : updatedItems[index].height,
    };
    setCanvasItems(updatedItems);
  };

  // Delete selected item
  const handleDeleteItem = () => {
    if (selectedItemIndex !== null) {
      const updatedItems = canvasItems.filter(
        (_, index) => index !== selectedItemIndex,
      );
      setCanvasItems(updatedItems);
      setSelectedItemIndex(null);
    }
  };

  const handleBringForward = () => {
    if (selectedItemIndex === null) return;

    const updatedItems = [...canvasItems];
    const currentItem = updatedItems[selectedItemIndex];

    // Find the item with the next highest z-index
    const itemsAbove = updatedItems.filter(
      (item, index) =>
        index !== selectedItemIndex && item.zIndex > currentItem.zIndex,
    );

    if (itemsAbove.length === 0) return; // Already at the top

    // Sort by z-index to find the next one up
    itemsAbove.sort((a, b) => a.zIndex - b.zIndex);
    const nextItemUp = itemsAbove[0];

    // Swap z-indices
    const tempZIndex = currentItem.zIndex;
    updatedItems[selectedItemIndex].zIndex = nextItemUp.zIndex;

    // Find the index of the next item up
    const nextItemIndex = updatedItems.findIndex(
      (item) => item.id === nextItemUp.id,
    );
    updatedItems[nextItemIndex].zIndex = tempZIndex;

    setCanvasItems(updatedItems);
  };

  // Send the selected item backward (decrease z-index)
  const handleSendBackward = () => {
    if (selectedItemIndex === null) return;

    const updatedItems = [...canvasItems];
    const currentItem = updatedItems[selectedItemIndex];

    // Find the item with the next lowest z-index
    const itemsBelow = updatedItems.filter(
      (item, index) =>
        index !== selectedItemIndex && item.zIndex < currentItem.zIndex,
    );

    if (itemsBelow.length === 0) return; // Already at the bottom

    // Sort by z-index in descending order to find the next one down
    itemsBelow.sort((a, b) => b.zIndex - a.zIndex);
    const nextItemDown = itemsBelow[0];

    // Swap z-indices
    const tempZIndex = currentItem.zIndex;
    updatedItems[selectedItemIndex].zIndex = nextItemDown.zIndex;

    // Find the index of the next item down
    const nextItemIndex = updatedItems.findIndex(
      (item) => item.id === nextItemDown.id,
    );
    updatedItems[nextItemIndex].zIndex = tempZIndex;

    setCanvasItems(updatedItems);
  };

  const sortedCanvasItems = [...canvasItems].sort(
    (a, b) => a.zIndex - b.zIndex,
  );

  return (
    <View style={styles.container}>
      <ItemSelector items={availableItems} onSelectItem={handleAddItem} />
      <View style={styles.actionBar}>
        <TouchableOpacity style={styles.actionButton}>
          <Text style={styles.actionButtonText}>←</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={() => captureView(canvasRef)}
        >
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Text style={styles.actionButtonText}>↻</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.actionButton,
            selectedItemIndex === null && styles.disabledButton,
          ]}
          onPress={handleDeleteItem}
          disabled={selectedItemIndex === null}
        >
          <Text style={styles.actionButtonText}>🗑️</Text>
        </TouchableOpacity>
      </View>
      <ZIndexControls
        onBringForward={handleBringForward}
        onSendBackward={handleSendBackward}
        disabled={selectedItemIndex === null}
      />
      <View ref={canvasRef} collapsable={false}>
        <Canvas showGrid={true}>
          {sortedCanvasItems.map((item, index) => (
            <DraggableResizeableItem
              key={item.id}
              source={item.source}
              initialX={item.x}
              initialY={item.y}
              initialWidth={item.width}
              initialHeight={item.height}
              isSelected={selectedItemIndex === index}
              onSelect={() => setSelectedItemIndex(index)}
              onPositionChange={(position) => handleItemChange(index, position)}
              onSizeChange={(changes) => handleItemChange(index, changes)}
            />
          ))}
        </Canvas>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // App styles
  appContainer: {
    backgroundColor: '#fff',
  },

  // StyledDiary styles
  container: {
    marginTop: 20,
  },
  header: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#000',
  },
  tabText: {
    fontSize: 16,
    color: '#888',
  },
  activeTabText: {
    color: '#000',
    fontWeight: 'bold',
  },

  // Canvas styles
  canvasContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  canvas: {
    width: CANVAS_WIDTH,
    height: CANVAS_HEIGHT - 100,
    backgroundColor: '#f5f5f5',
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 0, 0.5)', // Yellow with opacity
  },
  verticalCenter: {
    width: 1,
    height: '100%',
    left: '50%',
  },
  verticalLeft: {
    width: 1,
    height: '100%',
    left: '33%',
    borderStyle: 'dashed',
  },
  verticalRight: {
    width: 1,
    height: '100%',
    left: '67%',
    borderStyle: 'dashed',
  },
  horizontalCenter: {
    width: '100%',
    height: 1,
    top: '50%',
  },
  horizontalTop: {
    width: '100%',
    height: 1,
    top: '33%',
    borderStyle: 'dashed',
  },
  horizontalBottom: {
    width: '100%',
    height: 1,
    top: '67%',
    borderStyle: 'dashed',
  },

  // Combined DraggableResizeableItem styles
  draggableResizeableContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dragArea: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  selected: {
    borderWidth: 2,
    borderColor: '#00ff00',
    borderStyle: 'dashed',
  },
  handle: {
    width: 20,
    height: 20,
    backgroundColor: 'rgba(0, 255, 0, 0.5)',
    borderRadius: 10,
    zIndex: 10,
  },

  // Action bar styles
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 18,
  },
  saveButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#ccff00',
    borderRadius: 20,
  },
  saveButtonText: {
    color: '#000',
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },

  // ItemSelector styles
  itemSelectorContainer: {
    padding: 15,
    backgroundColor: '#000',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  itemScroll: {
    flexDirection: 'row',
  },
  itemOption: {
    marginRight: 15,
    alignItems: 'center',
  },
  itemThumbnail: {
    width: 60,
    height: 60,
    backgroundColor: '#fff',
    borderRadius: 5,
  },
  itemLabel: {
    color: '#fff',
    marginTop: 5,
    fontSize: 12,
  },

  // Bottom tabs styles
  bottomTabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  bottomTab: {
    alignItems: 'center',
  },
  bottomTabText: {
    fontSize: 24,
  },
  activeBottomTab: {
    transform: [{ scale: 1.2 }],
  },
  activeBottomTabText: {
    color: '#000',
  },
  bottomTabLabel: {
    fontSize: 12,
    marginTop: 5,
  },
});
