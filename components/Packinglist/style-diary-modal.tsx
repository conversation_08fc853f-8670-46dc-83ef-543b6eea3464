import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';
import { StyledDiary } from './style-diary-add-modal';
import {
  TemplateModalContainer,
  TemplateModalHeader,
  TemplateModalTitle,
  HeaderText,
} from '../TemplateListModal/styles';

interface EventActivity {
  name: string;
  eventId: string;
  id: string;
  image: string;
}
interface StyledDiaryModalProps {
  isVisible: boolean;
  onClose: () => void;
  eventId: string;
  activeStyleDiary: any;
  selectedEventActivity?: EventActivity;
  tripName?: string;
}

export default function StyledDiaryModal({ isVisible, onClose, eventId, activeStyleDiary, selectedEventActivity, tripName }: StyledDiaryModalProps) {

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <TemplateModalContainer style={{ height: '100%', }}>
        <TemplateModalHeader style={{ marginTop: 50 }}>
          <TouchableOpacity onPress={onClose}>
            <HeaderText>Close</HeaderText>
          </TouchableOpacity>
          <TemplateModalTitle>Styling: {tripName}</TemplateModalTitle>
          <View style={{ width: 50 }} />
        </TemplateModalHeader>
        <StyledDiary
          tripName={tripName}
          eventId={eventId}
          activeStyleDiary={activeStyleDiary}
          onClose={onClose}
          selectedEventActivity={selectedEventActivity?.name || ''} />
      </TemplateModalContainer>
    </Modal>
  );
}