import React from 'react';
import { View, Text } from 'react-native';
import { EmptyStateContainer, EmptyStateTitle } from './styles';
import TravelSvg from '@/assets/svg/travel.svg';
import Button from '../common/Button';

export const EmptyState = ({ onPress, onPressTemplate }: { onPress: () => void, onPressTemplate: () => void }) => {
  return <EmptyStateContainer>
    <EmptyStateTitle>Create a Packing List</EmptyStateTitle>
    <TravelSvg />
    <Button title="Create a Packing List" onPress={onPress} />
    <Button isLined title="Use a template" onPress={onPressTemplate} />
  </EmptyStateContainer>;
};
