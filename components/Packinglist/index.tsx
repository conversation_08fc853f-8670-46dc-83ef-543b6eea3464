import { useSyncPackingListWithCloset } from "@/methods/sync-utils";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Safe<PERSON>reaVie<PERSON>,
  ScrollView,
  View
} from "react-native";

import { useSession } from "@/config/ctx";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "expo-router";
import { useCallback, useEffect, useMemo, useState } from "react";

import TitleHeader from "@/components/common/TitleHeader";
import OverviewItem from "@/components/OverviewItem";
import { getUserProfile } from "@/methods/users";

import AddClothsCategoryModal from "@/components/AddClothsCategoryModal";
import AddClothsModal from "@/components/AddClothsModal";
import Button from "@/components/common/Button";

// Import DraggableFlatList
import AddCategoryModal from "@/components/AddCategory";
import AddItemsModal from "@/components/AddItems";
import { SubtitleText } from "@/components/common/Text";
import EditClothsModal from "@/components/EditClothsModal";
import { OverviewItemContainer, OverviewItemText } from "@/components/OverviewItem/styles";
import { EmptyState } from "@/components/Packinglist/emptyState";
import {
  getPackingList,
  getTripById,
  updatePackingList,
} from "@/methods/trips";
import { PlusIcon } from "lucide-react-native";
import uuid from "react-native-uuid";
import RecommendationModal from '../RecommendationModal';
import TemplateListModal from "../TemplateListModal";

interface ClothesItem {
  _id: string;
  name: string;
  isActive: boolean;
  type: "clothes" | "category";
  quantity?: number;
  note?: string;
  color?: string;
  brand?: string;
  imageUrl?: string;
}

interface PackingList {
  _id: string;
  name: string;
  isActive: boolean;
  type: "clothes" | "category";
  quantity?: number;
  showSubItems?: boolean;
  items?: ClothesItem[];
}

interface CategoryObject {
  _id: string;
  name: string;
}

interface PackingListProps {
  id: string;
}

interface TemplateItem {
  id: string;
  name: string;
  quantity?: number;
  isChecked?: boolean;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  image: string;
  items: TemplateItem[];
}

interface MyTemplateItem {
  _id?: string;
  name: string;
  items: ClothesItem[];
}

export const PackingList = ({ id }: PackingListProps) => {
  const { signOut } = useSession();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: userProfile, isLoading } = getUserProfile();
  const { data: trip } = getTripById(id as string);

  // State management
  const [isTemplateListModalVisible, setIsTemplateListModalVisible] =
    useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null
  );
  const [isAddCategoryModalVisible, setIsAddCategoryModalVisible] =
    useState(false);
  const [packingList, setPackingList] = useState<PackingList[]>([]);
  const [isAddClothsModalVisible, setIsAddClothsModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<PackingList | null>(null);
  const [categoryName, setCategoryName] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showEmptyState, setShowEmptyState] = useState(true);
  const [isEditClothsModalVisible, setIsEditClothsModalVisible] =
    useState(false);
  const [selectedCloth, setSelectedCloth] = useState<ClothesItem | null>(null);
  const [isAddItemsModalVisible, setIsAddItemsModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState("Packing List");
  const [isMyUseRecommendationsModalVisible, setIsMyUseRecommendationsModalVisible] = useState(false);
  const [showRecommendations, setShowRecommendations] = useState(false);

  const { data: packingLists } = getPackingList(id as string);
  const { mutate: updatePackingListMutation } = updatePackingList();
  const {
    invalidateClosetCache,
    removeItemFromPackingListMutation,
    forceRefreshAllCaches,
    hardRefresh,
  } = useSyncPackingListWithCloset();

  // Enhanced authentication-aware update function
  const updatePackingListWithAuth = useCallback(
    (eventId: string, packingList: any) => {
      updatePackingListMutation(
        { eventId, packingList },
        {
          onError: (error: Error) => {
            console.error("Failed to update packing list:", error);

            // Handle authentication errors
            if (
              error.message.includes("Authentication") ||
              error.message.includes("unauthorized") ||
              error.message.includes("User not found")
            ) {
              Alert.alert(
                "Session Expired",
                "Your session has expired. Please sign in again.",
                [
                  {
                    text: "Sign In",
                    onPress: () => {
                      signOut();
                      router.replace("/sign-in");
                    },
                  },
                ]
              );
            } else {
              Alert.alert(
                "Update Failed",
                "Failed to update packing list. Please try again.",
                [
                  {
                    text: "Retry",
                    onPress: () =>
                      updatePackingListMutation({ eventId, packingList }),
                  },
                  {
                    text: "Cancel",
                    style: "cancel",
                  },
                ]
              );
            }
          },
        }
      );
    },
    [updatePackingListMutation, signOut, router]
  );

  // Enhanced template selection with better error handling
  const handleSelectTemplate = useCallback(
    (template: Template) => {
      try {
        // Check if the template is already in the packing list
        const templateExists = packingList.some(
          (item) => item.name === template.name
        );
        if (templateExists) {
          Alert.alert(
            "Template already exists",
            "This template has already been added to your packing list."
          );
          return;
        }

        const newTemplate: PackingList = {
          _id: String(uuid.v4()),
          name: template.name,
          isActive: false,
          type: "category",
          showSubItems: true,
          items: template.items.map((item: TemplateItem) => ({
            _id: String(uuid.v4()),
            name: item.name,
            quantity: item.quantity || 1,
            isActive: false,
            type: "clothes" as const,
          })),
        };

        const newPackingList = [...packingList, newTemplate];

        updatePackingListWithAuth(id as string, newPackingList);

        setPackingList(newPackingList);
        setIsTemplateListModalVisible(false);
        setShowEmptyState(false);
      } catch (error) {
        console.error("Error adding template:", error);
        Alert.alert("Error", "Failed to add template. Please try again.");
      }
    },
    [packingList, id, updatePackingListMutation]
  );

  // Enhanced effect with better data handling
  useEffect(() => {
    if (packingLists?.data?.packingList) {
      const packingListData = packingLists.data.packingList;

      if (Array.isArray(packingListData) && packingListData.length > 0) {
        setPackingList([...packingListData]);
        setShowEmptyState(false);
      } else {
        setPackingList([]);
        setShowEmptyState(true);
      }
    } else {
      setPackingList([]);
      setShowEmptyState(true);
    }
  }, [packingLists]);

  // Memoized calculations with proper typing
  const memoizedPackingList = useMemo(() => packingList, [packingList]);

  const allItemCount = useMemo(() => {
    return memoizedPackingList.reduce((acc, item) => {
      if (item?.type === "category" && item.items) {
        const activeItemsCount = item.items.filter(
          (subItem) => subItem.isActive
        ).length;
        return acc + activeItemsCount;
      }

      return acc + (item.isActive ? 1 : 0);
    }, 0);
  }, [memoizedPackingList]);

  const countOnAllTotalItems = useMemo(() => {
    return memoizedPackingList.reduce((acc, item) => {
      if (item?.type === "category") {
        return acc + (item.items?.length || 0);
      }
      return acc + 1;
    }, 0);
  }, [memoizedPackingList]);

  const getallClothItemsId = useMemo(() => {
    return memoizedPackingList.reduce((acc: string[], item) => {
      if (item?.type === "category" && item.items) {
        return acc.concat(item.items.map((subItem) => subItem._id));
      }
      return acc;
    }, []);
  }, [memoizedPackingList]);

  // Enhanced category creation with better validation
  const addDefaultCategory = useCallback(() => {
    try {
      const clothesCategory: PackingList = {
        _id: String(uuid.v4()),
        name: "Clothes",
        isActive: false,
        type: "category",
        showSubItems: true,
        items: [],
      };

      const shoesCategory: PackingList = {
        _id: String(uuid.v4()),
        name: "Shoes",
        isActive: false,
        type: "category",
        showSubItems: true,
        items: [],
      };

      const defaultCategories = [clothesCategory, shoesCategory];

      updatePackingListWithAuth(id as string, defaultCategories);

      setPackingList(defaultCategories);
      setShowEmptyState(false);
    } catch (error) {
      console.error("Error adding default categories:", error);
      Alert.alert(
        "Error",
        "Failed to create default categories. Please try again."
      );
    }
  }, [id, updatePackingListMutation]);

  // Enhanced category addition with better validation
  const addCategory = useCallback(
    (category: string) => {
      if (!category.trim()) {
        Alert.alert("Invalid Input", "Please enter a category name");
        return;
      }

      const categoryExists = memoizedPackingList.some(
        (item) => item.name.toLowerCase() === category.toLowerCase()
      );

      if (categoryExists) {
        Alert.alert(
          "Category Exists",
          "A category with this name already exists"
        );
        return;
      }

      try {
        const newCategory: PackingList = {
          _id: String(uuid.v4()),
          name: category,
          isActive: false,
          type: "category",
          showSubItems: true,
          items: [],
        };

        const updatedPackingList = [...memoizedPackingList, newCategory];

        updatePackingListWithAuth(id as string, updatedPackingList);

        setPackingList(updatedPackingList);
        setIsAddCategoryModalVisible(false);
        setSelectedItem(newCategory);

        setTimeout(() => {
          setIsModalVisible(true);
        }, 1000);
      } catch (error) {
        console.error("Error adding category:", error);
        Alert.alert("Error", "Failed to add category. Please try again.");
      }
    },
    [id, memoizedPackingList, updatePackingListMutation]
  );

  // Enhanced item addition with better duplicate handling
  const handleAddItem = useCallback(
    (items: ClothesItem[]) => {
      if (!items || items.length === 0) return;

      try {
        const existingItems = items.filter((item) =>
          packingList.some((packingItem) => packingItem._id === item._id)
        );

        const existingItemsCategory = items.filter((item) =>
          packingList.some((packingItem) => {
            if (packingItem?.type === "category" && packingItem.items) {
              return packingItem.items.some(
                (subItem) => subItem._id === item._id
              );
            }
            return false;
          })
        );

        const filteredItems = items.filter(
          (item) =>
            !packingList.some((packingItem) => {
              if (packingItem?.type === "category" && packingItem.items) {
                return packingItem.items.some(
                  (subItem) => subItem._id === item._id
                );
              }
              return packingItem._id === item._id;
            })
        );

        const updatedPackingList = packingList.map((item) => {
          const existingItem = existingItems.find(
            (existingItem) => existingItem._id === item._id
          );

          let alreadyAdded = false;

          if (item.type === "category" && item.items) {
            item.items = item.items.map((subItem) => {
              const subItemExist = existingItemsCategory.find(
                (existingItem) => existingItem._id === subItem._id
              );

              if (subItemExist) {
                alreadyAdded = true;
                return {
                  ...subItem,
                  quantity: (subItem.quantity || 0) + 1,
                };
              }
              return subItem;
            });
          }

          if (alreadyAdded) {
            return item;
          }

          if (existingItem && !alreadyAdded) {
            return {
              ...item,
              quantity: (item.quantity || 0) + 1,
            };
          }

          return item;
        });

        const finalPackingList = [...updatedPackingList, ...filteredItems];

        updatePackingListWithAuth(id as string, finalPackingList);

        setPackingList(finalPackingList);
      } catch (error) {
        console.error("Error adding items:", error);
        Alert.alert("Error", "Failed to add items. Please try again.");
      }
    },
    [packingList, id, updatePackingListMutation]
  );

  // Enhanced drag end handling
  const handleDragEnd = useCallback(
    ({ data }: { data: PackingList[] }) => {
      try {
        setPackingList(data);
        updatePackingListWithAuth(id as string, data);
      } catch (error) {
        console.error("Error reordering items:", error);
        Alert.alert("Error", "Failed to reorder items. Please try again.");
      }
    },
    [id, updatePackingListMutation]
  );

  // Enhanced item addition to packing list with better error handling
  const handleAddItemInPackingList = useCallback(
    (items: ClothesItem[]) => {
      if (!selectedItem || !items.length) return;

      try {
        console.log(
          "Adding items to packing list:",
          JSON.stringify(items, null, 2)
        );

        const newPackingList = packingList.map((item) => ({ ...item }));

        // Remove duplicate items
        const packingListNew = newPackingList.filter(
          (item) => !items.some((newItem) => newItem._id === item._id)
        );

        // Find the category to add items into
        const selectedItemIndex = packingListNew.findIndex(
          (item) => item._id === selectedItem._id
        );

        if (selectedItemIndex !== -1) {
          const currentItems = packingListNew[selectedItemIndex].items || [];
          packingListNew[selectedItemIndex].items = [...currentItems, ...items];
          packingListNew[selectedItemIndex].isActive = false;

          // Invalidate cache
          queryClient.invalidateQueries({ queryKey: ["clothes"] });

          // Force refetch after delay
          setTimeout(() => {
            console.log(
              "Forcing refetch of clothes data after adding items to packing list"
            );
            queryClient.refetchQueries({ queryKey: ["clothes"] });
          }, 500);
        }

        setPackingList(packingListNew);
        updatePackingListWithAuth(id as string, packingListNew);
      } catch (error) {
        console.error("Error adding items to packing list:", error);
        Alert.alert(
          "Error",
          "Failed to add items to packing list. Please try again."
        );
      }
    },
    [selectedItem, packingList, id, updatePackingListMutation, queryClient]
  );

  // Enhanced category removal with better confirmation
  const removeCategory = useCallback(
    (category: PackingList) => {
      Alert.alert(
        "Delete Category",
        `Are you sure you want to delete "${category.name}"? This will remove all items in this category from the packing list.`,
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Delete",
            style: "destructive",
            onPress: () => {
              try {
                const packingListNew = packingList.filter(
                  (item) => item._id !== category._id
                );

                updatePackingListWithAuth(id as string, packingListNew);

                setPackingList(packingListNew);
                invalidateClosetCache();
              } catch (error) {
                console.error("Error removing category:", error);
                Alert.alert(
                  "Error",
                  "Failed to remove category. Please try again."
                );
              }
            },
          },
        ]
      );
    },
    [packingList, id, updatePackingListMutation, invalidateClosetCache]
  );

  const setShowSubItems = useCallback(
    (categoryId: string, show: boolean) => {
      try {
        console.log("🔄 setShowSubItems called with:", {
          categoryId,
          show,
        });

        const packingListNew = packingList.map((item) => {
          if (item._id === categoryId) {
            console.log("✅ Found matching category:", item.name || item._id);
            return { ...item, showSubItems: show };
          }
          return item;
        });

        console.log("📝 Updated packing list with new showSubItems state:");
        console.table(
          packingListNew.map((item) => ({
            id: item._id,
            name: item.name,
            showSubItems: item.showSubItems,
          }))
        );

        updatePackingListWithAuth(id as string, packingListNew);
        setPackingList(packingListNew);
      } catch (error) {
        console.error("❌ Error toggling sub items:", error);
      }
    },
    [packingList, id, updatePackingListMutation]
  );

  // Enhanced active items management
  const changeAllActiveItems = useCallback(
    (parentId: string, isActive: boolean) => {
      try {
        const packingListNew = packingList.map((item) => {
          if (item._id === parentId) {
            return {
              ...item,
              isActive: isActive,
              items: item.items?.map((subItem) => ({
                ...subItem,
                isActive: isActive,
              })),
            };
          }
          return item;
        });

        updatePackingListWithAuth(id as string, packingListNew);

        setPackingList(packingListNew);
      } catch (error) {
        console.error("Error changing active items:", error);
        Alert.alert("Error", "Failed to update items. Please try again.");
      }
    },
    [packingList, id, updatePackingListWithAuth]
  );

  // Enhanced individual item activation
  const changeItemsActive = useCallback(
    (parentId: string, itemId: string) => {
      try {
        const packingListNew = packingList.map((item) => {
          if (item._id === parentId && item.items) {
            const updatedItems = item.items.map((subItem) => {
              if (subItem._id === itemId) {
                return { ...subItem, isActive: !subItem.isActive };
              }
              return subItem;
            });

            // Check if all items are active
            const allItemsActive = updatedItems.every(
              (subItem) => subItem.isActive
            );

            return {
              ...item,
              items: updatedItems,
              isActive: allItemsActive,
            };
          }
          return item;
        });

        updatePackingListWithAuth(id as string, packingListNew);

        setPackingList(packingListNew);
      } catch (error) {
        console.error("Error changing item active state:", error);
        Alert.alert("Error", "Failed to update item. Please try again.");
      }
    },
    [packingList, id, updatePackingListWithAuth]
  );

  // Enhanced item removal from category
  const removeItemOnCategory = useCallback(
    (categoryId: string, itemSelected: ClothesItem) => {
      Alert.alert(
        "Remove Item",
        "Are you sure you want to remove this item from the category?",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Remove",
            style: "destructive",
            onPress: () => {
              try {
                const packingListNew = packingList.map((item) => {
                  if (item._id === categoryId && item.items) {
                    return {
                      ...item,
                      items: item.items.filter(
                        (subItem) => subItem._id !== itemSelected._id
                      ),
                    };
                  }
                  return item;
                });

                const updatedList = [...packingListNew, itemSelected];

                updatePackingListWithAuth(id as string, updatedList);

                setPackingList(updatedList);
                invalidateClosetCache();
              } catch (error) {
                console.error("Error removing item from category:", error);
                Alert.alert(
                  "Error",
                  "Failed to remove item. Please try again."
                );
              }
            },
          },
        ]
      );
    },
    [packingList, id, updatePackingListWithAuth, invalidateClosetCache]
  );

  // Enhanced packing list reset
  const resetPackingList = useCallback(() => {
    Alert.alert(
      "Reset Packing List",
      "Are you sure you want to reset the entire packing list? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset",
          style: "destructive",
          onPress: () => {
            try {
              setPackingList([]);
              updatePackingListWithAuth(id as string, []);
              setShowEmptyState(true);
              invalidateClosetCache();
            } catch (error) {
              console.error("Error resetting packing list:", error);
              Alert.alert(
                "Error",
                "Failed to reset packing list. Please try again."
              );
            }
          },
        },
      ]
    );
  }, [id, updatePackingListWithAuth, invalidateClosetCache]);

  // Enhanced item removal
  const removeItem = useCallback(
    (itemId: string) => {
      Alert.alert("Remove Item", "Are you sure you want to remove this item?", [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: () => {
            try {
              const packingListNew = packingList.filter(
                (item) => item._id !== itemId
              );

              updatePackingListWithAuth(id as string, packingListNew);

              setPackingList(packingListNew);

              // Enhanced cache management
              removeItemFromPackingListMutation.mutate(itemId);

              setTimeout(() => {
                forceRefreshAllCaches();
                setTimeout(hardRefresh, 2000);
              }, 1000);
            } catch (error) {
              console.error("Error removing item:", error);
              Alert.alert("Error", "Failed to remove item. Please try again.");
            }
          },
        },
      ]);
    },
    [
      packingList,
      id,
      updatePackingListWithAuth,
      removeItemFromPackingListMutation,
      forceRefreshAllCaches,
      hardRefresh,
    ]
  );

  // Enhanced sub-item removal
  const removeSubItem = useCallback(
    (parentId: string, subItemId: string) => {
      Alert.alert(
        "Delete Item",
        "Are you sure you want to delete this item? It will be removed from your packing list but not from your virtual closet.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Delete",
            style: "destructive",
            onPress: () => {
              try {
                const packingListNew = packingList.map((item) => {
                  if (item._id === parentId && item.items) {
                    return {
                      ...item,
                      items: item.items.filter(
                        (subItem) => subItem._id !== subItemId
                      ),
                    };
                  }
                  return item;
                });

                updatePackingListWithAuth(id as string, packingListNew);

                setPackingList(packingListNew);
                setIsEditClothsModalVisible(false);

                // Enhanced cache management
                removeItemFromPackingListMutation.mutate(subItemId);

                setTimeout(() => {
                  forceRefreshAllCaches();
                  setTimeout(hardRefresh, 2000);
                }, 1000);
              } catch (error) {
                console.error("Error removing sub item:", error);
                Alert.alert(
                  "Error",
                  "Failed to remove item. Please try again."
                );
              }
            },
          },
        ]
      );
    },
    [
      packingList,
      id,
      updatePackingListWithAuth,
      removeItemFromPackingListMutation,
      forceRefreshAllCaches,
      hardRefresh,
    ]
  );

  // Enhanced edit clothes function with better parameter handling
  const saveEditCloths = useCallback(
    (
      note: string,
      quantity: number,
      parentId: string,
      itemId: string,
      category: string | CategoryObject,
      color: string,
      brand: string,
      imageUrl?: string,
      name?: string
    ) => {
      try {
        let newPackingList = packingList.map((item) => ({ ...item }));

        // Update the item properties
        const selectedItemIndex = newPackingList.findIndex(
          (item) => item._id === parentId
        );

        if (
          selectedItemIndex !== -1 &&
          newPackingList[selectedItemIndex].items
        ) {
          newPackingList[selectedItemIndex].items = newPackingList[
            selectedItemIndex
          ].items!.map((item) =>
            item._id === itemId
              ? {
                ...item,
                note,
                quantity,
                color,
                brand,
                imageUrl: imageUrl || item.imageUrl,
                name: name || item.name,
              }
              : item
          );
        }

        // Handle category move if category is an object with _id
        if (
          typeof category === "object" &&
          category._id &&
          category._id !== parentId
        ) {
          const selectedCategoryIndex = newPackingList.findIndex(
            (item) => item._id === category._id
          );

          if (selectedCategoryIndex !== -1) {
            // Get the item from the old category
            const itemToMove = newPackingList[selectedItemIndex].items?.find(
              (item) => item._id === itemId
            );

            if (itemToMove && newPackingList[selectedCategoryIndex].items) {
              newPackingList[selectedCategoryIndex].items = [
                ...newPackingList[selectedCategoryIndex].items,
                itemToMove,
              ];
            }

            // Remove the item from the old category
            if (newPackingList[selectedItemIndex].items) {
              newPackingList[selectedItemIndex].items = newPackingList[
                selectedItemIndex
              ].items.filter((item) => item._id !== itemId);
            }
          }
        }

        setPackingList(newPackingList);
        updatePackingListWithAuth(id as string, newPackingList);
      } catch (error) {
        console.error("Error saving edited clothes:", error);
        Alert.alert("Error", "Failed to save changes. Please try again.");
      }
    },
    [packingList, id, updatePackingListWithAuth]
  );

  // Create a wrapper function for saveEditCloths to match AddItemsModal expectations
  const saveEditClothsWrapper = useCallback(
    (note: string, quantity: number, parentId: string, itemId: string) => {
      // Call the original function with default values for missing parameters
      saveEditCloths(note, quantity, parentId, itemId, "", "", "", "", "");
    },
    [saveEditCloths]
  );

  return (
    <View style={{ flexGrow: 1, flexBasis: "100%" }}>
      <View style={{ flex: 1 }}>
        {showEmptyState && (
          <View style={{ marginTop: 32 }}>
            <EmptyState
              onPress={() => {
                setShowEmptyState(false);
                addDefaultCategory();
              }}
              onPressTemplate={() => {
                setIsTemplateListModalVisible(true);
              }}
            />
          </View>
        )}
        {!showEmptyState && (
          <ScrollView
            style={{
              flexGrow: 1,
              flexShrink: 0,
              flexBasis: "100%",
            }}
            contentContainerStyle={{ paddingBottom: 350 }}
            showsVerticalScrollIndicator={false}
          >
            <View style={{ marginTop: 32 }}>
              <View
                style={{
                  marginBottom: 20,
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  gap: 10,
                }}
              >
                <TitleHeader title="My Packing List" />
                <SubtitleText
                  string={`(${allItemCount} / ${countOnAllTotalItems})`}
                />
              </View>
              <FlatList
                data={memoizedPackingList}
                keyExtractor={(item) => item._id}
                renderItem={({ item }) => (
                  <View>
                    <OverviewItem
                      item={item}
                      changeAllActiveItems={changeAllActiveItems}
                      setShowSubItems={setShowSubItems}
                      parentId={item._id}
                      removeCategory={removeCategory}
                      removeItem={removeItem}
                      isActive={item.isActive}
                      isSubItem={false}
                      removeSubItem={removeSubItem}
                      onPress={() => {
                        if (item?.type === "category") {
                          setSelectedItem(item);
                          setIsModalVisible(true);
                        }
                      }}
                      onLongPress={() => { }}
                      handleMyUseRecommendations={() => {
                        setIsMyUseRecommendationsModalVisible(true);
                      }}
                    />
                    {item?.items &&
                      item?.items?.length > 0 &&
                      item?.showSubItems ? (
                      <FlatList
                        style={{ marginLeft: 20, marginBottom: 10 }}
                        scrollEnabled={false}
                        contentContainerStyle={{ gap: 10 }}
                        data={[
                          ...item?.items,
                          {
                            addItem: true,
                            _id: "add-item",
                            name: "",
                            isActive: false,
                            type: "clothes" as const,
                          },
                        ]}
                        renderItem={({ item: subItem }) => {
                          console.log("Rendering subItem:", subItem);
                          console.log("Parent item ID:", item);

                          return (
                            <OverviewItem
                              changeItemsActive={changeItemsActive}
                              addCloths={() => {
                                if (item?.type === "category") {
                                  setSelectedItem(item);
                                  setIsModalVisible(true);
                                }
                              }}
                              handleMyUseRecommendations={() => {
                                setIsMyUseRecommendationsModalVisible(true);
                              }}
                              parentId={item._id}
                              item={subItem}
                              isActive={false}
                              isSubItem={true}
                              onPress={() => {
                                setSelectedItem(item);
                                setSelectedCloth(subItem as ClothesItem);
                                setIsEditClothsModalVisible(true);
                              }}
                              onLongPress={() => { }}
                              removeItem={removeItem}
                              removeSubItem={removeSubItem}
                              setShowSubItems={(show: boolean) =>
                                setShowSubItems(item._id, show)
                              }
                              removeCategory={removeCategory}
                            />
                          );
                        }}
                      />
                    ) : (
                      item?.showSubItems && (
                        <OverviewItemContainer
                          onPress={() => {
                            if (item?.type === "category") {
                              setSelectedItem(item);
                              setIsModalVisible(true);
                            }
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              gap: 10,
                              marginLeft: 20,
                            }}
                          >
                            <PlusIcon size={20} color="#5C5C5C" />
                            <OverviewItemText
                              style={{ color: "#5C5C5C" }}
                              numberOfLines={1}
                              ellipsizeMode="tail"
                            >
                              Add new item
                            </OverviewItemText>
                          </View>
                        </OverviewItemContainer>
                      )
                    )}
                  </View>
                )}
              />
            </View>
            <SafeAreaView>
              <View>
                <Button
                  title="Add New Category"
                  onPress={() => setIsAddCategoryModalVisible(true)}
                />
              </View>
            </SafeAreaView>
          </ScrollView>
        )}
        <AddClothsModal
          isVisible={isAddClothsModalVisible}
          onClose={() => setIsAddClothsModalVisible(false)}
          onAdd={handleAddItem}
        />
      </View>

      <AddClothsCategoryModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        item={selectedItem}
        addedCloths={getallClothItemsId}
        packingList={packingList}
        onAdd={handleAddItemInPackingList}
        setIsAddItemsModalVisible={setIsAddItemsModalVisible}
      />

      <AddCategoryModal
        isVisible={isAddCategoryModalVisible}
        onClose={() => setIsAddCategoryModalVisible(false)}
        item={selectedItem}
        packingList={packingList}
        setIsTemplateListModalVisible={setIsTemplateListModalVisible}
        onAdd={addCategory}
      />

      <EditClothsModal
        isVisible={isEditClothsModalVisible}
        onClose={() => setIsEditClothsModalVisible(false)}
        item={selectedCloth}
        packingList={packingList}
        selectedItem={selectedItem}
        saveEditCloths={saveEditCloths}
        removeItem={removeSubItem}
      />

      <AddItemsModal
        isVisible={isAddItemsModalVisible}
        onClose={() => setIsAddItemsModalVisible(false)}
        item={selectedCloth}
        selectedItem={selectedItem}
        packingList={packingList}
        handleAddItemInPackingList={handleAddItemInPackingList}
        setIsModalVisible={setIsModalVisible}
        saveEditCloths={saveEditClothsWrapper}
        removeItem={removeSubItem}
      />

      <TemplateListModal
        isVisible={isTemplateListModalVisible}
        onClose={() => {
          setIsTemplateListModalVisible(false);
          setShowRecommendations(false);
          }
        }
        setShowEmptyState={setShowEmptyState}
        onSelect={handleSelectTemplate}
        setShowRecommendations={setShowRecommendations}
        isShowRecommendations={showRecommendations}
      />

      <RecommendationModal
        isVisible={isMyUseRecommendationsModalVisible}
        onClose={() => setIsMyUseRecommendationsModalVisible(false)}
      />
    </View>
  );
};
