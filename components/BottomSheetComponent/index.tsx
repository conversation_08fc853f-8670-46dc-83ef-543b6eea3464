import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { ReactNode, forwardRef } from 'react';
import { Dimensions, Keyboard, Platform } from 'react-native';

interface BottomSheetContainerProps {
  children: ReactNode;
  snapPoints?: string[];
  onChange?: (index: number) => void;
  enableDynamicSizing?: boolean;
}

const BottomSheetComponent = forwardRef<BottomSheet, BottomSheetContainerProps>(
  ({ children, snapPoints, onChange, enableDynamicSizing }, ref) => {
    const renderBackdrop = (props: any) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        pressBehavior="close"
        onPress={() => {
          Keyboard.dismiss();
        }}
        opacity={0.4}
      />
    );

    return (
      <BottomSheet
        ref={ref}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose={true}
        enableOverDrag={false}
        enableDynamicSizing={enableDynamicSizing}
        backdropComponent={renderBackdrop}
        maxDynamicContentSize={Dimensions.get('window').height - 100}
        keyboardBehavior={Platform.OS === 'ios' ? 'extend' : 'interactive'}
        android_keyboardInputMode="adjustPan"
        onChange={(index) => {
          if (index === -1) {
            Keyboard.dismiss();
          }
          onChange && onChange(index);
        }}
      >
        <BottomSheetScrollView
          showsVerticalScrollIndicator={true}
          bounces={false}
        >
          {children}
        </BottomSheetScrollView>
      </BottomSheet>
    );
  },
);

export default BottomSheetComponent;
