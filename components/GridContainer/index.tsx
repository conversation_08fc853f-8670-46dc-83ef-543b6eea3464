import React from 'react';
import { View, StyleSheet, Dimensions, ViewStyle } from 'react-native';

interface GridContainerProps {
  children: React.ReactNode;
  itemsPerRow?: number;
  gap?: number;
  containerStyle?: ViewStyle;
  width?: number;
  itemsToShow?: number;
}

export const GridContainer = ({
  children,
  itemsPerRow = 2,
  gap = 16,
  containerStyle,
  width = Dimensions.get('window').width * 0.8999, // work around to prevent unintentional wrapping
  itemsToShow,
}: GridContainerProps) => {
  const totalGapSize = (itemsPerRow - 1) * gap;
  const childWidth = (width - totalGapSize) / itemsPerRow;

  const styles = StyleSheet.create({
    grid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-around',
      gap: gap,
      ...containerStyle,
    },
    item: {
      width: childWidth,
    },
  });

  return (
    <View style={styles.grid}>
      {React.Children.toArray(children)
        .slice(0, itemsToShow !== undefined ? itemsToShow : undefined)
        .map((child, index) => (
          <View style={styles.item} key={index}>
            {child}
          </View>
        ))}
    </View>
  );
};
