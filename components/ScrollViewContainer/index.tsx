import React from 'react';
import { ScrollView } from 'react-native';
import { Edges, SafeAreaView } from 'react-native-safe-area-context';

type ScrollViewContainerProps = {
  children: React.ReactNode;
  edges?: Edges;
};

export default function ScrollViewContainer({
  children,
  edges = ['top', 'bottom'],
}: ScrollViewContainerProps) {
  return (
    <SafeAreaView edges={edges} style={{ flex: 1 }}>
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={{
          width: '90%',
          alignSelf: 'center',
        }}
      >
        {children}
      </ScrollView>
    </SafeAreaView>
  );
}
