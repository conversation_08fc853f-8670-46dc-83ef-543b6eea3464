import RightChevronBlack from '@/assets/svg/right-chevron-black.svg';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Text, View, ActivityIndicator } from 'react-native';
import { SettingsDropdown } from '../SettingsDropdown';
import {
  SettingsItemChildrenContainer,
  SettingsItemContainer,
  SettingsTouchable,
  SettingsTouchableText,
} from './styles';
import { useQueryClient } from '@tanstack/react-query';
import { useFocusEffect } from 'expo-router';

export type SettingsOption = {
  label: string;
  value: string | boolean;
};

export type SettingsItemProps = {
  title: string;
  itemKey?: string;
  onPress?: () => void;
  options?: SettingsOption[];
  default?: SettingsOption;
  selected?: SettingsOption;
  children?: React.ReactNode;
  keywords?: string[];
  // Mutation properties for individual buttons
  mutation?: {
    mutate: (data: any, options?: any) => void;
  };
  queryKey?: string;
  isLoading?: boolean;
  // Data for individual buttons
  data?: any;
};

export const SettingsItem = ({
  title,
  itemKey,
  options,
  onPress,
  selected,
  children,
  keywords,
  mutation,
  queryKey = 'profile',
  isLoading = false,
  data,
}: SettingsItemProps) => {
  const [selectedValue, setSelectedValue] = useState<SettingsOption>({
    label: '',
    value: '',
  });

  const queryClient = useQueryClient();
  const isNavigating = useRef(false);

  // Use data from button props if available, otherwise fall back to selected prop
  const effectiveSelected = selected || (itemKey && data ? 
    options?.find((option) => option.value === data?.[itemKey]) || options?.[0] : 
    undefined
  );

  useEffect(() => {
    if (effectiveSelected) {
      setSelectedValue(effectiveSelected);
    }
  }, [effectiveSelected]);

  useFocusEffect(useCallback(() => {
    isNavigating.current = false;
  }, []));

  const handleOnOptionSelect = useCallback(
    (item: SettingsOption) => {
      if (itemKey && mutation) {
        mutation.mutate(
          { [itemKey]: item.value },
          {
            onSuccess: () =>
              queryClient.invalidateQueries({
                queryKey: [queryKey],
              }),
          },
        );
        setSelectedValue(item);
      }
      onPress && onPress();
    },
    [itemKey, onPress, mutation, queryClient, queryKey],
  );

  return (
    <SettingsItemContainer>
      {itemKey && options && options.length > 0 ? (
        <SettingsDropdown
          title={title}
          options={options}
          selectedItem={selectedValue}
          onSelect={handleOnOptionSelect}
          isLoading={isLoading}
        />
      ) : (
        <SettingsTouchable onPress={() => {
          if (isNavigating.current) return;
          isNavigating.current = true;
          onPress && onPress();
        }} activeOpacity={0.8}>
          <SettingsTouchableText>{title}</SettingsTouchableText>
          <RightChevronBlack />
        </SettingsTouchable>
      )}
      {children ? (
        <SettingsItemChildrenContainer>
          {children}
        </SettingsItemChildrenContainer>
      ) : keywords ? (
        <SettingsItemChildrenContainer>
          <View>
            <Text style={{ fontSize: 16, fontWeight: 600 }} numberOfLines={1}>
              {keywords.join(', ')}
            </Text>
          </View>
        </SettingsItemChildrenContainer>
      ) : null}
    </SettingsItemContainer>
  );
};
