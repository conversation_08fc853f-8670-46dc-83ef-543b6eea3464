import { View, Text, TouchableOpacity } from 'react-native';
import styled , { DefaultTheme } from 'styled-components/native';
import Input from '../common/Input';

export const AddClothsModalContainer = styled(View)`
  height: 80%;
  background-color: white;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px;
`;

export const AddClothsModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom-width: 1px;
  border-bottom-color: #EBEBEB;
  margin-bottom: 8px;
`;

export const HeaderText = styled(Text)`
  font-size: 18px;
  font-family: MuktaVaaniMedium;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;


export const AddClothsModalTitle = styled(Text)`
  font-size: 24px;
  font-family: MuktaVaaniSemiBold;
  color: #1C1C1C;
  text-align: center;
  flex: 1;
`;

export const AddClothsModalInput = styled(Input)`
  width: 100%;
  height: 40px;
  border-radius: 10px;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;


export const ClothItemContainer = styled(View)<{ width: number, isSelected?: boolean }>`
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: ${(props: { isSelected?: boolean }) => props.isSelected ? '#D4F0E8' : '#EBEBEB'};
  border-radius: 10px;
  margin: 8px;
  margin-bottom: 36px; /* Add extra margin at the bottom for the label */
  padding: 0;
  height: ${({ width }: { width: number }) => Math.min((width / 2) - 20, 160)}px;
  width: ${({ width }: { width: number }) => Math.min((width / 2) - 20, 160)}px;
  position: relative;
  overflow: visible; /* Changed from hidden to visible to show the label */
`;

export const AddClothItemContainer = styled(View)`
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #EBEBEB;
  border-radius: 10px;
  margin: 8px;
  margin-bottom: 36px; /* Add extra margin at the bottom for the label */
  padding: 0;
  height: ${({ width }: { width: number }) => Math.min((width / 2) - 20, 160)}px;
  width: ${({ width }: { width: number }) => Math.min((width / 2) - 20, 160)}px;
  overflow: visible; /* Changed from hidden to visible to show the label */
  position: relative;
`;


export const ClothItemImage = styled.Image`
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: #F0F0F0;
`;

export const ClothItemName = styled(Text)`
  font-size: 16px;
  font-family: MuktaVaani;
  text-align: left;
  width: 100%;
  margin-top: 8px;
  padding-horizontal: 0;
  color: #333333;
  height: 24px;
  overflow: visible;
  position: absolute;
  bottom: -28px;
  left: 0;
`;

export const ClothesContainer = styled(View)`
  flex-direction: row;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: space-evenly;
  align-items: center;
  padding-horizontal: 4px;
`;

export const CheckBox = styled(View)`
  width: 22px;
  height: 22px;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  border-width: 1px;
  border-color: #0E7E61;
  position: absolute;
  right: 8px;
  bottom: 8px;
  background-color: #FFF;
  elevation: 2;
  shadow-color: #000;
  shadow-offset: 0px 1px;
  shadow-opacity: 0.2;
  shadow-radius: 1.5px;
`;

export const CategoryItem = styled(TouchableOpacity)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-width: 1px;
  border-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  border-radius: 10px;
  margin: 5px;
  padding: 8px 16px;
  background-color: ${({ isSelected, theme }: { isSelected: boolean, theme: DefaultTheme }) => isSelected ? theme.brand.green[500] : '#F5F5F5'};
`;

export const CategoryItemText = styled(Text)<{ isSelected: boolean }>`
  font-size: 16px;
  font-family: MuktaVaani;
  color: ${({ isSelected }: { isSelected: boolean }) => isSelected ? '#FFF' : '#000'};
`;
