import { Activity<PERSON>ndi<PERSON>or, FlatList, LayoutChangeEvent, TouchableOpacity, View, Alert } from 'react-native';
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalTitle,
  HeaderText,
  AddClothsModalInput,
  ClothItemContainer,
  ClothItemName,
  ClothesContainer,
  AddClothItemContainer,
  CategoryItem,
  CategoryItemText,
  ClothItemImage
} from './styles';
import Modal from 'react-native-modal';
import { PlusIcon } from 'lucide-react-native';
import React, { useState, useEffect, useMemo } from 'react';
import { Checkbox } from '@/components/common/Checkbox';
import { CLOTHES } from '@/constants/Clothes';
import { getClothes, getCategories } from '@/methods/cloths';
// Import the main categories from CategoryFilterModal as a fallback
import { MAIN_CATEGORIES } from '@/components/CategoryFilterModal';
// Import API-based categories
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';

import ConfirmAddClothes from '@/components/ConfirmAddClothes';
import SimpleAddNewItemModal from '@/components/SimpleAddNewItemModal';

interface AddClothsModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
  selectedFilter?: string; // Optional filter to match the Closet screen
}

function AddClothsModal({ isVisible, onClose, onAdd, selectedFilter = 'All' }: AddClothsModalProps) {
  const [search, setSearch] = useState('');
  const [clothes, setClothes] = useState<any[]>([]);
  const [width, setWidth] = useState(0);
  const [selectedClothes, setSelectedClothes] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>({ id: 'all', name: 'All' });
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [isAddNewItemModalVisible, setIsAddNewItemModalVisible] = useState(false);

  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<any[]>(MAIN_CATEGORIES);

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Log when the confirm modal visibility changes
  useEffect(() => {
    console.log('isConfirmModalVisible changed:', isConfirmModalVisible);
  }, [isConfirmModalVisible]);

  // Log when the add new item modal visibility changes
  useEffect(() => {
    console.log('isAddNewItemModalVisible changed:', isAddNewItemModalVisible);
  }, [isAddNewItemModalVisible]);

  // Log when the modal visibility changes
  useEffect(() => {
    console.log('AddClothsModal isVisible changed:', isVisible);
  }, [isVisible]);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  // State for tracking image loading status
  const [imageLoadingStates, setImageLoadingStates] = useState<{[key: string]: boolean}>({});

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };


  // Use the getClothes hook to get all clothes without filtering
  // We'll do client-side filtering for more reliable results
  const { data: clothesData, isLoading: isClothesLoading } = getClothes();

  // Log once when the component mounts
  useEffect(() => {
    console.log('AddClothsModal - Fetching all clothes and will filter client-side');
  }, []);

  // Update the clothes state when the data changes
  // Apply client-side filtering based on the selected filter from the Closet screen
  useEffect(() => {
    if (clothesData && clothesData.items) {
      // If no filter is specified or "All" is selected, show all items
      if (!selectedFilter || selectedFilter === 'All') {
        setClothes(clothesData.items);
        return;
      }

      // Filter items by category based on the selectedFilter from the Closet screen
      const filteredItems = clothesData.items.filter((item: any) => {
        // If "All" is selected, return all items
        if (selectedFilter === 'All') {
          return true;
        }

        // Get the category name from the item
        const itemCategory = item.category?.name || item.categoryName || '';

        // Skip items without a category
        if (!itemCategory) {
          return false;
        }

        // Normalize categories for comparison
        const normalizedItemCategory = itemCategory.toLowerCase().trim();
        const normalizedFilterCategory = selectedFilter.toLowerCase().trim();

        // Exact match (case-insensitive)
        if (normalizedItemCategory === normalizedFilterCategory) {
          return true;
        }

        // Handle plural/singular variations
        if (normalizedItemCategory.endsWith('s') &&
            normalizedItemCategory.slice(0, -1) === normalizedFilterCategory) {
          // Item is plural, filter is singular (e.g., "Tops" matches "Top")
          return true;
        }

        if (normalizedFilterCategory.endsWith('s') &&
            normalizedFilterCategory.slice(0, -1) === normalizedItemCategory) {
          // Filter is plural, item is singular (e.g., "Top" matches "Tops")
          return true;
        }

        // No match
        return false;
      });

      setClothes(filteredItems);
    } else if (!isClothesLoading) {
      // Fallback to hardcoded data if API returns empty data
      setClothes(CLOTHES);
    }
  }, [clothesData, isClothesLoading, selectedFilter]);

  // Add error handling for API failures
  useEffect(() => {
    if (clothesData === undefined && !isClothesLoading) {
      console.error('Failed to fetch clothes data');
      // Show a toast or alert for API failure
      // Alert.alert('Error', 'Failed to fetch clothes data. Using sample data instead.');
      setClothes(CLOTHES);
    }
  }, [clothesData, isClothesLoading]);

  // Reset selected clothes when the modal becomes visible
  useEffect(() => {
    if (isVisible) {
      setSelectedClothes([]);
    }
  }, [isVisible]);

  // Load categories from backend API
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('AddClothsModal - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log('AddClothsModal - Using gender from getCurrentUserGender:', gender);
        }

        if (!gender) {
          console.warn('AddClothsModal - No gender available, using default categories');
          setDisplayCategories(MAIN_CATEGORIES);
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        if (backendCategories && backendCategories.length > 0) {
          // Format categories for display
          // Create a Set to track category names we've already added (for deduplication)
          const addedCategoryNames = new Set<string>(['All']);

          // Format and deduplicate categories
          const formattedCategories = [
            { id: 'all', name: 'All' } // Always include "All" category
          ];

          // Define basic categories that should always be included
          const basicCategories = [
            { id: 'tops', name: 'Tops' },
            { id: 'bottoms', name: 'Bottoms' },
            { id: 'dresses', name: 'Dresses' },
            { id: 'shoes', name: 'Shoes' },
            { id: 'accessories', name: 'Accessories' }
          ];

          // Add backend categories with deduplication
          // Make sure backendCategories is an array before iterating
          if (!Array.isArray(backendCategories)) {
            console.error('AddClothsModal - backendCategories is not an array:', backendCategories);
            return; // Exit early if not an array
          }

          backendCategories.forEach(category => {
            // Skip if category is undefined or null or has no name
            if (!category || !category.name) {
              console.log('AddClothsModal - Skipping category with no name');
              return;
            }

            // Skip if we've already added a category with this name
            if (addedCategoryNames.has(category.name)) {
              console.log(`AddClothsModal - Skipping duplicate category: ${category.name}`);
              return;
            }

            formattedCategories.push({
              id: category.id || `category-${category.name.toLowerCase().replace(/\s+/g, '-')}`,
              name: category.name
            });

            // Add this category name to our Set to prevent duplicates
            addedCategoryNames.add(category.name);
          });

          // Make sure basic categories are included
          basicCategories.forEach(basicCat => {
            // Check if this basic category or a similar one is already included
            const isIncluded = formattedCategories.some(cat =>
              cat.name === basicCat.name ||
              cat.name.toLowerCase() === basicCat.name.toLowerCase() ||
              cat.name.includes(basicCat.name) ||
              basicCat.name.includes(cat.name)
            );

            if (!isIncluded) {
              console.log(`AddClothsModal - Adding missing basic category: ${basicCat.name}`);
              formattedCategories.push(basicCat);
              addedCategoryNames.add(basicCat.name);
            }
          });

          // Log all categories
          console.log('=== CATEGORIES IN ADD CLOTHS MODAL ===');
          formattedCategories.forEach((cat, index) => {
            console.log(`${index + 1}. ${cat.name} (ID: ${cat.id})`);
          });
          console.log('=== END CATEGORIES IN ADD CLOTHS MODAL ===');

          console.log(`AddClothsModal - Loaded ${formattedCategories.length} categories from backend for gender: ${gender}`);
          setDisplayCategories(formattedCategories);
        } else {
          console.warn('AddClothsModal - No categories returned from backend, using default categories');
          // Fallback to MAIN_CATEGORIES if no categories returned
          setDisplayCategories(MAIN_CATEGORIES);
        }
      } catch (error) {
        console.error('Error loading categories from backend:', error);
        // Fallback to MAIN_CATEGORIES if there's an error
        setDisplayCategories(MAIN_CATEGORIES);
      }
    };

    loadGenderCategories();
  }, [userProfile]);

  // Check if an item is selected by comparing IDs
  const isItemSelected = useMemo(() => {
    return (item: any) => {
      return selectedClothes.some(selectedItem => selectedItem._id === item._id);
    };
  }, [selectedClothes]);

  // Add an item to the selected items list
  const addSelectedCloth = (cloth: any) => {
    // Add quantity property if it doesn't exist
    const clothWithQuantity = { ...cloth, quantity: 1 };
    console.log('Adding cloth:', clothWithQuantity);
    setSelectedClothes([...selectedClothes, clothWithQuantity]);

    // Provide haptic feedback (if available)
    // Uncomment when Haptics library is available
    // Haptics.selectionAsync();
  }

  // Remove an item from the selected items list
  const removeSelectedCloth = (cloth: any) => {
    console.log('Removing cloth:', cloth.name);
    setSelectedClothes(selectedClothes.filter((item) => item._id !== cloth._id));

    // Provide haptic feedback (if available)
    // Uncomment when Haptics library is available
    // Haptics.selectionAsync();
  }

  // Helper function to get the appropriate image source
  const getImageSource = useMemo(() => {
    return (item: any) => {
      // If no image URL is provided, use a placeholder
      if (!item.imageUrl) {
        return require('@/assets/images/placeholder-item.png');
      }

      // Check if the image URL is a local asset path
      if (typeof item.imageUrl === 'string' && item.imageUrl.startsWith('@/')) {
        // For local assets (using require)
        if (item.imageUrl === '@/assets/images/closet/closet-outfit-main2.png') {
          return require('@/assets/images/closet/closet-outfit-main2.png');
        } else if (item.imageUrl === '@/assets/images/closet/closet-outfit-main3.png') {
          return require('@/assets/images/closet/closet-outfit-main3.png');
        } else {
          return require('@/assets/images/placeholder-item.png');
        }
      }

      // For remote URLs (already processed by our centralized function)
      return { uri: item.imageUrl };
    };
  }, []);

  // This effect is no longer needed as we're using the getClothes hook

  // Memoize the filtered clothes to prevent unnecessary recalculations
  const filteredClothes = useMemo(() => {
    let filteredItems = [...clothes];

    // Apply category filter if a specific category is selected
    if (selectedCategory && selectedCategory.id !== 'all') {
      filteredItems = filteredItems.filter(item => {
        // Check if the item has a category object or just a category name
        if (item.category && typeof item.category === 'object') {
          return item.category.name === selectedCategory.name;
        } else if (item.category && typeof item.category === 'string') {
          return item.category === selectedCategory.name;
        } else if (item.categoryName) {
          return item.categoryName === selectedCategory.name;
        }
        return false;
      });
    }

    // Apply search filter if search text is provided
    if (search) {
      const searchLower = search.toLowerCase();
      filteredItems = filteredItems.filter(item => {
        const itemName = item.name ? item.name.toLowerCase() : '';
        const itemBrand = item.brand ? item.brand.toLowerCase() : '';
        const itemColor = item.color ? item.color.toLowerCase() : '';

        return itemName.includes(searchLower) ||
               itemBrand.includes(searchLower) ||
               itemColor.includes(searchLower);
      });
    }

    return filteredItems;
  }, [clothes, selectedCategory, search]);



  // Log when the modal visibility changes
  useEffect(() => {
    console.log('AddClothsModal isVisible:', isVisible);
  }, [isVisible]);

  // Handle saving the selected clothes
  const handleSaveClothes = () => {
    if (selectedClothes.length === 1) {
      // If only one item is selected, show the confirm-add-clothes modal
      const item = selectedClothes[0];
      if (item) {
        // Process the item to ensure it has all necessary fields
        const processedItem = {
          ...item,
          // Add default values for fields that might be missing
          color: item.color || '',
          size: item.size || '',
          material: item.material || '',
          category: item.category || '',
          brand: item.brand || '',
          price: item.price || '',
          tags: item.tags || '',
          purchaseDate: item.purchaseDate || '',
          lastWornDate: item.lastWornDate || '',
          timesWorn: item.timesWorn || '0',
          eventBought: item.eventBought || ''
        };

        console.log('Selected item for details:', processedItem);

        // Set the selected item and show the confirm modal
        setSelectedItem(processedItem);
        setIsConfirmModalVisible(true);
      } else {
        console.error('Selected item is null or undefined');
        Alert.alert('Error', 'Could not load item details. Please try again.');
      }
    } else if (selectedClothes.length > 1) {
      // If multiple items are selected, show a confirmation and proceed with onAdd
      Alert.alert(
        'Add Multiple Items',
        `Are you sure you want to add ${selectedClothes.length} items to your closet?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Add',
            onPress: () => {
              console.log('Adding multiple items:', selectedClothes);
              onAdd(selectedClothes);
              onClose();
            }
          },
        ]
      );
    }
  };

  // Handle successful save from the confirm modal
  const handleConfirmSave = () => {
    // Add the selected item to the closet
    if (selectedItem) {
      onAdd([selectedItem]);
    }
    // Reset state
    setSelectedItem(null);
    setIsConfirmModalVisible(false);
  };

  // Handle adding a new item from the SimpleAddNewItemModal
  const handleAddNewItem = (category: string) => {
    console.log('Adding new item with category:', category);

    // Create a temporary item with the selected category
    const newItem = {
      _id: `new-item-${Date.now()}`,
      name: `New ${category} Item`,
      category: { name: category },
      imageUrl: '',
      quantity: 1
    };

    // Add the new item to selected clothes
    addSelectedCloth(newItem);

    // Show the confirm modal to fill in details
    setSelectedItem(newItem);
    setIsConfirmModalVisible(true);
  };

  return <Modal
    style={{ justifyContent: 'flex-end', margin: 0 }}
    isVisible={isVisible}
    onBackButtonPress={onClose}
    onBackdropPress={onClose}
    useNativeDriver={true}
    hideModalContentWhileAnimating={true}
    animationIn="slideInUp"
    animationOut="slideOutDown"
    animationInTiming={300}
    animationOutTiming={300}
    backdropTransitionInTiming={300}
    backdropTransitionOutTiming={300}>

    <AddClothsModalContainer>
      <AddClothsModalHeader>
        <TouchableOpacity onPress={onClose}>
          <HeaderText>Close</HeaderText>
        </TouchableOpacity>
        <AddClothsModalTitle>Closet</AddClothsModalTitle>
        <TouchableOpacity
          onPress={handleSaveClothes}
          disabled={selectedClothes.length === 0}
        >
          <HeaderText style={{ opacity: selectedClothes.length === 0 ? 0.5 : 1 }}>
            Save {selectedClothes.length > 0 ? `(${selectedClothes.length})` : ''}
          </HeaderText>
        </TouchableOpacity>
      </AddClothsModalHeader>
      <View style={{ marginTop: 20 }}>
        <AddClothsModalInput placeholder="Search"
          value={search}
          onChangeText={setSearch}
        />
      </View>
      <View style={{ marginTop: 20 }}>
        <FlatList
          data={displayCategories}
          horizontal
          keyExtractor={(item) => item.id}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => {
            // Use a string key that's guaranteed to be unique
            const categoryKey = `category-${item.id}`;
            return <CategoryItem
              key={categoryKey}
              isSelected={selectedCategory.id === item.id}
              onPress={() => setSelectedCategory(item)}
            >
              <CategoryItemText isSelected={selectedCategory.id === item.id}>{item.name}</CategoryItemText>
            </CategoryItem>
          }}
        />
      </View>
      <ClothesContainer
        onLayout={handleLayout}
        style={{ marginTop: 20 }}>
        {isClothesLoading ? <ActivityIndicator size="large" /> : <FlatList
          keyExtractor={(item) => item._id}
          contentContainerStyle={{ paddingBottom: 200, paddingHorizontal: 8 }}
          data={[{ addItem: true }, ...filteredClothes]}
          numColumns={2}
          renderItem={({ item }) => {
            if (item.addItem) {
              return (
                <TouchableOpacity
                  key="add-item-container"
                  onPress={() => setIsAddNewItemModalVisible(true)}
                  activeOpacity={0.7}
                >
                  <AddClothItemContainer width={width} style={{
                    backgroundColor: '#E8F5F1',
                    borderWidth: 1.5,
                    borderColor: '#0E7E61',
                    borderStyle: 'dashed'
                  }}>
                    <PlusIcon size={32} color="#0E7E61" />
                    <ClothItemName style={{
                      color: '#0E7E61',
                      fontWeight: '600',
                      fontSize: 16,
                      marginTop: 12
                    }}>
                      Add New Item
                    </ClothItemName>
                  </AddClothItemContainer>
                </TouchableOpacity>
              )
            }
            // Create a unique key for each item
            const itemKey = item._id ? `item-${item._id}` : `item-${item.name}-${Math.random()}`;
            return (
              <TouchableOpacity
                key={itemKey}
                onPress={() => isItemSelected(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}
                activeOpacity={0.7}
              >
                <ClothItemContainer width={width} isSelected={isItemSelected(item)}>
                  {/* Display image with proper error handling */}
                  <ClothItemImage
                    source={getImageSource(item)}
                    resizeMode="contain"
                    onLoadStart={() => {
                      // Set loading state
                      setImageLoadingStates(prev => ({ ...prev, [item._id]: true }));
                    }}
                    onLoadEnd={() => {
                      // Clear loading state with a small delay to ensure smooth transition
                      setTimeout(() => {
                        setImageLoadingStates(prev => ({ ...prev, [item._id]: false }));
                      }, 300);
                    }}
                    onError={(e: any) => {
                      console.error(`Image error for ${item.name}:`, e.nativeEvent?.error || 'Unknown error');
                      setImageLoadingStates(prev => ({ ...prev, [item._id]: false }));
                    }}
                  />

                  {/* Show item name below the image */}
                  <ClothItemName numberOfLines={1}>{item.name}</ClothItemName>

                  {/* Loading indicator */}
                  {imageLoadingStates[item._id] && (
                    <View style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(240, 240, 240, 0.7)',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 8
                    }}>
                      <ActivityIndicator size="small" color="#0E7E61" />
                    </View>
                  )}

                  <Checkbox
                    checked={isItemSelected(item)}
                    onToggle={() => isItemSelected(item) ? removeSelectedCloth(item) : addSelectedCloth(item)}
                    size={26}
                    containerStyle={{ position: 'absolute', bottom: 12, right: 12 }}
                  />
                </ClothItemContainer>
              </TouchableOpacity>
            )
          }}
        />}
      </ClothesContainer>
    </AddClothsModalContainer>
    {/* Confirm Add Clothes Modal */}
    <ConfirmAddClothes
      isVisible={isConfirmModalVisible}
      onClose={() => setIsConfirmModalVisible(false)}
      item={selectedItem}
      onSave={handleConfirmSave}
    />

    {/* Simple Add New Item Modal */}
    <SimpleAddNewItemModal
      isVisible={isAddNewItemModalVisible}
      onClose={() => setIsAddNewItemModalVisible(false)}
      onAddItem={handleAddNewItem}
    />
  </Modal>;
}

// Add displayName to the component
AddClothsModal.displayName = 'AddClothsModal';

// Export the component wrapped in React.memo for better performance
export default React.memo(AddClothsModal);
