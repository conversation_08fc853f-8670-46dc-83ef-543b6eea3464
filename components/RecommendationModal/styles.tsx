import { Text, View } from 'react-native';
import styled, { css, DefaultTheme } from 'styled-components';


const DefaultText = css`
  font-family: MuktaVaaniLight;
  font-size: 16px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[600]};
`;
export const HeaderTextRecommendation = styled(Text)`
  text-align: center;
  font-family: 'MuktaVaaniBold';
  font-size: 20;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
`;
export const HeaderViewRecommendation = styled(View)`
  flex: 1;
  align-items: center;
  justify-content: center;
`;

export const ContentContainer = styled(View)`
  align-items: center;
  justify-content: center;
  margin-top: 24px;
`;

export const ContentText = styled(Text)`
  ${DefaultText}
`;

export const ContentImageContainer = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

export const ContentItemContainer = styled(View)`
  background-color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[50]};
  border-radius: 8px;
  margin-top: 10px;
  padding: 16px;
`;

export const ContentItemTitle = styled(Text)`
  ${DefaultText}
  margin-bottom: 8px;
`;

export const ContentItemDescription = styled(Text)`
  ${DefaultText}
  font-size: 14px;
  flex: 1;
  margin-left: 4px;
`;

export const ContentItemColor = styled(Text)`
  ${DefaultText}
  font-family:'MuktaVaaniRegular';
  font-size: 14px;
`;

export const ContentItemBrand = styled(Text)`
  ${DefaultText}
`;

