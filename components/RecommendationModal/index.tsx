import { FlatList, Image, Text, TouchableOpacity, View } from "react-native";
import Modal from "react-native-modal";
import { HeaderText } from '../AddItems/styles';
import { TemplateModalContainer, TemplateModalHeader } from '../TemplateListModal/styles';
// To be remove after
import Tshirt from '@/assets/images/tshirt.png';
import { packingList } from '@/constants/strings';
import { ContentContainer, ContentImageContainer, ContentItemBrand, ContentItemColor, ContentItemContainer, ContentItemDescription, ContentItemTitle, ContentText, HeaderTextRecommendation, HeaderViewRecommendation } from './styles';

interface RecommendationModalProps {
  isVisible: boolean;
  onClose: () => void;
}

// To be remove after
const RecommendationArray = [
  {
    title: "T-Shirt",
    image: "Img",
    color: "Black",
    brand: "Formcloud",
    description: "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    title: "T-Shirt",
    image: "Img",
    color: "Black",
    brand: "Formcloud",
    description: "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
  {
    title: "T-Shirt",
    image: "Img",
    color: "Black",
    brand: "Formcloud",
    description: "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
  },
]
const RecommendationModal: React.FC<RecommendationModalProps> = ({ isVisible, onClose }) => {
  const renderHeader = () => {
    return (
      <TemplateModalHeader>
        <TouchableOpacity onPress={onClose} style={{ width: 50 }}>
          <HeaderText>{packingList.back}</HeaderText>
        </TouchableOpacity>
        <HeaderViewRecommendation>
          {/* Myuse Recommendations: Sports Bras --> To be edit */}
          <HeaderTextRecommendation>{packingList.myuseRecommendations}{"\n"} Sports Bras</HeaderTextRecommendation>
        </HeaderViewRecommendation>
        <View style={{ width: 50 }} />
      </TemplateModalHeader>
    );
  };

  const renderContent = () => {
    return (
      <ContentContainer>
        <ContentText>{packingList.myuseRecommendationsText}</ContentText>
        <View style={{ width: '100%' }}>
          <FlatList
            data={RecommendationArray}
            showsVerticalScrollIndicator={false}
            ListFooterComponent={<View style={{ width: '100%', height: 100 }} />}
            renderItem={({ item }) => (
              <ContentItemContainer>
                <ContentItemTitle>{item.title}</ContentItemTitle>
                <ContentImageContainer>
                  <View style={{ width: 100, height: 100 }}>
                    {/* To be change after */}
                    <Image source={Tshirt} style={{ width: 100, height: 100 }} />
                  </View>
                  <ContentItemDescription>{item.description}</ContentItemDescription>
                </ContentImageContainer>
                <ContentItemColor>{item.color}</ContentItemColor>
                <ContentItemBrand>{item.brand}</ContentItemBrand>
              </ContentItemContainer>
            )}
          />
        </View>
      </ContentContainer>
    );
  };

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <TemplateModalContainer>
        {renderHeader()}
        {renderContent()}
      </TemplateModalContainer>
    </Modal>
  );
};

export default RecommendationModal;