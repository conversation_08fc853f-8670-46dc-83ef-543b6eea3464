import HelpIcon from '@/assets/svg/help.svg';
import React, { FC } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Checkbox } from '../common/Checkbox';
import { CheckboxContainer } from './styles';

interface KeywordsListProps {
  keywords: string[];
  selectedKeywords: string[];
  onToggleKeyword: (selectedKeywords: string[]) => void;
}

export const KeywordsList: FC<KeywordsListProps> = ({
  keywords = [],
  selectedKeywords = [],
  onToggleKeyword,
}: KeywordsListProps) => {
  const handleToggle = (keyword: string) => {
    if (selectedKeywords.includes(keyword)) {
      // If keyword is already selected, remove it
      const newSelectedKeywords = selectedKeywords.filter((k) => k !== keyword);
      onToggleKeyword(newSelectedKeywords);
    } else {
      // If keyword is not selected
      const newSelectedKeywords = [...selectedKeywords];
      if (newSelectedKeywords.length >= 5) {
        // Remove the first (earliest) keyword if limit is reached
        newSelectedKeywords.shift();
      }
      newSelectedKeywords.push(keyword);
      onToggleKeyword(newSelectedKeywords);
    }
  };

  return (
    <View>
      {keywords.map((keyword) => (
        <KeywordCheckbox
          key={keyword}
          label={keyword}
          checked={selectedKeywords.includes(keyword)}
          onToggle={() => handleToggle(keyword)}
        />
      ))}
    </View>
  );
};

interface KeywordCheckboxProps {
  label: string;
  checked?: boolean;
  onToggle?: () => void;
}

const KeywordCheckbox: FC<KeywordCheckboxProps> = ({
  label,
  checked,
  onToggle,
}) => {
  return (
    <CheckboxContainer>
      <Checkbox label={label} checked={checked} onToggle={onToggle} />
      <TouchableOpacity activeOpacity={1}>
        <HelpIcon height={18} width={18} />
      </TouchableOpacity>
    </CheckboxContainer>
  );
};
