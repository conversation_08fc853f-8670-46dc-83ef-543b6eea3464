import { brand } from '@/constants/Colors';
import styled from 'styled-components/native';

export const PlanCardView = styled.View`
  background-color: ${brand.green[500]};
  padding: 16px;
  border-radius: 16px;
`;

export const PlanCardText = styled.Text`
  color: #fff;
  font-family: 'MuktaVaani';
  font-size: 14px;
  margin-bottom: 4px;
`;

export const CurrentPlanView = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 4px;
  border-bottom-width: 1px;
  border-bottom-color: #ffffff;
  padding-bottom: 8px;
  margin-bottom: 8px;
`;

export const CurrentPlanText = styled.Text`
  color: #fff;
  font-family: 'MuktaVaaniSemiBold';
  font-size: 20px;
`;
