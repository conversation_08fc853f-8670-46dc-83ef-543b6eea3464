import Tag from '@/assets/svg/tag.svg';
import { View } from 'react-native';
import Button from '../common/Button';
import {
  CurrentPlanText,
  CurrentPlanView,
  PlanCardText,
  PlanCardView,
} from './style';

export const PlanCard = () => {
  return (
    <PlanCardView>
      <PlanCardText>Your current plan</PlanCardText>
      <CurrentPlanView>
        <Tag width={30} />
        <CurrentPlanText>Free</CurrentPlanText>
      </CurrentPlanView>
      <PlanCardText>Description here</PlanCardText>
      <View style={{ marginTop: 8 }}>
        <Button isInverted title="Upgrade to Premium" />
      </View>
    </PlanCardView>
  );
};
