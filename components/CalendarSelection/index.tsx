import { router } from 'expo-router';
import { CalendarDays } from 'lucide-react-native';
import moment from 'moment';
import { CalendarSelectionContainer, CalendarSelectionText } from './styles';

export default function CalendarSelection() {
  return (
    <CalendarSelectionContainer>
      <CalendarSelectionText>
        {moment().format('dddd, D MMMM')}
      </CalendarSelectionText>
      <CalendarDays color="#000" size={24} onPress={() => router.navigate('/my-calendar')} />
    </CalendarSelectionContainer>
  );
}
