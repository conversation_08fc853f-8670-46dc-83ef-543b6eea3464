import React, { useState } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
  useAnimatedGestureHandler,
} from 'react-native-reanimated';
import { PanGestureHandler } from 'react-native-gesture-handler';
import SubItem from './SubItem';

const EnhancedDraggableItem = ({ item, index, onDragEnd, parentId = null }) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const isDragging = useSharedValue(false);
  const [expanded, setExpanded] = useState(false);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startX = translateX.value;
      context.startY = translateY.value;
      isDragging.value = true;
    },
    onActive: (event, context) => {
      translateX.value = context.startX + event.translationX;
      translateY.value = context.startY + event.translationY;
    },
    onEnd: (event) => {
      isDragging.value = false;
      runOnJS(onDragEnd)({
        id: item.id,
        parentId,
        index,
        position: { x: event.translationX, y: event.translationY },
      });
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: isDragging.value ? 1.05 : 1 },
      ],
      zIndex: isDragging.value ? 100 : 1,
      shadowOpacity: isDragging.value ? 0.2 : 0,
    };
  });

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <View style={styles.container}>
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[styles.item, animatedStyle]}>
          <Text style={styles.itemText}>{item.title}</Text>
          {item.subItems && item.subItems.length > 0 && (
            <Text style={styles.expandButton} onPress={toggleExpand}>
              {expanded ? '▼' : '►'}
            </Text>
          )}
        </Animated.View>
      </PanGestureHandler>

      {expanded && item.subItems && item.subItems.length > 0 && (
        <View style={styles.subItemsContainer}>
          {item.subItems.map((subItem, subIndex) => (
            <SubItem
              key={subItem.id}
              item={subItem}
              index={subIndex}
              onDragEnd={onDragEnd}
              parentId={item.id}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  item: {
    backgroundColor: 'white',
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    elevation: 3,
  },
  itemText: {
    fontSize: 16,
    fontWeight: '500',
  },
  expandButton: {
    fontSize: 18,
    color: '#666',
    padding: 5,
  },
  subItemsContainer: {
    marginLeft: 30,
    paddingVertical: 4,
  },
});

export default EnhancedDraggableItem;
