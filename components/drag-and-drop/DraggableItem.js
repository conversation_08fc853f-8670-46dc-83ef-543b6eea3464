import React, { useState } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { PanGestureHandler } from 'react-native-gesture-handler';

const DraggableItem = ({ item, index, onDragEnd, parentId = null }) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const isDragging = useSharedValue(false);
  const [expanded, setExpanded] = useState(false);

  const handleDragEnd = (x, y) => {
    'worklet';
    runOnJS(onDragEnd)({
      id: item.id,
      parentId,
      index,
      position: { x, y },
    });
  };

  const panGestureEvent = (event) => {
    'worklet';
    isDragging.value = true;
    translateX.value = event.translationX;
    translateY.value = event.translationY;

    if (event.state === 5) { // State.END
      isDragging.value = false;
      handleDragEnd(event.translationX, event.translationY);
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: isDragging.value ? 1.05 : 1 },
      ],
      zIndex: isDragging.value ? 100 : 1,
      shadowOpacity: isDragging.value ? 0.2 : 0,
    };
  });

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  return (
    <View style={styles.container}>
      <PanGestureHandler onGestureEvent={panGestureEvent}>
        <Animated.View style={[styles.item, animatedStyle]}>
          <Text style={styles.itemText}>{item.title}</Text>
          {item.subItems && item.subItems.length > 0 && (
            <Text style={styles.expandButton} onPress={toggleExpand}>
              {expanded ? '▼' : '►'}
            </Text>
          )}
        </Animated.View>
      </PanGestureHandler>

      {expanded && item.subItems && item.subItems.length > 0 && (
        <View style={styles.subItemsContainer}>
          {item.subItems.map((subItem, subIndex) => (
            <DraggableItem
              key={subItem.id}
              item={subItem}
              index={subIndex}
              onDragEnd={onDragEnd}
              parentId={item.id}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  item: {
    backgroundColor: 'white',
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    elevation: 3,
  },
  itemText: {
    fontSize: 16,
    fontWeight: '500',
  },
  expandButton: {
    fontSize: 18,
    color: '#666',
  },
  subItemsContainer: {
    marginLeft: 30,
  },
});

export default DraggableItem;
