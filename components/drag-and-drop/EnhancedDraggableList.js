import React, { useState, useCallback } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import EnhancedDraggableItem from './EnhancedDraggableItem';

const EnhancedDraggableList = ({ initialData, setItems }) => {
  const [items, setLocalItems] = useState(initialData);

  const handleDragEnd = useCallback(
    ({ id, parentId, index, position }) => {
      // Calculate the new position based on the drag distance
      const moveDistance = Math.round(position.y / 80); // Approximate height of an item

      if (moveDistance === 0) return; // No movement needed

      const newItems = [...items];

      // Handle nested item reordering
      if (parentId) {
        const parentIndex = newItems.findIndex((item) => item.id === parentId);
        if (parentIndex === -1) return;

        const parentItem = newItems[parentIndex];
        const newSubItems = [...parentItem.subItems];
        const draggedItem = newSubItems[index];

        // Calculate new index with bounds checking
        const newIndex = Math.max(
          0,
          Math.min(index + moveDistance, newSubItems.length - 1),
        );

        // Remove from old position and insert at new position
        newSubItems.splice(index, 1);
        newSubItems.splice(newIndex, 0, draggedItem);

        // Update parent item with new subItems
        newItems[parentIndex] = {
          ...parentItem,
          subItems: newSubItems,
        };
      }
      // Handle top-level item reordering
      else {
        const draggedItem = newItems[index];

        // Calculate new index with bounds checking
        const newIndex = Math.max(
          0,
          Math.min(index + moveDistance, newItems.length - 1),
        );

        // Remove from old position and insert at new position
        newItems.splice(index, 1);
        newItems.splice(newIndex, 0, draggedItem);
      }

      setLocalItems(newItems);
      setItems(newItems);
    },
    [items, setItems],
  );

  return (
    <ScrollView
      style={styles.container}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <View style={styles.listContainer}>
        {items.map((item, index) => (
          <EnhancedDraggableItem
            key={item.id}
            item={item}
            index={index}
            onDragEnd={handleDragEnd}
          />
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    paddingVertical: 8,
  },
});

export default EnhancedDraggableList;
