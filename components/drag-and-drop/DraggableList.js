import React, { useState, useCallback } from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import DraggableItem from './DraggableItem';

const DraggableList = ({ initialData }) => {
  const [items, setItems] = useState(initialData);

  const handleDragEnd = useCallback(({ id, parentId, index, position }) => {
    // Calculate the new position based on the drag distance
    const moveDistance = Math.round(position.y / 80); // Approximate height of an item

    if (moveDistance === 0) return; // No movement needed

    setItems((prevItems) => {
      // Handle nested item reordering
      if (parentId) {
        return prevItems.map((item) => {
          if (item.id === parentId) {
            const newSubItems = [...item.subItems];
            const draggedItem = newSubItems[index];

            // Calculate new index with bounds checking
            const newIndex = Math.max(
              0,
              Math.min(index + moveDistance, newSubItems.length - 1),
            );

            // Remove from old position and insert at new position
            newSubItems.splice(index, 1);
            newSubItems.splice(newIndex, 0, draggedItem);

            return {
              ...item,
              subItems: newSubItems,
            };
          }
          return item;
        });
      }
      // Handle top-level item reordering
      else {
        const newItems = [...prevItems];
        const draggedItem = newItems[index];

        // Calculate new index with bounds checking
        const newIndex = Math.max(
          0,
          Math.min(index + moveDistance, newItems.length - 1),
        );

        // Remove from old position and insert at new position
        newItems.splice(index, 1);
        newItems.splice(newIndex, 0, draggedItem);

        return newItems;
      }
    });
  }, []);

  return (
    <ScrollView
      style={styles.container}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <View style={styles.listContainer}>
        {items.map((item, index) => (
          <DraggableItem
            key={item.id}
            item={item}
            index={index}
            onDragEnd={handleDragEnd}
          />
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    paddingVertical: 8,
  },
});

export default DraggableList;
