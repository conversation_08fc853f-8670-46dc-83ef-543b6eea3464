import { RewardCard } from '@/components/RewardCard';
import { getRewards } from '@/methods/rewards';
import { FC, useEffect, useState } from 'react';
import { GridContainer } from '../GridContainer';

type RewardsGridProps = {
  itemsToShow?: number;
};

export const RewardsGrid: FC<RewardsGridProps> = ({
  itemsToShow,
}: RewardsGridProps) => {
  const [rewards, setRewards] = useState<any[]>([]);
  const { data } = getRewards();

  useEffect(() => {
    if (data && data.success) {
      setRewards(data.data.rewards);
    }
  }, [data]);

  return (
    <GridContainer itemsToShow={itemsToShow}>
      {rewards.length > 0 &&
        rewards.map((reward) => (
          <RewardCard
            key={reward._id}
            reward={reward}
          />
        ))}
    </GridContainer>
  );
};
