import React from 'react';
import { ActivityIndicator, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import styled from 'styled-components/native';

// Styled components
const Container = styled.SafeAreaView`
  flex: 1;
  background-color: white;
  width: 100%;
  height: 100%;
`;

const BackButton = styled.TouchableOpacity`
  position: absolute;
  top: 60px;
  left: 16px;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  background-color: #F5F5F5;
  border-radius: 20px;
  z-index: 999;
`;

const ScrollContent = styled.ScrollView`
  flex: 1;
  width: 100%;
`;

const ContentContainer = styled.View`
  width: 100%;
  max-width: 343px;
  margin-top: 20px;
  padding-left: 16px;
  padding-right: 16px;
  align-self: center;
  padding-top: 20px;
  padding-bottom: 40px;
`;

const HeaderContainer = styled.View`
  width: 279px;
  align-self: center;
  margin-bottom: 40px;
  margin-top: 20px;
  justify-content: center;
  align-items: center;
`;

const HeaderText = styled.Text`
  font-family: CormorantGaramondSemiBold;
  font-size: 26px;
  line-height: 32px;
  letter-spacing: 0px;
  text-align: center;
  color: #0E7E61;
  width: 279px;
  padding-left: 16px;
  padding-right: 16px;
`;

const ImageContainer = styled.View`
  width: 240px;
  height: 240px;
  background-color: #EBEBEB;
  border-radius: 8px;
  margin-bottom: 40px;
  overflow: hidden;
  align-self: center;
`;

const ItemImage = styled.Image`
  width: 100%;
  height: 100%;
`;

const ActionButton = styled.TouchableOpacity`
  width: 140px;
  height: 44px;
  background-color: #0E7E61;
  border-radius: 99px;
  justify-content: center;
  align-items: center;
  align-self: center;
  margin-bottom: 40px;
`;

const ButtonText = styled.Text`
  color: white;
  font-size: 16px;
  font-family: 'MuktaVaaniSemiBold';
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  min-height: 300px;
`;

const ErrorText = styled.Text`
  color: red;
  font-size: 16px;
  text-align: center;
  margin: 20px;
`;

const DetailsContainer = styled.View`
  margin-top: 20px;
`;

const DetailRow = styled.View`
  flex-direction: row;
  margin-bottom: 12px;
`;

const DetailLabel = styled.Text`
  width: 80px;
  font-size: 14px;
  font-family: 'MuktaVaaniSemiBold';
  color: #333;
`;

const DetailValue = styled.Text`
  flex: 1;
  font-size: 14px;
  color: #666;
`;

interface ItemDetailsModalProps {
  title: string;
  imageUrl: string | null;
  isLoading?: boolean;
  error?: string | null;
  details?: {
    label: string;
    value: string;
  }[];
  actionButtonText?: string;
  onActionButtonPress?: () => void;
  onBackPress?: () => void;
}

export const ItemDetailsModal: React.FC<ItemDetailsModalProps> = ({
  title,
  imageUrl,
  isLoading = false,
  error = null,
  details = [],
  actionButtonText = 'Wear It',
  onActionButtonPress,
  onBackPress,
}) => {
  const router = useRouter();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <Container>
      {/* Back Button */}
      <BackButton onPress={handleBackPress} activeOpacity={0.7}>
        <ArrowLeft size={24} color="#333333" />
      </BackButton>

      <ScrollContent
        contentContainerStyle={{
          paddingTop: 120,
          paddingBottom: 40,
          width: '100%',
          alignItems: 'center',
        }}
        showsVerticalScrollIndicator={false}
      >
        {isLoading ? (
          <LoadingContainer>
            <ActivityIndicator size="large" color="#0E7E61" />
          </LoadingContainer>
        ) : error ? (
          <ErrorText>{error}</ErrorText>
        ) : (
          <ContentContainer>
            {/* Item Name Header */}
            <HeaderContainer>
              <HeaderText numberOfLines={2} ellipsizeMode="tail">
                {title || 'Item Details'}
              </HeaderText>
            </HeaderContainer>

            {/* Item Image */}
            <ImageContainer>
              {imageUrl ? (
                <ItemImage
                  source={{ uri: imageUrl }}
                  resizeMode="cover"
                  onError={() => {
                    console.error('Error loading image');
                    console.log('Failed image URL:', imageUrl);
                  }}
                  onLoad={() => {
                    console.log('Image loaded successfully from URL:', imageUrl);
                  }}
                />
              ) : (
                <ItemImage
                  source={require('@/assets/images/placeholder-item.png')}
                  resizeMode="cover"
                />
              )}
            </ImageContainer>

            {/* Action Button */}
            {onActionButtonPress && (
              <ActionButton onPress={onActionButtonPress} activeOpacity={0.7}>
                <ButtonText>{actionButtonText}</ButtonText>
              </ActionButton>
            )}

            {/* Item Details */}
            {details.length > 0 && (
              <DetailsContainer>
                {details.map((detail, index) => (
                  <DetailRow key={index}>
                    <DetailLabel>{detail.label}:</DetailLabel>
                    <DetailValue>{detail.value}</DetailValue>
                  </DetailRow>
                ))}
              </DetailsContainer>
            )}
          </ContentContainer>
        )}
      </ScrollContent>
    </Container>
  );
};

export default ItemDetailsModal;
