import { DefaultTheme } from 'styled-components';
import styled from 'styled-components/native';


export const HeaderView = styled.View`
  flex: 1;
  align-items: flex-start;
`;
export const HeaderText = styled.Text`
  font-size: 18px;
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.gray[900]};
  margin-bottom: 8px;
  font-family: 'MuktaVaaniSemiBold';
`;
export const HeaderLine = styled.View`
  width: 100%;
  height: 1;
  background-color: #c7c7c7;
`;
export const DayContainer = styled.View`
  border-radius: 20px;
  background-color: transparent;
  align-items: center;
  justify-content: center;
`;
export const DayText = styled.Text`
  font-family: 'MuktaVaaniMedium';
  font-size: 16px;
  text-align: center;
  border-radius: 50px;
  height: 24px;
  width: 24px;
`;
export const MarkedView = styled.View`
  width: 5px;
  height: 5px;
  border-radius: 5px;
  margin-top: 2px;
`;

