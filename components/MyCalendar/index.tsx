import { getUpcomingTrips } from '@/methods/trips';
import { FontAwesome } from '@expo/vector-icons';
import { router } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { CalendarList } from 'react-native-calendars';
import { DayState } from 'react-native-calendars/src/types';
import { useTheme } from 'styled-components';
import { DayContainer, DayText, HeaderLine, HeaderText, HeaderView, MarkedView } from './style';
interface Trip {
  start: string;
  end: string;
  dates: string[];
  color: string;
  name: string;
}
interface MyCalendarProps {
  trips: Trip[];
  today: string;
}

export default function MyCalendarComponent({ trips, today }: MyCalendarProps) {
  const theme = useTheme();
  const { data: tripsData, refetch, isLoading } = getUpcomingTrips();

  const handlePress = (tripMatch: any) => {
    const tripData = tripsData?.data?.events?.find((trip: any) => trip?.name === tripMatch?.name);
    if (tripData) {
      router.push(`/trip-overview/${tripData?._id}`);
    }
  }
  const renderDay = (day: any, state: DayState | undefined) => {
    const date = day?.dateString;

    const tripMatch = trips.find(
      trip =>
        trip.start === date || trip.end === date || trip.dates.includes(date)
    );
    const isStartOrEnd = tripMatch?.start === date || tripMatch?.end === date;
    const isMarked = tripMatch?.dates?.includes(date);
    const isToday = date === today;
    const bgColor =
      isToday
        ? theme.brand.green[500]
        : isStartOrEnd
          ? theme.brand.magenta
          : 'transparent';


    const textColor =
      state === 'disabled'
        ? theme.brand.gray[500]
        : isToday || isStartOrEnd
          ? theme.brand.gray[50]
          : theme.brand.gray[900];

    return (
      <TouchableOpacity onPress={() => handlePress(tripMatch)}>
        <DayContainer>
          <DayText style={{ color: textColor, backgroundColor: bgColor }}>
            {day.day}
          </DayText>
          {isStartOrEnd && <FontAwesome
            name="suitcase"
            size={16}
            color={theme.brand.magenta}
          />}
          {isMarked && !isStartOrEnd && <MarkedView style={{ backgroundColor: theme.brand.magenta }} />}
        </DayContainer>
      </TouchableOpacity>

    );
  };

  return (
    <CalendarList
      // ref={calendarRef}
      current={today}
      // calendarWidth={WINDOW_WIDTH}
      // Show 12 months, scrollable
      pastScrollRange={12}
      // futureScrollRange={12}
      scrollEnabled={true}
      showScrollIndicator={false}
      // Scroll vertically
      horizontal={false}
      hideArrows={true}
      // calendarHeight={300}
      // Optional styling
      calendarHeight={300} // fixed height for all months
      theme={{
        textDayHeaderFontFamily: 'MuktaVaaniMedium',
        textDayHeaderFontWeight: '400',
        textDayHeaderFontSize: 16,
        calendarBackground: '#EBEBEB',
        textSectionTitleColor: theme.brand.gray[500],
        textMonthFontWeight: 'bold',
        monthTextColor: 'transparent',
        arrowColor: 'transparent',

      }}
      renderHeader={(date) => {
        const monthName = date.toString('MMMM yyyy').toUpperCase();
        return (
          <HeaderView>
            <HeaderText>{monthName}</HeaderText>
            <HeaderLine />
          </HeaderView>
        );
      }}
      dayComponent={({ date, state }) => {
        return renderDay(date, state)
      }}
    />
  );
}