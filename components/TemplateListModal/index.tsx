import { packingList } from '@/constants/strings';
import { getTemplate } from '@/methods/trips';
import { Check, SearchIcon } from 'lucide-react-native';
import { useCallback, useMemo, useRef, useState } from 'react';
import { ActivityIndicator, FlatList, GestureResponderEvent, LayoutChangeEvent, Text, TextInput, TouchableOpacity, View } from 'react-native';
import Modal from 'react-native-modal';
import { useTheme } from 'styled-components';
import { SubtitleText, TitleSection } from '../common/Text';
import GenericDropdown, { DropdownOption, GenericDropdownRef } from '../GenericDropdown';
import {
  ContentItemButton,
  ContentItemButtonText,
  ModalCloseButtonText,
  ModalViewText,
  ModalViewTextButton,
  ModalViewTextButtonText,
  ModalViewTextDescription,
  ModalViewTextTitle,
  RecommendationsModal,
  RecommendationsModalCloseButton,
} from '../OverviewItem/styles';
import {
  EmptyIconView,
  EmptyStateContainer,
  HeaderText,
  SearchBar,
  SearchBarContainer,
  SortButton,
  SortContainer,
  TemplateImage,
  TemplateItemCheckbox,
  TemplateItemContainer,
  TemplateItemName,
  TemplateItemRow,
  TemplateItemsList,
  TemplateModalContainer,
  TemplateModalHeader,
  TemplatePreviewContainer,
  TemplatePreviewDescription,
  TemplatePreviewHeader,
  TemplatePreviewItemName,
  TemplatePreviewTitle,
  TemplatesContainer
} from './styles';
interface TemplateItem {
  id: string;
  name: string;
  quantity?: number;
  isChecked?: boolean;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  image: string;
  items: TemplateItem[];
  createdAt: string;
}

interface TemplateListModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelect: (template: Template) => void;
  setShowEmptyState: (show: boolean) => void;
  setShowRecommendations: (show: boolean) => void;
  isShowRecommendations: boolean;
}

type ViewMode = 'list' | 'preview';

const SORT_OPTIONS: DropdownOption[] = [
  {
    label: 'Newest',
    value: 'Newest',
  },
  {
    label: 'Ascending',
    value: 'Ascending',
  },
  {
    label: 'Descending',
    value: 'Descending',
  },
  // {
  //   label: 'Featured',
  //   value: 'Featured',
  // },
  // {
  //   label: 'A-Z',
  //   value: 'AZ',
  // },
  // {
  //   label: 'Z-A',
  //   value: 'ZA',
  // },
  // {
  //   label: 'Most Popular',
  //   value: 'Most Popular',
  // },

];

export default function TemplateListModal({
  isVisible,
  onClose,
  onSelect,
  setShowEmptyState,
  setShowRecommendations,
  isShowRecommendations }: TemplateListModalProps) {
  const { data: templates, isLoading } = getTemplate();
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [width, setWidth] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<any>({ id: 'all', name: 'All', category: 'All' });
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const [lastToggledItem, setLastToggledItem] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };
  const theme = useTheme();
  const [sort, setSort] = useState<'Ascending' | 'Descending' | 'Newest'>('Newest');
  const genderDropdownRef = useRef<GenericDropdownRef>(null);

  const getFilteredTemplates = () => {
    if (selectedCategory && selectedCategory.category !== 'All') {
      return templates?.packingListTemplates?.filter((item: Template) => item.category === selectedCategory.category);
    }
    return templates?.packingListTemplates;
  };

  const handleSelectTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setCheckedItems(template.items.map(item => item.name));
    setViewMode('preview');
    setShowEmptyState(false);
  };

  const handleUseTemplate = useCallback(() => {
    if (selectedTemplate) {

      const newSelectedTemplate = { ...selectedTemplate };

      const selectedItems = newSelectedTemplate.items.filter(item => checkedItems.includes(item.name));

      newSelectedTemplate.items = selectedItems;
      onSelect(newSelectedTemplate);
      setSelectedTemplate(null);
      setCheckedItems([]);
      setViewMode('list');
      onClose();
    }
  }, [selectedTemplate, checkedItems, onSelect, onClose]);

  const handleBack = () => {
    if (viewMode === 'preview') {
      setViewMode('list');
      setSelectedTemplate(null);
    } else {
      onClose();
    }
  };

  const toggleItemCheck = useCallback((itemId: string) => {
    let newCheckedItems = [...checkedItems];
    if (newCheckedItems.includes(itemId)) {
      newCheckedItems.splice(newCheckedItems.indexOf(itemId), 1);
    } else {
      newCheckedItems = [...newCheckedItems, itemId];
    }
    setCheckedItems([...newCheckedItems]);
    setLastToggledItem(itemId);
  }, [checkedItems]);

  const toggleAllItems = useCallback(() => {
    if (!selectedTemplate?.items) return;

    setLastToggledItem(null);
    setCheckedItems(prev => {
      // If all items are checked, uncheck all
      const allItemsChecked = selectedTemplate.items.every(item => prev.includes(item.id));
      if (allItemsChecked) {
        return [];
      }
      // Otherwise, check all items
      return selectedTemplate.items.map(item => item.id);
    });
  }, [selectedTemplate?.items]);

  const getCheckedCount = useCallback(() => {
    return checkedItems.length;
  }, [checkedItems]);

  const getTotalItems = useCallback(() => {
    return selectedTemplate?.items?.length || 0;
  }, [selectedTemplate?.items]);

  const isAllChecked = useCallback(() => {
    if (!selectedTemplate?.items?.length) return false;
    return selectedTemplate.items.every(item => checkedItems.includes(item.id));
  }, [selectedTemplate?.items, checkedItems]);

  const isPartiallyChecked = useCallback(() => {
    if (!selectedTemplate?.items?.length) return false;
    const checkedCount = getCheckedCount();
    return checkedCount > 0 && checkedCount < getTotalItems();
  }, [selectedTemplate?.items, getCheckedCount, getTotalItems]);

  const handleRowPress = (itemId: string) => {
    toggleItemCheck(itemId);
  };

  const renderHeader = () => (
    <TemplateModalHeader>
      <TouchableOpacity onPress={handleBack}>
        <HeaderText>{viewMode === 'preview' ? 'Back' : 'Close'}</HeaderText>
      </TouchableOpacity>

      {viewMode === 'preview' && (
        <TouchableOpacity onPress={handleUseTemplate}>
          <HeaderText>{packingList.useTemplate}</HeaderText>
        </TouchableOpacity>
      )}
      {viewMode === 'list' && <View style={{ width: 50 }} />}
    </TemplateModalHeader>
  );


  // search & sort function
  const filteredTemplates = useMemo(() => {
    return getFilteredTemplates()?.length > 0 ?
      getFilteredTemplates()
        .filter((item: Template) =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .sort((a: Template, b: Template) => {
          if (sort === 'Newest') {
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          } else if (sort === 'Ascending') {
            return a.name.localeCompare(b.name);
          } else if (sort === 'Descending') {
            return b.name.localeCompare(a.name);
          }
          return 0;
        }) : [];

  }, [getFilteredTemplates(), searchQuery, sort]);

  const renderContent = () => {
    if (viewMode === 'preview' && selectedTemplate) {
      return (
        <TemplatePreviewContainer>
          <TemplatePreviewHeader>
            <TemplatePreviewTitle>{selectedTemplate.name}</TemplatePreviewTitle>
            <TemplatePreviewDescription>{selectedTemplate.description}</TemplatePreviewDescription>
          </TemplatePreviewHeader>

          <TemplateItemsList>
            {selectedTemplate.items?.map((item) => {

              const isChecked = checkedItems.includes(item.name);
              const isLastToggled = lastToggledItem === item.name;

              return (
                <TemplateItemRow
                  key={`${item.id}-${item.name}`}
                  onPress={() => handleRowPress(item.name)}
                  activeOpacity={0.7}
                >
                  <TemplateItemCheckbox
                    isChecked={isChecked}
                    onPress={(e: GestureResponderEvent) => {
                      e.stopPropagation();
                      toggleItemCheck(item.name);
                    }}
                  >
                    {isChecked && (
                      <Check
                        size={16}
                        color={theme.brand.gray[50]}
                        strokeWidth={3}
                        style={{
                          transform: [{ scale: isLastToggled ? 1.1 : 1 }],
                        }}
                      />
                    )}
                  </TemplateItemCheckbox>
                  <TemplatePreviewItemName
                    style={{
                      color: isChecked ? theme.brand.gray[500] : theme.brand.gray[800],
                    }}
                  >
                    {item.name}
                  </TemplatePreviewItemName>
                  {/* Recommendations Button -- Need to check the items if its under on recommendations*/}
                  {/* For test purpose only */}
                  {item.name === "Sports Bras" && (
                    <ContentItemButton onPress={() => setShowRecommendations(true)}>
                      <ContentItemButtonText>{packingList.myuseRecommendationsButton}</ContentItemButtonText>
                    </ContentItemButton>
                  )}
                </TemplateItemRow>
              );
            })}
          </TemplateItemsList>
        </TemplatePreviewContainer>
      );
    }

    const renderSearchBar = () => {
      return (
        <SearchBarContainer>
          <SearchBar>
            <SearchIcon size={12} color={theme.brand.gray[500]} style={{ marginRight: 10 }} />
            <TextInput
              placeholder={packingList.searchPlaceholder}
              style={{ height: 40 }}
              onChangeText={(text) => setSearchQuery(text)}
              value={searchQuery}
            />
          </SearchBar>
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Text style={{ color: theme.brand.green[500] }}>{packingList.cancel}</Text>
          </TouchableOpacity>
        </SearchBarContainer>
      );
    };

    const renderSortBy = () => {
      return (
        <SortContainer>
          <Text>{packingList.sortBy}</Text>
          <SortButton onPress={() => { genderDropdownRef.current?.toggle() }}>
            <GenericDropdown
              buttonStyles={{ width: 130, justifyContent: 'center' }}
              textStyles={{ textAlign: 'left', marginRight: 2, fontFamily: 'MuktaVaani', fontSize: 16, color: '#333' }}
              withDropdownIcon={true}
              options={SORT_OPTIONS}
              selectedValue={sort}
              onSelect={(option: DropdownOption) => {
                setSort(option.value as "Ascending" | "Descending" | "Newest");
                genderDropdownRef.current?.close();
                setSearchQuery('');
              }}
              ref={genderDropdownRef}
            />
          </SortButton>
        </SortContainer>
      );
    };
    const renderEmptyState = () => {
      return (
        <EmptyStateContainer>
          <EmptyIconView>
            <Text style={{ fontSize: 15, color: theme.brand.gray[50] }}>!</Text>
          </EmptyIconView>
          <View style={{ marginLeft: 10 }}>
            <Text style={{ fontFamily: 'MuktaVaaniMedium', color: theme.brand.gray[900] }}>{packingList.noResultsFound}</Text>
          </View>
        </EmptyStateContainer>
      )
    }
    return (
      <>
        <View style={{ marginTop: 20 }}>
          <TitleSection string={packingList.packingListTemplatesTitle} />
          <SubtitleText string={packingList.packingListTemplatesText} />
        </View>
        {/* Search Bar */}
        {renderSearchBar()}
        {/* Sort by */}
        {renderSortBy()}

        <TemplatesContainer onLayout={handleLayout} style={{ marginTop: 20 }}>
          {isLoading ? (
            <ActivityIndicator size="large" />
          ) : filteredTemplates.length > 0 ? (
            <FlatList
              keyExtractor={(item, index) => `${item.id}-${index}`}
              numColumns={3}
              columnWrapperStyle={{ justifyContent: 'space-between' }}
              contentContainerStyle={{
                width: width,
                paddingBottom: 200,
                gap: 20,
                justifyContent: 'space-between',
              }}
              data={filteredTemplates}
              renderItem={({ item }) => (
                <TouchableOpacity onPress={() => handleSelectTemplate(item)}>
                  <TemplateItemContainer width={width / 3}>
                    <TemplateImage source={{ uri: item.image }} />
                    <TemplateItemName>{item.name}</TemplateItemName>
                  </TemplateItemContainer>
                </TouchableOpacity>
              )}
            />
          ) : renderEmptyState()}

        </TemplatesContainer>
      </>
    );
  };

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={handleBack}
      onBackdropPress={handleBack}
    >
      <TemplateModalContainer>
        {renderHeader()}
        {renderContent()}
      </TemplateModalContainer>
      {/* Recommendations Modal */}
      <Modal
        style={{ justifyContent: 'center', margin: 0 }}
        isVisible={isShowRecommendations}
        onBackButtonPress={() => setShowRecommendations(false)}
        onBackdropPress={() => setShowRecommendations(false)}>
        <RecommendationsModal>
          <RecommendationsModalCloseButton onPress={() => setShowRecommendations(false)}>
            <ModalCloseButtonText>X</ModalCloseButtonText>
          </RecommendationsModalCloseButton>
          <ModalViewText>
            <ModalViewTextTitle>{packingList.viewOurRecommendations}</ModalViewTextTitle>
            <ModalViewTextDescription>
              {packingList.modalViewTextDescription1}
              {/* For test purpose only */}
              <Text style={{ fontFamily: 'MuktaVaaniSemiBold', color: theme.brand.gray[900] }}> Travel Workout Gear (Clothes) </Text>
              {packingList.modalViewTextDescription2}
            </ModalViewTextDescription>
            <ModalViewTextButton onPress={() => {
              setShowRecommendations(false);
              // add some delay to close the modal
              setTimeout(() => {
                handleUseTemplate();
              }, 500);
            }}>
              <ModalViewTextButtonText>{packingList.usePackingTemplate}</ModalViewTextButtonText>
            </ModalViewTextButton>
          </ModalViewText>
        </RecommendationsModal>
      </Modal>
    </Modal>
  );
}