import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import Modal from 'react-native-modal';
import styled from 'styled-components/native';
import { ChevronRight, Check, ChevronDown } from 'lucide-react-native';
import {
  clothingCategories,
  CategoryItem as CategoryItemType,
} from '@/data/categories';

interface FilterModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectCategory: (categoryId: string, categoryName: string) => void;
  selectedCategory: string;
  gender?: 'WOMENS' | 'MENS'; // Optional gender filter
}

export default function FilterModal({
  isVisible,
  onClose,
  onSelectCategory,
  selectedCategory,
  gender = 'WOMENS', // Default to women's categories
}: FilterModalProps) {
  // State for expanded categories
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

  // Reset expanded state when modal is closed
  useEffect(() => {
    if (!isVisible) {
      setExpandedCategory(null);
    }
  }, [isVisible]);

  // Get the categories for the current gender
  const categories = clothingCategories[gender];

  // Get the most common top-level categories for pills
  const mainCategories = [
    { id: 'all', name: 'All' },
    ...categories
      .filter((cat) =>
        [
          'womens-tops',
          'womens-bottoms',
          'womens-dresses',
          'womens-shoes',
          'mens-tops',
          'mens-bottoms',
          'mens-shoes',
        ].includes(cat.id),
      )
      .slice(0, 5), // Limit to 5 main categories for pills
  ];

  // Handle category selection
  const handleCategorySelect = (category: CategoryItemType) => {
    onSelectCategory(category.id, category.name);
    onClose();
  };

  // Toggle expanded category
  const toggleExpandCategory = (categoryId: string) => {
    setExpandedCategory(expandedCategory === categoryId ? null : categoryId);
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      style={{ margin: 0, justifyContent: 'flex-end' }}
      backdropOpacity={0.5}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      useNativeDriver={true}
    >
      <ModalContainer>
        {/* Bar indicator at the top of the modal */}
        <BarIndicatorContainer>
          <BarIndicator />
        </BarIndicatorContainer>

        <ModalHeader>
          <ModalTitle>Filter by Category</ModalTitle>
          <CloseButton onPress={onClose}>
            <CloseButtonText>Close</CloseButtonText>
          </CloseButton>
        </ModalHeader>

        {/* Main category pills */}
        <PillsContainer>
          {mainCategories.map((category) => (
            <Pill
              key={category.id}
              isSelected={selectedCategory === category.id}
              onPress={() => handleCategorySelect(category)}
            >
              <PillText isSelected={selectedCategory === category.id}>
                {category.name}
              </PillText>
            </Pill>
          ))}
        </PillsContainer>

        <ScrollView
          style={{ maxHeight: 300 }}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          {/* Category sections with expandable subcategories */}
          {categories.map((category) => {
            const hasChildren =
              category.children && category.children.length > 0;
            const isExpanded = expandedCategory === category.id;

            return (
              <View key={category.id}>
                <CategoryHeader>
                  <CategoryHeaderButton
                    onPress={() =>
                      hasChildren
                        ? toggleExpandCategory(category.id)
                        : handleCategorySelect(category)
                    }
                  >
                    <CategoryHeaderText>{category.name}</CategoryHeaderText>
                    {hasChildren && (
                      <ChevronDown
                        size={18}
                        color="#333333"
                        style={{
                          transform: [
                            { rotate: isExpanded ? '180deg' : '0deg' },
                          ],
                          marginLeft: 8,
                        }}
                      />
                    )}
                  </CategoryHeaderButton>

                  {!hasChildren && (
                    <SelectButton
                      onPress={() => handleCategorySelect(category)}
                      isSelected={selectedCategory === category.id}
                    >
                      {selectedCategory === category.id && (
                        <Check size={16} color="#FFFFFF" />
                      )}
                    </SelectButton>
                  )}
                </CategoryHeader>

                {/* Subcategories */}
                {isExpanded && hasChildren && (
                  <SubcategoriesContainer>
                    {category.children?.map((subcategory) => (
                      <SubcategoryItem key={subcategory.id}>
                        <SubcategoryButton
                          onPress={() => handleCategorySelect(subcategory)}
                        >
                          <SubcategoryText>{subcategory.name}</SubcategoryText>
                        </SubcategoryButton>

                        <SelectButton
                          onPress={() => handleCategorySelect(subcategory)}
                          isSelected={selectedCategory === subcategory.id}
                        >
                          {selectedCategory === subcategory.id && (
                            <Check size={16} color="#FFFFFF" />
                          )}
                        </SelectButton>
                      </SubcategoryItem>
                    ))}
                  </SubcategoriesContainer>
                )}
              </View>
            );
          })}
        </ScrollView>
      </ModalContainer>
    </Modal>
  );
}

// Styled components
const ModalContainer = styled(View)`
  background-color: #ffffff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 16px;
  max-height: 80%;
`;

const BarIndicatorContainer = styled(View)`
  width: 100%;
  height: 34px;
  align-items: center;
  justify-content: center;
`;

const BarIndicator = styled(View)`
  width: 56px;
  height: 5px;
  border-radius: 5px;
  background-color: rgba(51, 51, 51, 0.2);
`;

const ModalHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom-width: 1px;
  border-bottom-color: #eeeeee;
`;

const ModalTitle = styled(Text)`
  font-family: 'CormorantGaramondSemiBold';
  font-size: 24px;
  color: #333333;
  flex: 1;
`;

const CloseButton = styled(TouchableOpacity)`
  padding: 8px;
`;

const CloseButtonText = styled(Text)`
  font-family: 'MuktaVaani';
  font-size: 16px;
  color: #0e7e61;
`;

interface SelectableProps {
  isSelected?: boolean;
}

// Pills container for main categories
const PillsContainer = styled(View)`
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 16px;
  gap: 8px;
`;

const Pill = styled(TouchableOpacity)<SelectableProps>`
  padding: 8px 16px;
  border-radius: 20px;
  background-color: ${(props) =>
    props.isSelected ? '#0E7E61' : 'transparent'};
  border: 1px solid ${(props) => (props.isSelected ? '#0E7E61' : '#CCCCCC')};
`;

const PillText = styled(Text)<SelectableProps>`
  font-family: 'MuktaVaani';
  font-size: 14px;
  color: ${(props) => (props.isSelected ? '#FFFFFF' : '#333333')};
`;

// Category header
const CategoryHeader = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom-width: 1px;
  border-bottom-color: #eeeeee;
`;

const CategoryHeaderButton = styled(TouchableOpacity)`
  flex: 1;
  flex-direction: row;
  align-items: center;
`;

const CategoryHeaderText = styled(Text)`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
  color: #333333;
`;

// Subcategories
const SubcategoriesContainer = styled(View)`
  padding-left: 16px;
  background-color: #f9f9f9;
`;

const SubcategoryItem = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom-width: 1px;
  border-bottom-color: #eeeeee;
`;

const SubcategoryButton = styled(TouchableOpacity)`
  flex: 1;
`;

const SubcategoryText = styled(Text)`
  font-family: 'MuktaVaani';
  font-size: 14px;
  color: #333333;
`;

// Select button (checkmark)
const SelectButton = styled(TouchableOpacity)<SelectableProps>`
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: ${(props) =>
    props.isSelected ? '#0E7E61' : 'transparent'};
  border: 1px solid ${(props) => (props.isSelected ? '#0E7E61' : '#CCCCCC')};
  justify-content: center;
  align-items: center;
  margin-left: 8px;
`;
