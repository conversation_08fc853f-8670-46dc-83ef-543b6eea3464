import styled from 'styled-components/native';
import { DefaultTheme } from 'styled-components/native';

export const QuizCardView = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 10px;
  background-color: #fff;
  border-radius: 16px;
  padding: 16px;
`;

export const QuizCardHeading = styled.Text`
  font-family: 'MuktaVaaniSemiBold';
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 4px;
`;

export const QuizCardDescription = styled.Text`
  font-family: 'MuktaVaani';
  line-height: 20px;
  margin-bottom: 8px;
`;

export const QuizCardLink = styled.Text`
  color: ${({ theme }: { theme: DefaultTheme }) => theme.brand.green[500]};
  font-family: 'MuktaVaaniSemiBold';
  line-height: 20px;
  text-decoration: underline;
  text-decoration-color: ${({ theme }: { theme: DefaultTheme }) =>
    theme.brand.green[500]};
`;
