import React from 'react';
import { StyleSheet, Text } from 'react-native';
import { AllTripsButton, AllTripsButtonText, AllTripsNavView } from './styles';

type TabType = 'upcoming' | 'past';

interface AllTripsNavProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
}

export const AllTripsNav = ({ activeTab, setActiveTab }: AllTripsNavProps) => {
  return (
    <AllTripsNavView>
      <AllTripsButton
        active={activeTab === 'upcoming'}
        onPress={() => setActiveTab('upcoming')}
        activeOpacity={0.8}
      >
        <AllTripsButtonText active={activeTab === 'upcoming'}>
          Upcoming
        </AllTripsButtonText>
      </AllTripsButton>
      <AllTripsButton
        active={activeTab === 'past'}
        onPress={() => setActiveTab('past')}
        activeOpacity={0.8}
      >
        <AllTripsButtonText active={activeTab === 'past'}>
          Past
        </AllTripsButtonText>
      </AllTripsButton>
    </AllTripsNavView>
  );
};
