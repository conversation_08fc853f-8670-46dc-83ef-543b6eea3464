import styled, { DefaultTheme } from 'styled-components/native';

export const AllTripsNavView = styled.View`
  flex-direction: row;
  gap: 16px;
`;

export const AllTripsButton = styled.TouchableOpacity<{ active?: boolean }>`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${({
  theme,
  active,
}: {
  theme: DefaultTheme;
  active?: boolean;
}) => (active ? theme.brand.green[500] : 'transparent')};
  border: 1px solid
    ${({ theme, active }: { theme: DefaultTheme; active?: boolean }) =>
    active ? theme.brand.green[500] : '#E4E4E4'};
  padding: 8px 16px;
  border-radius: 50px;
`;

export const AllTripsButtonText = styled.Text<{ active?: boolean }>`
  color: ${({ active }: { active?: boolean }) => (active ? '#fff' : '#000')};
  font-size: 14px;
  font-family: MuktaVaaniSemiBold;
`;
