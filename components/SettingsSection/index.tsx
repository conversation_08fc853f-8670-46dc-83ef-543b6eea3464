import { CSSProperties } from 'react';
import { SectionTitle, SettingsSectionContainer } from './styles';

type SettingsSectionProps = {
  title?: string;
  children?: React.ReactNode;
};

export const SettingsSection = ({ title, children }: SettingsSectionProps) => {
  return (
    <SettingsSectionContainer>
      {title && <SectionTitle>{title}</SectionTitle>}
      {children}
    </SettingsSectionContainer>
  );
};
