import { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';
import { PADDING, WIDTHS, HEIGHTS, FONT_SIZES, BORDER_RADIUS, isTablet } from '@/constants/responsive';

/**
 * Hook to get responsive values and listen for dimension changes
 * This is useful for components that need to respond to orientation changes
 */
export const useResponsive = () => {
  const [dimensions, setDimensions] = useState(Dimensions.get('window'));
  const [isTabletDevice, setIsTabletDevice] = useState(isTablet());

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions(window);
      setIsTabletDevice(Math.min(window.width, window.height) >= 600);
    });

    return () => subscription.remove();
  }, []);

  return {
    width: dimensions.width,
    height: dimensions.height,
    isTablet: isTabletDevice,
    padding: PADDING,
    widths: WIDTHS,
    heights: HEIGHTS,
    fontSizes: FONT_SIZES,
    borderRadius: BORDER_RADIUS,
  };
};

export default useResponsive;
