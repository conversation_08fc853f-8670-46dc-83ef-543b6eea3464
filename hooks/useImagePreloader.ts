import { useCallback, useState } from 'react';
import { Image } from 'react-native';

interface PreloadProgress {
  loaded: number;
  total: number;
  percentage: number;
  isComplete: boolean;
  errors: string[];
  failedUrls: string[];
}

interface UseImagePreloaderReturn {
  preloadImages: (urls: string[]) => Promise<void>;
  progress: PreloadProgress;
  isPreloading: boolean;
  resetProgress: () => void;
  retryFailedImages: () => Promise<void>;
}

export const useImagePreloader = (): UseImagePreloaderReturn => {
  const [progress, setProgress] = useState<PreloadProgress>({
    loaded: 0,
    total: 0,
    percentage: 0,
    isComplete: false,
    errors: [],
    failedUrls: [],
  });
  const [isPreloading, setIsPreloading] = useState(false);
  const [failedUrls, setFailedUrls] = useState<string[]>([]);

  const resetProgress = useCallback(() => {
    setProgress({
      loaded: 0,
      total: 0,
      percentage: 0,
      isComplete: false,
      errors: [],
      failedUrls: [],
    });
    setFailedUrls([]);
  }, []);

  const preloadSingleImage = useCallback(async (url: string, retryCount = 0): Promise<boolean> => {
    const maxRetries = 2;
    
    try {
      console.log(`Attempting to preload image (attempt ${retryCount + 1}): ${url}`);
      
      // Add a timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), 10000); // 10 second timeout
      });
      
      const prefetchPromise = Image.prefetch(url);
      
      await Promise.race([prefetchPromise, timeoutPromise]);
      
      console.log(`Successfully preloaded: ${url}`);
      return true;
    } catch (error) {
      console.error(`Failed to preload ${url} (attempt ${retryCount + 1}):`, error);
      
      if (retryCount < maxRetries) {
        console.log(`Retrying ${url} (attempt ${retryCount + 2})...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
        return preloadSingleImage(url, retryCount + 1);
      }
      
      return false;
    }
  }, []);

  const preloadImages = useCallback(async (urls: string[]) => {
    if (urls.length === 0) return;

    setIsPreloading(true);
    resetProgress();

    const validUrls = urls.filter(url => url && url.trim() !== '');
    
    if (validUrls.length === 0) {
      setIsPreloading(false);
      return;
    }

    console.log(`Starting to preload ${validUrls.length} images:`, validUrls);
    
    setProgress(prev => ({
      ...prev,
      total: validUrls.length,
    }));

    const errors: string[] = [];
    const failedUrls: string[] = [];
    let loadedCount = 0;

    try {
      // Preload images in smaller batches to avoid overwhelming the network
      const batchSize = 2; // Reduced from 3 to 2
      const batches = [];
      
      for (let i = 0; i < validUrls.length; i += batchSize) {
        batches.push(validUrls.slice(i, i + batchSize));
      }

      console.log(`Created ${batches.length} batches of size ${batchSize}`);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`Processing batch ${batchIndex + 1}/${batches.length}:`, batch);
        
        const batchPromises = batch.map(async (url) => {
          const success = await preloadSingleImage(url);
          
          if (success) {
            loadedCount++;
            setProgress(prev => ({
              ...prev,
              loaded: loadedCount,
              percentage: Math.round((loadedCount / validUrls.length) * 100),
            }));
            console.log(`✅ Preloaded image ${loadedCount}/${validUrls.length}: ${url}`);
          } else {
            const errorMessage = `Failed to preload after retries: ${url}`;
            console.error(`❌ ${errorMessage}`);
            errors.push(errorMessage);
            failedUrls.push(url);
            loadedCount++; // Count as "processed" even if failed
            setProgress(prev => ({
              ...prev,
              loaded: loadedCount,
              percentage: Math.round((loadedCount / validUrls.length) * 100),
              errors: [...prev.errors, errorMessage],
              failedUrls: [...prev.failedUrls, url],
            }));
          }
        });

        // Wait for current batch to complete before starting next batch
        await Promise.allSettled(batchPromises);
        
        // Small delay between batches to be gentle on the network
        if (batchIndex < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      setProgress(prev => ({
        ...prev,
        isComplete: true,
        errors,
        failedUrls,
      }));
      
      setFailedUrls(failedUrls);

      console.log(`🎉 Image preloading complete!`);
      console.log(`   ✅ Successfully loaded: ${validUrls.length - failedUrls.length}/${validUrls.length}`);
      console.log(`   ❌ Failed to load: ${failedUrls.length}/${validUrls.length}`);
      
      if (failedUrls.length > 0) {
        console.log(`   Failed URLs:`, failedUrls);
      }
      
    } catch (error) {
      console.error('💥 Error during image preloading:', error);
      setProgress(prev => ({
        ...prev,
        errors: [...prev.errors, `General preloading error: ${error}`],
      }));
    } finally {
      setIsPreloading(false);
    }
  }, [resetProgress, preloadSingleImage]);

  const retryFailedImages = useCallback(async () => {
    if (failedUrls.length === 0) return;
    
    console.log(`🔄 Retrying ${failedUrls.length} failed images...`);
    
    setIsPreloading(true);
    
    const retryErrors: string[] = [];
    let retrySuccessCount = 0;
    
    for (const url of failedUrls) {
      const success = await preloadSingleImage(url);
      
      if (success) {
        retrySuccessCount++;
        console.log(`✅ Retry successful: ${url}`);
      } else {
        retryErrors.push(`Retry failed: ${url}`);
        console.error(`❌ Retry failed: ${url}`);
      }
    }
    
    console.log(`🔄 Retry complete: ${retrySuccessCount}/${failedUrls.length} images recovered`);
    
    setIsPreloading(false);
  }, [failedUrls, preloadSingleImage]);

  return {
    preloadImages,
    progress,
    isPreloading,
    resetProgress,
    retryFailedImages,
  };
}; 