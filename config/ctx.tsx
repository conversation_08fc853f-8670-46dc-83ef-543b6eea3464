import { useContext, createContext, type PropsWithChildren } from 'react';
import { useStorageState } from './useStorageState';
import * as SecureStore from 'expo-secure-store';


const AuthContext = createContext<{
  signIn: (token:string) => void;
  signOut: () => void;
  session?: string | null;
  isLoading: boolean;
}>({
  signIn: () => null,
  signOut: () => null,
  session: null,
  isLoading: false,
});

// This hook can be used to access the user info.
export function useSession() {
  const value = useContext(AuthContext);
  if (process.env.NODE_ENV !== 'production') {
    if (!value) {
      throw new Error('useSession must be wrapped in a <SessionProvider />');
    }
  }

  return value;
}

export function SessionProvider({ children }: PropsWithChildren) {
  const [[isLoading, session], setSession] = useStorageState('session');

  return (
    <AuthContext.Provider
      value={{
        signIn: async(token) => {
          setSession(token);
          await SecureStore.setItemAsync('userToken',token);
        },
        signOut: async() => {
          setSession(null);
          await SecureStore.deleteItemAsync('userToken');
        },
        session,
        isLoading,
      }}>
      {children}
    </AuthContext.Provider>
  );
}