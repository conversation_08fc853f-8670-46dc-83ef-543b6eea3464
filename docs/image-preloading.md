# Image Preloading Feature

## Overview

The image preloading feature ensures a smooth user experience in the style quiz by downloading all quiz images before the user starts swiping. This eliminates waiting times during the quiz and provides a seamless interaction.

## How It Works

### 1. Preloading Process
- When the quiz screen loads, it fetches all quiz questions from the server
- Extracts image URLs from all quiz questions
- Uses React Native's `Image.prefetch()` to download images in batches
- Shows a progress screen with loading percentage
- Starts the quiz once all images are loaded

### 2. Components

#### `useImagePreloader` Hook
- Manages the preloading state and progress
- Handles batch loading (3 images at a time) to avoid overwhelming the network
- Provides error handling for failed image downloads
- Returns progress information and loading state

#### `ImagePreloaderScreen` Component
- Displays a beautiful loading screen with progress bar
- Shows loading percentage and error messages
- Includes a "Skip & Start Quiz" button for impatient users
- Matches the app's design system

### 3. Features

#### Batch Loading
Images are loaded in batches of 3 to balance between speed and network efficiency.

#### Error Handling
- Failed image downloads are logged but don't stop the preloading process
- Users are informed about failed images
- The quiz continues with successfully loaded images

#### Timeout Fallback
- If preloading takes longer than 10 seconds, the quiz starts automatically
- Ensures users don't get stuck on the loading screen

#### Skip Option
- Users can skip preloading and start the quiz immediately
- Useful for users with slow connections or who want to start quickly

## Benefits

1. **Smooth User Experience**: No waiting for images to load during the quiz
2. **Better Performance**: Images are cached and load instantly when displayed
3. **Reduced Network Usage**: Images are downloaded once, not on each swipe
4. **User Control**: Users can skip preloading if they prefer

## Implementation Details

### File Structure
```
hooks/
  useImagePreloader.ts          # Custom hook for image preloading
components/
  ImagePreloaderScreen/
    index.tsx                   # Loading screen component
app/(style-quiz)/swipe/
  index.tsx                     # Updated swipe screen with preloading
```

### Usage Example
```typescript
const { preloadImages, progress, isPreloading } = useImagePreloader();

// Preload images when quiz questions are available
useEffect(() => {
  if (quizQuestions.length > 0) {
    const imageUrls = quizQuestions.map(question => question.image).filter(Boolean);
    preloadImages(imageUrls);
  }
}, [quizQuestions]);

// Show loading screen until preloading is complete
if (!isQuizReady) {
  return <ImagePreloaderScreen progress={progress} isPreloading={isPreloading} />;
}
```

## Future Enhancements

1. **Progressive Loading**: Load images in order of quiz appearance
2. **Background Preloading**: Start preloading when user enters the style quiz section
3. **Caching Strategy**: Implement more sophisticated caching for better performance
4. **Network Detection**: Adjust batch size based on network conditions 