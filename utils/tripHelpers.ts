import { Destination, TripDetails } from '@/types/trip';

interface TripDates {
  startDate?: Date;
  endDate?: Date;
}

export const getTripDates = (locations: Destination[]): TripDates => {
  const validLocations = locations.filter((loc) => loc.startDate);
  if (validLocations.length === 0) {
    return {
      startDate: undefined,
      endDate: undefined,
    };
  }

  const startDate = new Date(
    Math.min(...validLocations.map((loc) => (loc.startDate as Date).getTime())),
  );
  const endDates = validLocations
    .map((loc) => loc.endDate)
    .filter(Boolean) as Date[];
  const endDate =
    endDates.length > 0
      ? new Date(Math.max(...endDates.map((date) => date.getTime())))
      : undefined;

  return { startDate, endDate };
};

export const formatDate = (date: Date): string => {
  const day = date.getDate();
  const month = date.toLocaleDateString('en-UK', { month: 'short' });
  const year = date.getFullYear();
  return `${day} ${month}, ${year}`;
};

export const formatTripDates = (locations: Destination[]): string => {
  const { startDate, endDate } = getTripDates(locations);

  if (!startDate) {
    return 'No dates set';
  }

  if (!endDate) {
    return formatDate(startDate);
  }

  const sameDay = startDate.getDate() === endDate.getDate();
  const sameMonth = startDate.getMonth() === endDate.getMonth();
  const sameYear = startDate.getFullYear() === endDate.getFullYear();

  if (sameDay && sameMonth && sameYear) {
    return formatDate(startDate);
  }

  if (sameMonth && sameYear) {
    return `${startDate.getDate()} - ${endDate.getDate()} ${startDate.toLocaleDateString(
      'en-UK',
      { month: 'short' },
    )}, ${startDate.getFullYear()}`;
  }

  if (sameYear) {
    return `${startDate.getDate()} ${startDate.toLocaleDateString('en-UK', { month: 'short' })} - ${endDate.getDate()} ${endDate.toLocaleDateString('en-UK', { month: 'short' })}, ${startDate.getFullYear()}`;
  }

  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
};

export const cleanTripDetails = (details: TripDetails): TripDetails => {
  return {
    ...details,
    destinations: details.destinations.map(
      ({ id, ...destination }) => destination,
    ),
  };
};

export const formatDuration = (startDate: Date, endDate: Date): string => {
  const millisecondsPerDay = 1000 * 60 * 60 * 24;
  const totalDays = Math.ceil(
    (endDate.getTime() - startDate.getTime()) / millisecondsPerDay,
  );

  const years = Math.floor(totalDays / 365);
  const remainingDaysAfterYears = totalDays % 365;
  const months = Math.floor(remainingDaysAfterYears / 30);
  const days = remainingDaysAfterYears % 30;

  const parts = [];
  if (years > 0) parts.push(`${years}y`);
  if (months > 0) parts.push(`${months}m`);
  if (days > 0 || parts.length === 0) parts.push(`${days}d`);

  return parts.join(' ');
};

export const formatDestinations = (destinations: Destination[]): string => {
  if (destinations.length === 1) {
    const parts = destinations[0].name.split(/[,-]/); // Split by comma or hyphen
    return parts[0]?.trim() || 'Location';
  }

  const countries = destinations
    .map((loc) => {
      const parts = loc.name.split(/[,-]/); // Split by comma or hyphen
      const country = parts[parts.length - 1]?.trim();
      return country;
    })
    .filter(Boolean);

  const uniqueCountries = Array.from(new Set(countries));
  return uniqueCountries.join(', ') || 'Multiple Locations';
};

export const getStartDateText = (
  destinations: Array<{ startDate?: Date; endDate?: Date }>,
  currentDestination: { startDate?: Date },
) => {
  const index = destinations.indexOf(currentDestination);
  if (index === 0) {
    return currentDestination.startDate
      ? formatDate(currentDestination.startDate)
      : 'Select Start Date';
  }
  const prevDestination = destinations[index - 1];
  return prevDestination.endDate
    ? formatDate(prevDestination.endDate)
    : 'Waiting for previous end date';
};
