import { useEarnedRewardsModal } from '@/context/EarnedRewardsContext';

// Example utility functions for manually triggering the earned rewards modal

// Example: Show modal for a single reward
export const showSingleRewardModal = (
  showRewardModal: ReturnType<typeof useEarnedRewardsModal>['showRewardModal']
) => {
  const exampleReward = {
    _id: 'example-reward-id',
    rewardCode: 'EXAMPLE_REWARD',
    metadata: { action: 'manual_trigger' },
    seen: false,
    createdAt: new Date(),
    userId: 'current-user-id',
    points: 50,
  };

  showRewardModal([exampleReward]);
};

// Example: Show modal for multiple rewards
export const showMultipleRewardsModal = (
  showRewardModal: ReturnType<typeof useEarnedRewardsModal>['showRewardModal']
) => {
  const exampleRewards = [
    {
      _id: 'reward-1',
      rewardCode: 'FIRST_ACHIEVEMENT',
      metadata: { action: 'completed_profile' },
      seen: false,
      createdAt: new Date(),
      userId: 'current-user-id',
      points: 25,
    },
    {
      _id: 'reward-2',
      rewardCode: 'SECOND_ACHIEVEMENT',
      metadata: { action: 'added_item' },
      seen: false,
      createdAt: new Date(),
      userId: 'current-user-id',
      points: 15,
    },
  ];

  showRewardModal(exampleRewards);
};

// Example usage in a component:
/*
import { useEarnedRewardsModal } from '@/context/EarnedRewardsContext';
import { showSingleRewardModal } from '@/utils/earnedRewardsUtils';

export const ExampleComponent = () => {
  const { showRewardModal } = useEarnedRewardsModal();

  const handleButtonPress = () => {
    // Manually trigger the rewards modal
    showSingleRewardModal(showRewardModal);
  };

  return (
    <TouchableOpacity onPress={handleButtonPress}>
      <Text>Test Rewards Modal</Text>
    </TouchableOpacity>
  );
};
*/ 