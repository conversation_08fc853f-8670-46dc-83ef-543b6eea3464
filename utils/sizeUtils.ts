import { MENS_SIZE_CHARTS, WOMENS_SIZE_CHARTS, SIZE_REGIONS } from '@/constants/size-charts';

// Type definitions
export type SizingRegion = 'US' | 'UK' | 'EU' | 'AUS';
export type Gender = 'men' | 'women';

/**
 * Get all available sizing regions
 * @returns Array of sizing regions
 */
export const getAvailableRegions = (): SizingRegion[] => {
  return SIZE_REGIONS[0].options.map(option => option.value as SizingRegion);
};

/**
 * Get all size charts for one or more genders
 * @param gender - The gender(s) ('men', 'women', array of both, or undefined for all)
 * @returns Array of size charts for the specified gender(s) or all if no gender specified
 */
export const getSizeChartsByGender = (gender?: Gender | Gender[]): any[] => {
  if (!gender) {
    // Return all size charts when no gender is specified
    return [...MENS_SIZE_CHARTS, ...WOMENS_SIZE_CHARTS];
  }
  
  if (Array.isArray(gender)) {
    const charts: any[] = [];
    gender.forEach(g => {
      const genderCharts = g === 'men' ? MENS_SIZE_CHARTS : WOMENS_SIZE_CHARTS;
      charts.push(...genderCharts);
    });
    return charts;
  }
  return gender === 'men' ? MENS_SIZE_CHARTS : WOMENS_SIZE_CHARTS;
};

/**
 * Get all sizes for a specific region across all clothing types
 * @param region - The sizing region (US, UK, EU, AUS)
 * @param gender - The gender ('men' or 'women')
 * @returns Object with clothing types as keys and arrays of sizes as values
 */
export const getAllSizesByRegionAndGender = (
  region: SizingRegion,
  gender: Gender
) => {
  const sizeCharts = getSizeChartsByGender(gender);
  const result: Record<string, any[]> = {};

  sizeCharts.forEach(chart => {
    const regionData = chart.regions.find((r: any) => r.name === region);
    if (regionData) {
      result[chart.type] = regionData.sizes;
    }
  });

  return result;
};

/**
 * Get sizes for a specific clothing type and region
 * @param clothingType - The type of clothing
 * @param region - The sizing region (US, UK, EU, AUS)
 * @param gender - The gender ('men' or 'women')
 * @returns Array of sizes for the specified clothing type and region
 */
export const getSizesByClothingTypeAndRegion = (
  clothingType: string,
  region: SizingRegion,
  gender: Gender
) => {
  const sizeCharts = getSizeChartsByGender(gender);
  const chart = sizeCharts.find((c: any) => c.type === clothingType);

  if (!chart) {
    return [];
  }

  const regionData = chart.regions.find((r: any) => r.name === region);
  return regionData ? regionData.sizes : [];
};

/**
 * Get all available clothing types for a specific gender
 * @param gender - The gender ('men' or 'women')
 * @returns Array of clothing types
 */
export const getClothingTypesByGender = (gender: Gender) => {
  const sizeCharts = getSizeChartsByGender(gender);
  return sizeCharts.map((chart: any) => chart.type);
};

/**
 * Get size labels for a specific clothing type and region
 * @param clothingType - The type of clothing
 * @param region - The sizing region (US, UK, EU, AUS)
 * @param gender - The gender ('men' or 'women')
 * @returns Array of size labels (value and label pairs)
 */
export const getSizeLabelsByClothingTypeAndRegion = (
  clothingType: string,
  region: SizingRegion,
  gender: Gender
) => {
  const sizes = getSizesByClothingTypeAndRegion(clothingType, region, gender);
  return sizes.map((size: any) => ({
    value: size.value,
    label: size.label || size.value
  }));
};

/**
 * Get size measurements for a specific size
 * @param clothingType - The type of clothing
 * @param region - The sizing region (US, UK, EU, AUS)
 * @param gender - The gender ('men' or 'women')
 * @param sizeValue - The size value to get measurements for
 * @returns Array of measurements for the specified size
 */
export const getSizeMeasurements = (
  clothingType: string,
  region: SizingRegion,
  gender: Gender,
  sizeValue: string
) => {
  const sizes = getSizesByClothingTypeAndRegion(clothingType, region, gender);
  const size = sizes.find((s: any) => s.value === sizeValue);
  return size ? size.measurements : [];
};

/**
 * Get all sizes for a specific region (main utility function)
 * @param region - The sizing region (US, UK, EU, AUS)
 * @returns Object containing all sizes organized by gender and clothing type
 */
export const getAllSizesByRegion = (region: SizingRegion) => {
  return {
    men: getAllSizesByRegionAndGender(region, 'men'),
    women: getAllSizesByRegionAndGender(region, 'women')
  };
};

/**
 * Check if a region is valid
 * @param region - The region to validate
 * @returns True if the region is valid
 */
export const isValidRegion = (region: string): region is SizingRegion => {
  return getAvailableRegions().includes(region as SizingRegion);
};

/**
 * Get size chart for a specific clothing type
 * @param clothingType - The type of clothing
 * @param gender - The gender ('men' or 'women')
 * @returns The size chart for the specified clothing type
 */
export const getSizeChart = (
  clothingType: string,
  gender: Gender
) => {
  const sizeCharts = getSizeChartsByGender(gender);
  return sizeCharts.find((chart: any) => chart.type === clothingType);
};

/**
 * Get size charts filtered by category, gender, and region
 * @param category - The category to filter by (e.g., 'General Apparel', 'Bottoms', 'Footwear')
 * @param gender - The gender to filter by ('men', 'women', 'both', or undefined for all)
 * @param region - The region to filter by ('US', 'UK', 'EU', 'AUS', or undefined for all)
 * @returns Array of size charts matching the filters
 */
export const getSizeChartsByCategory = (
  category?: string,
  gender?: Gender | 'both',
  region?: SizingRegion
) => {
  let charts: any[] = [];

  // Get charts based on gender filter
  if (!gender || gender === 'both') {
    // Get all charts (both men and women)
    charts = [...MENS_SIZE_CHARTS, ...WOMENS_SIZE_CHARTS];
  } else if (gender === 'men') {
    charts = MENS_SIZE_CHARTS;
  } else if (gender === 'women') {
    charts = WOMENS_SIZE_CHARTS;
  }

  // Filter by category if specified
  if (category) {
    charts = charts.filter((chart: any) => chart.category === category);
  }

  // Filter by region if specified
  if (region) {
    charts = charts.filter((chart: any) => {
      return chart.regions.some((r: any) => r.name === region);
    });
  }

  return charts;
};

/**
 * Get unique categories available for a specific gender and region
 * @param gender - The gender to filter by ('men', 'women', 'both', or undefined for all)
 * @param region - The region to filter by ('US', 'UK', 'EU', 'AUS', or undefined for all)
 * @returns Array of unique categories
 */
export const getCategoriesByGenderAndRegion = (
  gender?: Gender | 'both',
  region?: SizingRegion
) => {
  const charts = getSizeChartsByCategory(undefined, gender, region);
  const categories = charts.map((chart: any) => chart.category);
  return Array.from(new Set(categories)); // Remove duplicates
};

/**
 * Get size charts for a specific category and region, organized by gender
 * @param category - The category to filter by
 * @param region - The region to filter by ('US', 'UK', 'EU', 'AUS', or undefined for all)
 * @returns Object with 'men' and 'women' keys containing filtered charts
 */
export const getSizeChartsByCategoryAndRegion = (
  category: string,
  region?: SizingRegion
) => {
  const mensCharts = getSizeChartsByCategory(category, 'men', region);
  const womensCharts = getSizeChartsByCategory(category, 'women', region);

  return {
    men: mensCharts,
    women: womensCharts
  };
};

/*
USAGE EXAMPLES:

// Get all available regions
const regions = getAvailableRegions(); // ['US', 'UK', 'EU', 'AUS']

// Get all sizes for US region (main function)
const allSizes = getAllSizesByRegion('US');
// Returns: { men: { mensTops: [...], mensTrousers: [...], ... }, women: { ... } }

// Get sizes for men's tops in US region
const mensTopsSizes = getSizesByClothingTypeAndRegion('mensTops', 'US', 'men');

// Get size labels for women's tops in EU region
const womensTopsLabels = getSizeLabelsByClothingTypeAndRegion('womensTops', 'EU', 'women');

// Get measurements for a specific size
const measurements = getSizeMeasurements('mensTops', 'US', 'men', '38');

// Get all clothing types for men
const mensClothingTypes = getClothingTypesByGender('men');

// Get size charts for multiple genders
const allSizeCharts = getSizeChartsByGender(['men', 'women']); // Returns combined array
const mensSizeCharts = getSizeChartsByGender('men'); // Returns only men's charts
const womensSizeCharts = getSizeChartsByGender('women'); // Returns only women's charts
const allSizeChartsNoGender = getSizeChartsByGender(); // Returns all size charts (men + women)

// Validate a region
const isValid = isValidRegion('US'); // true
const isInvalid = isValidRegion('XX'); // false

// NEW HELPER FUNCTIONS:

// Get size charts by category with gender and region filtering
const footwearCharts = getSizeChartsByCategory('Footwear', 'both', 'US');
// Returns all footwear charts for both men and women in US region

const mensBottomsCharts = getSizeChartsByCategory('Bottoms', 'men', 'EU');
// Returns men's bottoms charts in EU region

const allApparelCharts = getSizeChartsByCategory('General Apparel');
// Returns all general apparel charts for all genders and regions

// Get available categories for specific filters
const mensCategories = getCategoriesByGenderAndRegion('men', 'US');
// Returns: ['General Apparel', 'Bottoms', 'Footwear']

const allCategories = getCategoriesByGenderAndRegion('both');
// Returns all available categories for both genders

// Get size charts organized by gender for a category and region
const bottomsByGender = getSizeChartsByCategoryAndRegion('Bottoms', 'UK');
// Returns: { men: [...], women: [...] }
*/
