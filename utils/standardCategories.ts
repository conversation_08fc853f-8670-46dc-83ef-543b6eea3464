import { CategoryItem } from '@/data/categories';
import { fetchCategoriesFromBackend } from '@/data/gender-categories';

// Standard list of categories that should always be included
export const STANDARD_CATEGORIES = [
  'Tops',
  'Bottoms',
  'Dresses',
  'Shoes',
  'Accessories',
  'Outerwear',
  'Bags',
  'Loungewear',
  'Activewear',
  'Swimwear',
  'Underwear',
  'Sleepwear',
  'Jewelry',
  'Tech',
  'Others'
];

/**
 * Gets a standardized set of categories that includes both backend categories
 * and our standard categories to ensure consistency across the app
 *
 * @param gender The user's gender ('Male' or 'Female')
 * @returns A promise that resolves to an array of CategoryItem objects
 */
export async function getStandardizedCategories(gender: string): Promise<CategoryItem[]> {
  try {
    // Get categories from backend
    const backendCategories = await fetchCategoriesFromBackend(gender);

    // Create a Map to track categories by name for deduplication
    const categoryMap = new Map<string, CategoryItem>();

    // First add all backend categories to the map
    if (Array.isArray(backendCategories)) {
      backendCategories.forEach(category => {
        if (category && category.name) {
          // Normalize the category name for consistent comparison
          const normalizedName = category.name.toLowerCase().trim();

          // Use proper capitalization for display (capitalize first letter)
          const displayName = category.name.charAt(0).toUpperCase() + category.name.slice(1);

          categoryMap.set(normalizedName, {
            id: category.id || `category-${normalizedName.replace(/\s+/g, '-')}`,
            name: displayName, // Use properly capitalized name for display
            children: category.children
          });
        }
      });
    }

    // Then ensure all standard categories are included
    STANDARD_CATEGORIES.forEach(categoryName => {
      const normalizedName = categoryName.toLowerCase().trim();

      // Check if this category or a similar one already exists
      // More precise matching to avoid false positives
      const exists = Array.from(categoryMap.keys()).some(key => {
        // Exact match
        if (key === normalizedName) return true;

        // For partial matches, only consider it a match if one is fully contained in the other
        // and they share at least 4 characters (to avoid matching "bag" with "baggage" for example)
        if (normalizedName.length >= 4 && key.length >= 4) {
          if (key.includes(normalizedName) || normalizedName.includes(key)) {
            console.log(`Considering "${categoryName}" and "${key}" as the same category`);
            return true;
          }
        }

        return false;
      });

      if (!exists) {
        categoryMap.set(normalizedName, {
          id: `standard-${normalizedName.replace(/\s+/g, '-')}`,
          name: categoryName
        });
      }
    });

    // Convert the map back to an array
    return Array.from(categoryMap.values());
  } catch (error) {
    console.error('Error getting standardized categories:', error);

    // Fallback to standard categories if there's an error
    return STANDARD_CATEGORIES.map(name => ({
      id: `standard-${name.toLowerCase().replace(/\s+/g, '-')}`,
      name
    }));
  }
}

/**
 * Formats categories for display in a component
 *
 * @param categories The array of CategoryItem objects
 * @param includeAll Whether to include an "All" category at the beginning
 * @param idProperty The property name to use for the ID ('id' or '_id')
 * @param includeCategory Whether to include a 'category' property that matches the name
 * @returns An array of formatted category objects
 */
export function formatCategoriesForDisplay(
  categories: CategoryItem[],
  includeAll: boolean = true,
  idProperty: 'id' | '_id' = 'id',
  includeCategory: boolean = false
): any[] {
  // Create the result array, optionally starting with an "All" category
  const result = includeAll
    ? [{
        [idProperty]: 'all',
        name: 'All',
        ...(includeCategory ? { category: 'All' } : {})
      }]
    : [];

  // Create a map to track categories by normalized name for deduplication
  const categoryMap = new Map<string, any>();

  // Process all categories and deduplicate by normalized name
  categories.forEach(category => {
    // Make sure category is not null or undefined and has a name
    if (!category || !category.name) return;

    // Normalize the name for deduplication
    const normalizedName = category.name.toLowerCase().trim();

    // Ensure proper capitalization (capitalize first letter)
    const displayName = category.name.charAt(0).toUpperCase() +
                        category.name.slice(1).toLowerCase();

    // Get the category ID, with fallbacks
    const categoryId = category.id ||
      `category-${normalizedName.replace(/\s+/g, '-')}`;

    // Only add if we haven't seen this category before (case-insensitive)
    if (!categoryMap.has(normalizedName)) {
      categoryMap.set(normalizedName, {
        [idProperty]: categoryId,
        name: displayName, // Use properly capitalized name
        ...(includeCategory ? { category: displayName } : {})
      });
    }
  });

  // Add the deduplicated categories to the result
  result.push(...Array.from(categoryMap.values()));

  return result;
}
